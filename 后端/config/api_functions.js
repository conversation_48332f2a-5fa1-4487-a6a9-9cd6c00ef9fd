const axios = require('axios');

// 获取API基础URL
function getApiBaseUrl() {
    // 简化的API基础URL获取逻辑，避免复杂的配置验证
    if (process.env.MCD_USE_LOCAL_PROXY === 'true') {
        return process.env.MCD_LOCAL_PROXY_URL || 'http://mcd-api-server:9527';
    }
    return process.env.MCD_EXTERNAL_API_URL || 'http://server.pqkap.com';
}

module.exports = {
    // 查询用户钱包余额
    async getWalletBalance(sid) {
        try {
            const result = await axios.get(`${getApiBaseUrl()}/user/wallet/balance?debug_sid=${sid}`, {
                timeout: 5000 // 5秒超时
            });
            return result.data;
        } catch (error) {
            console.error('获取钱包余额失败:', error.message);
            // 返回默认格式，避免上层代码出错
            return {
                code: 500,
                msg: '余额服务暂时不可用',
                data: { balance: '0.00' }
            };
        }
    },
    
    // 获取用户信息
    async getUserInfos(sid) {
        try {
            const result = await axios.get(`${getApiBaseUrl()}/user/portal/info?debug_sid=${sid}`, {
                timeout: 5000 // 5秒超时
            });
            return result.data;
        } catch (error) {
            console.error('获取用户信息失败:', error.message);
            return {
                code: 500,
                msg: '用户信息服务暂时不可用',
                data: null
            };
        }
    }
};
