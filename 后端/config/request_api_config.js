const express = require("express");
const router = express.Router();
const config = require("../config/api_config")
const option = require("../config/config_config")
const requestEncapsulation = require("../config/sing_config")
const axios = require('axios')
const {errLog} = require("../utils/err");

// 引入统一配置
const unifiedConfig = require("./unified_config");

// 获取API基础URL
function getApiBaseUrl() {
    return unifiedConfig.getApiBaseUrl();
}

// 简单的内存缓存实现
class SimpleCache {
  constructor(maxSize = 100, ttl = 5 * 60 * 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  set(key, data) {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.ttl
    });
  }
  
  clear() {
    this.cache.clear();
  }
  
  delete(key) {
    this.cache.delete(key);
  }
}

// 创建缓存实例
const orderCache = new SimpleCache(100, 5 * 60 * 1000); // 100条缓存，5分钟过期
const orderDetailCache = new SimpleCache(50, 10 * 60 * 1000); // 50条缓存，10分钟过期


module.exports = {
    //获取订单列表
    async getOrders(sid, orderCategoryId, page, size) {
        const result = await axios.get(`${getApiBaseUrl()}/order/orders`, {
            params: {
                debug_sid: sid,
                orderCategoryId: orderCategoryId,
                page: page,
                size: size
            },
            timeout: 8000 // 8秒超时
        });
        return result.data;
    },
    //获取麦当劳账号订单列表（从Python后端API获取）
    async getMcdOrders(sid, orderCategoryId, page, size, filters = {}) {
        // 构建请求参数，设置默认值
        const params = {
            debug_sid: sid,
            orderCategoryId: orderCategoryId || 0, // 订单分类ID，默认为0
            page: page || 1, // 页码，默认第1页
            size: size || 10, // 每页数量，默认10条
            ...filters // 添加筛选条件支持
        };

        // 生成缓存键
        const cacheKey = `orders_${JSON.stringify(params)}`;
        
        // 检查缓存
        const cachedResult = orderCache.get(cacheKey);
        if (cachedResult) {
            errLog({
                err: null,
                code: 200,
                msg: `[订单列表API] 缓存命中: ${cacheKey}`,
                funName: "info"
            });
            return cachedResult;
        }

        try {
            // 根据修正后的API格式发送GET请求
            const queryString = Object.entries(params)
                .filter(([key, value]) => value !== '' && value !== null && value !== undefined)
                .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
                .join('&');
                
            const result = await axios.get(`${getApiBaseUrl()}/order/orders?${queryString}`, {
                timeout: 10000, // 10秒超时
            });

            // 缓存结果
            if (result.data && result.data.code === 200) {
                orderCache.set(cacheKey, result.data);
            }

            // 记录成功响应日志
            errLog({
                err: null,
                code: 200,
                msg: `[订单列表API] 请求成功: ${JSON.stringify(result.data)}`,
                funName: "info"
            });
            
            // 转换数据格式
            const response = result.data;
            const transformedList = [];
            
            // 检查是否有订单数据
            if (response && response.success && response.data && Array.isArray(response.data.list)) {
                // 遍历订单列表并转换格式
                for (const order of response.data.list) {
                    try {
                        // 获取订单详情以补充缺失字段
                        let orderDetail = null;
                        try {
                            // 尝试获取订单详情
                            const detailResult = await this.getOrderDetail(order.orderId, sid);
                            if (detailResult && detailResult.data && detailResult.data.success) {
                                orderDetail = detailResult.data.data;
                            }
                        } catch (detailError) {
                            console.error('获取订单详情失败:', detailError);
                            // 获取详情失败不影响主流程
                        }
                        
                        // 合并订单详情数据
                        const mergedOrder = orderDetail ? { ...order, ...orderDetail } : order;
                        
                        // 提取商品名称
                        let mealTitle = '未知商品';
                        if (mergedOrder.orderProductList && Array.isArray(mergedOrder.orderProductList) && mergedOrder.orderProductList.length > 0) {
                            // 获取主商品名称
                            const productNames = [];
                            
                            // 遍历所有产品
                            for (const product of mergedOrder.orderProductList) {
                                let productInfo = product.productName || '';
                                
                                // 如果有组合商品，添加组合商品详情
                                if (product.comboItemList && Array.isArray(product.comboItemList) && product.comboItemList.length > 0) {
                                    const comboDetails = product.comboItemList
                                        .filter(combo => combo && combo.name)
                                        .map(combo => combo.name)
                                        .join(', ');
                                    
                                    if (comboDetails && productInfo) {
                                        productInfo += ` (${comboDetails})`;
                                    }
                                }
                                
                                if (productInfo) {
                                    productNames.push(productInfo);
                                }
                            }
                            
                            // 合并所有产品名称
                            if (productNames.length > 0) {
                                mealTitle = productNames.join(' + ');
                            }
                        }
                        
                        // 格式化价格
                        let priceNum = '0.00';
                        // 优先使用realTotalAmount，如果不存在则尝试使用realSubtotal或totalAmount
                        const amount = mergedOrder.realTotalAmount || mergedOrder.realSubtotal || mergedOrder.totalAmount;
                        if (amount) {
                            // 确保价格格式正确
                            priceNum = parseFloat(amount).toFixed(2);
                        }
                        
                        // 转换支付方式
                        let payText = 'balance';
                        if (mergedOrder.paymentMethod) {
                            // 根据实际支付方式映射
                            switch(mergedOrder.paymentMethod.toUpperCase()) {
                                case 'ALIPAY':
                                    payText = 'alipay';
                                    break;
                                case 'WECHAT':
                                case 'WECHATPAY':
                                    payText = 'wechat';
                                    break;
                                case 'BALANCE':
                                case 'ARCHCARD':
                                    payText = 'balance';
                                    break;
                                default:
                                    payText = mergedOrder.paymentMethod;
                            }
                        }
                        
                        // 映射订单状态
                        const mapOrderStatus = (mcdStatus) => {
                            // 处理null或undefined
                            if (mcdStatus === null || mcdStatus === undefined) {
                                return 1; // 默认为已支付状态
                            }
                            
                            // 处理数字状态码 - 支持数字和字符串形式的数字
                            if (typeof mcdStatus === 'number' || (typeof mcdStatus === 'string' && !isNaN(mcdStatus))) {
                                const statusCode = parseInt(mcdStatus);
                                switch(statusCode) {
                                    case 1:  // 已支付
                                    case 2:  // 支付确认
                                        return 1;
                                    case 3:  // 准备中
                                    case 4:  // 准备完成
                                    case 5:  // 待取餐
                                        return 2;
                                    case 6:  // 已完成
                                        return 3;
                                    case 7:  // 已取消
                                    case 8:  // 已退款
                                        return 4;
                                    default:
                                        return 1; // 默认为已支付状态
                                }
                            }
                            
                            // 处理文本状态
                            switch(mcdStatus) {
                                case 'PAID':           // 已支付
                                case 'PAYMENT_CONFIRMED':
                                    return 1;
                                case 'PREPARING':      // 准备中/已出餐
                                case 'READY_FOR_PICKUP':
                                    return 2;
                                case 'COMPLETED':      // 已完成/已取餐
                                case 'PICKED_UP':
                                    return 3;
                                case 'CANCELLED':      // 已取消/已退款
                                case 'REFUNDED':
                                    return 4;
                                default:
                                    return 1; // 默认为已支付状态
                            }
                        };
                        
                        // 添加转换后的订单
                        transformedList.push({
                            ORDER_ID: mergedOrder.orderId || '',                    // 订单号
                            MEAL_TITLE: mealTitle,                                 // 商品名称
                            STORE_NAME: mergedOrder.storeName || '',               // 门店名称
                            PRICE_NUM: priceNum,                                   // 订单金额
                            PAY_TEXT: payText,                                     // 支付方式
                            ORDER_STATUS: mapOrderStatus(mergedOrder.orderStatusCode), // 订单状态转换
                            PICKUP_CODE: mergedOrder.pickupCode || mergedOrder.mpOrderStatusCode || '-',  // 取餐码，如果没有则使用订单状态码
                            CREATED_TIME: mergedOrder.createTime || new Date().toISOString().replace('T', ' ').substring(0, 19),    // 创建时间，格式化为YYYY-MM-DD HH:MM:SS
                            // 保留旧字段以兼容现有代码
                            ODER_ID: mergedOrder.orderId || '',                     // 订单号 (保留旧字段以兼容)
                            ODER_STATUS: mapOrderStatus(mergedOrder.orderStatusCode),  // 订单状态转换 (保留旧字段以兼容)
                            CREATRD_TIME: mergedOrder.createTime || new Date().toISOString().replace('T', ' ').substring(0, 19)      // 创建时间，格式化为YYYY-MM-DD HH:MM:SS (保留旧字段以兼容)
                        });
                    } catch (err) {
                        console.error('订单数据转换错误:', err, '原始订单数据:', JSON.stringify(order));
                        // 即使转换出错，也添加一个基本的订单记录
                        transformedList.push({
                            ORDER_ID: order.orderId || '未知订单',
                            MEAL_TITLE: '数据解析错误',
                            STORE_NAME: '',
                            PRICE_NUM: '0.00',
                            PAY_TEXT: 'balance',
                            ORDER_STATUS: 1,
                            PICKUP_CODE: '-',
                            CREATED_TIME: new Date().toISOString().replace('T', ' ').substring(0, 19),
                            // 保留旧字段以兼容现有代码
                            ODER_ID: order.orderId || '未知订单',
                            ODER_STATUS: 1,
                            CREATRD_TIME: new Date().toISOString().replace('T', ' ').substring(0, 19)
                        });
                    }
                }
            }
            
            // 记录转换后的数据
            console.log(`成功转换 ${transformedList.length} 条订单数据`);
            
            // 返回转换后的数据
            return {
                success: response.success,
                message: response.message,
                data: {
                    list: transformedList,
                    pages: response.data?.pages || 0,
                    total: response.data?.total || 0
                }
            };
        } catch (error) {
            // 记录错误日志
            errLog({
                err: error,
                code: error.response?.status || 500,
                msg: `[订单列表API] 请求失败: ${error.message}`,
                funName: "error"
            });
            throw error;
        }
    },
    
    // 批量获取订单详情
    async getBatchOrderDetails(sid, orderIds) {
        if (!Array.isArray(orderIds) || orderIds.length === 0) {
            throw new Error('订单ID列表不能为空');
        }
        
        const results = [];
        const uncachedIds = [];
        
        // 检查缓存
        for (const orderId of orderIds) {
            const cacheKey = `order_detail_${sid}_${orderId}`;
            const cachedDetail = orderDetailCache.get(cacheKey);
            if (cachedDetail) {
                results.push(cachedDetail);
            } else {
                uncachedIds.push(orderId);
            }
        }
        
        // 批量获取未缓存的订单详情
        if (uncachedIds.length > 0) {
            try {
                const batchPromises = uncachedIds.map(orderId => 
                     this.getOrderDetail(orderId, sid)
                 );
                
                const batchResults = await Promise.allSettled(batchPromises);
                
                batchResults.forEach((result, index) => {
                    if (result.status === 'fulfilled') {
                        const orderId = uncachedIds[index];
                        const cacheKey = `order_detail_${sid}_${orderId}`;
                        orderDetailCache.set(cacheKey, result.value);
                        results.push(result.value);
                    } else {
                        errLog({
                            err: result.reason,
                            code: 500,
                            msg: `[批量订单详情API] 获取订单详情失败: ${uncachedIds[index]}`,
                            funName: "error"
                        });
                    }
                });
            } catch (error) {
                errLog({
                    err: error,
                    code: 500,
                    msg: `[批量订单详情API] 批量请求失败: ${error.message}`,
                    funName: "error"
                });
                throw error;
            }
        }
        
        return results;
    },
    
    //取消订单
    async cancelOder(orderId, sid) {
        const result = await axios.put(`${getApiBaseUrl()}/order/orders/${orderId}/cancellation`, {
            "cancelReasonCode": "3",
            "debug_sid": sid
        })
        return result
    },
    //优惠券列表加入购物车
    async couponListAddCart(storeCode,couponCode,couponId,promotionId,sid){
        const url = `${getApiBaseUrl()}/cart/cartscoupon?storeCode=${storeCode}&subProductCode=&couponCode=${couponCode}&couponId=${couponId}&promotionId=${promotionId}&cartType=1&channelCode=03&beType=1&beCode=&orderType=1&dayPartCode=8&quantity=1&membershipCode=&productType=&cardType=0&cardId=&debug_sid=${sid}`;
        
        // 记录详细的请求日志
        errLog({err: null, code: 200, msg: `[优惠券购物车API] 发送请求到: ${url}`, funName: "info"});
        errLog({err: null, code: 200, msg: `[优惠券购物车API] 请求方法: GET`, funName: "info"});
        errLog({err: null, code: 200, msg: `[优惠券购物车API] 请求参数: ${JSON.stringify({
            storeCode,
            couponCode,
            couponId,
            promotionId,
            sid
        })}`, funName: "info"});
        
        try {
            const result = await axios.get(url, {
                timeout: 8000 // 8秒超时
            });
            
            // 记录成功响应日志
            errLog({err: null, code: 200, msg: `[优惠券购物车API] 请求成功，状态码: ${result.status}`, funName: "info"});
            errLog({err: null, code: 200, msg: `[优惠券购物车API] 响应头信息: ${JSON.stringify(result.headers)}`, funName: "info"});
            errLog({err: null, code: 200, msg: `[优惠券购物车API] 响应数据: ${JSON.stringify(result.data, null, 2)}`, funName: "info"});
            
            return result.data;
        } catch (error) {
            // 记录详细的错误日志
            console.error(`[优惠券购物车API] 请求失败:`);
            console.error(`[优惠券购物车API] 错误类型: ${error.name}`);
            console.error(`[优惠券购物车API] 错误消息: ${error.message}`);
            
            if (error.response) {
                // 服务器响应了错误状态码
                console.error(`[优惠券购物车API] 响应状态码: ${error.response.status}`);
                console.error(`[优惠券购物车API] 响应状态文本: ${error.response.statusText}`);
                console.error(`[优惠券购物车API] 响应头信息:`, error.response.headers);
                console.error(`[优惠券购物车API] 错误响应数据:`, JSON.stringify(error.response.data, null, 2));
            } else if (error.request) {
                // 请求已发出但没有收到响应
                console.error(`[优惠券购物车API] 网络错误，未收到响应`);
                console.error(`[优惠券购物车API] 请求配置:`, error.config);
            } else {
                // 其他错误
                console.error(`[优惠券购物车API] 请求配置错误:`, error.config);
            }
            
            // 重新抛出错误，保持原有的错误处理逻辑
            throw error;
        }
    },
    //服务通知
    async getServerNotification(page,sid){
        return requestEncapsulation.get_ayncRequest(`https://api.mcd.cn/mc/api/msg/box/list?category=1&page=${page}&size=10`,sid)
    },
    //**********
    //******
    //***
    async getZaoCaoCanList(date,storeCode,time){//date:2024-09-15 time:07:00
        return requestEncapsulation.get_ayncRequest(`https://api.mcd.cn/bff/spc/menu?date=${date}&dayPartCode=1&isPromoter=0&orderType=1&storeCode=${storeCode}&time=${time}`)
    },
    //***
    //******
    //********
    //获取券详情
    async getCouponXq(code,sid,id){
        const result = await axios.get(`${getApiBaseUrl()}/promotion/coupons/${code}?debug_sid=${sid}&couponId=${id}`, {
            timeout: 8000 // 8秒超时
        })
        return result.data
    },
    //获取用户信息
    async getUserInfos(sid){
         const result = await axios.get(`${getApiBaseUrl()}/user/portal/info?debug_sid=${sid}`, {
            timeout: 5000 // 5秒超时
        })
        return result.data
    },
    // 查询用户钱包余额（新增）
    async getWalletBalance(sid){
        const result = await axios.get(`${getApiBaseUrl()}/user/wallet/balance?debug_sid=${sid}`)
        return result.data
    },
    //根据城市id获取门店
    async storeIdGetShop(cityId,keyword,pageNo,pageSize){
        const result = await axios.get(`${getApiBaseUrl()}/store/stores?cityCode=${cityId}&keyword=${keyword}&pageNo=${pageNo}&pageSize=${pageSize}`)
        return result.data
    },
    //获取城市
    async citiesGroup() {
        return requestEncapsulation.get_ayncRequest(config.apiUrlObj.AcquisitionCity)
    },
    //获取用户券信息
    async userCoupon(channelCode, scene, storeCode, sid) {
        const result = await axios.get(`${getApiBaseUrl()}/promotion/coupons/v2?debug_sid=${sid}`, {
            timeout: 5000 // 5秒超时
        })
        return result.data
    },
    //获取用户券信息
    async getMdlUserInfo(sid) {
        return requestEncapsulation.get_ayncRequest(config.apiUrlObj.getMdlUserInfo, sid, config.apiUrlObj.getMdlUserInfo)
    },
    //通过优惠券信息获取有券商品
    async findCouponFood(couponCode, couponId, promotionId, storeCode, sid) {

        return requestEncapsulation.get_ayncRequest(config.apiUrlObj.findCouponFood + `?channelCode=03&couponCode=${couponCode}&couponId=${couponId}&daypartCodes=4&orderType=1&promotionId=${promotionId}&storeCode=${storeCode}`, sid, config.apiUrlObj.findCouponFood + `?channelCode=03&couponCode=${couponCode}&couponId=${couponId}&daypartCodes=4&orderType=1&promotionId=${promotionId}&storeCode=${storeCode}`)
    },
    //备用 用券查商品信息
    async bfindCouponFood(couponCode, couponId, promotionId, storeCode, sid) {
        // const resultApi = ayncRequest("GET", `http://server.pqkap.com/promotion/coupons/products?debug_sid=${sid}&couponCode=${couponCode}&promotionId=${promotionId}&couponId=${couponId}&storeCode=${storeCode}`);
        // return JSON.parse(resultApi.getBody('utf8'));
        const result = await axios.get(`${getApiBaseUrl()}/promotion/coupons/products?debug_sid=${sid}&couponCode=${couponCode}&promotionId=${promotionId}&couponId=${couponId}&storeCode=${storeCode}`)
        return result.data
    },
    //获取门店菜单
    async getSpcMenu(orderType = 1, storeCode, dayPartCode = 5) {
        let result = await requestEncapsulation.get_ayncRequest(config.apiUrlObj.GetTheMenuAccordingToTheStore + '?dayPartCode=' + dayPartCode + '&orderType=' + orderType + '&storeCode=' + storeCode)
        return result
    },
    //查询现金券
    async findCash(code) {
        // const resultApi = ayncRequest("GET", `http://server.pqkap.com/promotion/coupons/products?debug_sid=${sid}&couponCode=${couponCode}&promotionId=${promotionId}&couponId=${couponId}&storeCode=${storeCode}`);
        // return JSON.parse(resultApi.getBody('utf8'));
        const result = await axios.get(`${getApiBaseUrl()}/coupon/exchanges/${code}?debug_sid=${await option.sid()}`)
        return result.data
    },
    //查询权益
    async findVip(code, sid) {
        const result = await axios.get(`${getApiBaseUrl()}/myrewards/benefit/use/detail/${code}?debug_sid=${sid}`)
        return result.data

    },
    //根据经纬度获取附近门店
    async GetNearbyStoresBasedOnCoordinatesMy(latitude, longitude, orderType = 1, showType = 2, pageNo = 1, pageSize = 10, sid) {
        let result =  await axios.get(`${getApiBaseUrl()}/store/stores/vicinity?lat=${latitude}&lng=${longitude}&showType=${showType}&beType=1&orderType=${orderType}`)
        return result.data
    },
    //根据城市code获取门店
    async CityAcquisitionStoreMy(cityCode, pageNo, pageSize) {
        let result = await axios.get(`${getApiBaseUrl()}/store/storesGetCityByLocation?cityCode=${cityCode}&pageNo=${pageNo}&pageSize=${pageSize}`)
        return result.data
    },
    //根据经纬度获取城市
    async GetLTCity(latitude, longitude) {
        ///store/cities
        let result =  await axios.get(`${getApiBaseUrl()}/store/cities?lat=${latitude}&lng=${longitude}`)
        return result.data
    },
    //根据门店信息获取菜单
    async StoreCodeGetRestaurantDetailsMy(dayPartCode, orderType, storeCode) {
        let result = await requestEncapsulation.get_ayncRequest(config.apiUrlObj.GetTheMenuAccordingToTheStore + '?dayPartCode=' + dayPartCode + '&orderType=' + orderType + '&storeCode=' + storeCode)
        return result
    },
    //获取商品详情
    async commodityCetailsMy(storeCode, productCode) {
        const result = await axios.get(`${getApiBaseUrl()}/spc/products/detail/${productCode}?storeCode=${storeCode}&productCode=${productCode}&channelCode=03&orderType=1`)
        return result
    },
    //清理购物车
    async clearCartSid(storeCode, sid) {
        const result = await axios.put(`${getApiBaseUrl()}/cart/carts/empty`, {
            "storeCode": storeCode,
            "cartType": 1,
            "channelCode": '03',
            "beType": "1",
            "beCode": "",
            "orderType": "1",
            "debug_sid": sid
        })
        return result
    },
    
    //加入购物车
    async addCartSid(storeCode, sid, productCode, couponCode, couponId, promotionId, customization, comboItems, membershipCode, productType, cardType, cardId, productInfo = {}) {
       
        // 构建请求数据，补充抓包数据中的必要字段
        const requestData = {
            "storeCode": storeCode,
            "productCode": productCode,
            "subProductCode": "",
            "couponCode": couponCode,
            "couponId": couponId,
            "promotionId": promotionId,
            "cartType": 1,
            "channelCode": "03",
            "beType": "1",
            "beCode": "",
            "orderType": "1",
            "quantity": 1,
            "debug_sid": sid,
            "customization": customization,
            "comboItems": comboItems,
            "membershipCode": membershipCode,
            "productType": productType,
            "cardType": cardType,
            "cardId": cardId,
            // 根据抓包数据补充的字段
            "isChoices": productInfo.isChoices || false,
            "choicesCode": productInfo.choicesCode || "",
            "name": productInfo.name || "",
            "price": productInfo.price || 0,
            "dataSource": "1" // 统一数据源标识
        }

        // 处理customization数据结构，确保与抓包数据一致
        if (customization && Array.isArray(customization)) {
            requestData.customization = customization.map(item => {
                const processedItem = { ...item };
                
                // 确保必要字段存在
                if (!processedItem.hasOwnProperty('isChoices')) {
                    processedItem.isChoices = false;
                }
                if (!processedItem.hasOwnProperty('choicesCode')) {
                    processedItem.choicesCode = "";
                }
                if (!processedItem.hasOwnProperty('dataSource')) {
                    processedItem.dataSource = "1";
                }
                
                // 处理options或items结构
                if (processedItem.options && Array.isArray(processedItem.options)) {
                    processedItem.options = processedItem.options.map(option => ({
                        ...option,
                        isChoices: option.isChoices || false,
                        choicesCode: option.choicesCode || "",
                        dataSource: option.dataSource || "1"
                    }));
                }
                
                if (processedItem.items && Array.isArray(processedItem.items)) {
                    processedItem.items = processedItem.items.map(subItem => ({
                        ...subItem,
                        isChoices: subItem.isChoices || false,
                        choicesCode: subItem.choicesCode || "",
                        dataSource: subItem.dataSource || "1"
                    }));
                }
                
                return processedItem;
            });
        }

        // 处理comboItems数据结构，确保与抓包数据一致
        if (comboItems && Array.isArray(comboItems)) {
            requestData.comboItems = comboItems.map(item => {
                const processedItem = { ...item };
                
                // 确保必要字段存在
                if (!processedItem.hasOwnProperty('isChoices')) {
                    processedItem.isChoices = false;
                }
                if (!processedItem.hasOwnProperty('choicesCode')) {
                    processedItem.choicesCode = "";
                }
                if (!processedItem.hasOwnProperty('dataSource')) {
                    processedItem.dataSource = "1";
                }
                if (!processedItem.hasOwnProperty('name')) {
                    processedItem.name = "";
                }
                if (!processedItem.hasOwnProperty('price')) {
                    processedItem.price = 0;
                }
                
                // 处理comboProducts中的customization
                if (processedItem.comboProducts && Array.isArray(processedItem.comboProducts)) {
                    processedItem.comboProducts = processedItem.comboProducts.map(product => {
                        const processedProduct = { ...product };
                        
                        if (processedProduct.customization && Array.isArray(processedProduct.customization)) {
                            processedProduct.customization = processedProduct.customization.map(custItem => ({
                                ...custItem,
                                isChoices: custItem.isChoices || false,
                                choicesCode: custItem.choicesCode || "",
                                dataSource: custItem.dataSource || "1"
                            }));
                        }
                        
                        return processedProduct;
                    });
                }
                
                return processedItem;
            });
        }

        // 记录详细的请求日志
        errLog({err: null, code: 200, msg: `[购物车API] 发送请求到: ${getApiBaseUrl()}/cart/carts`, funName: "info"});
        errLog({err: null, code: 200, msg: `[购物车API] 请求方法: PUT`, funName: "info"});
        errLog({err: null, code: 200, msg: `[购物车API] 请求头信息: ${JSON.stringify({
            'Content-Type': 'application/json',
            'User-Agent': 'axios/' + require('axios/package.json').version
        })}`, funName: "info"});
        errLog({err: null, code: 200, msg: `[购物车API] 完整请求数据: ${JSON.stringify(requestData, null, 2)}`, funName: "info"});
        
        try {
            const result = await axios.put(`${getApiBaseUrl()}/cart/carts`, requestData, {
                timeout: 10000, // 10秒超时
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            // 记录成功响应日志
            errLog({err: null, code: 200, msg: `[购物车API] 请求成功，状态码: ${result.status}`, funName: "info"});
            errLog({err: null, code: 200, msg: `[购物车API] 响应头信息: ${JSON.stringify(result.headers)}`, funName: "info"});
            errLog({err: null, code: 200, msg: `[购物车API] 响应数据: ${JSON.stringify(result.data, null, 2)}`, funName: "info"});
            
            return result;
        } catch (error) {
            // 记录详细的错误日志
            console.error(`[购物车API] 请求失败:`);
            console.error(`[购物车API] 错误类型: ${error.name}`);
            console.error(`[购物车API] 错误消息: ${error.message}`);
            
            if (error.response) {
                // 服务器响应了错误状态码
                console.error(`[购物车API] 响应状态码: ${error.response.status}`);
                console.error(`[购物车API] 响应状态文本: ${error.response.statusText}`);
                console.error(`[购物车API] 响应头信息:`, error.response.headers);
                console.error(`[购物车API] 错误响应数据:`, JSON.stringify(error.response.data, null, 2));
            } else if (error.request) {
                // 请求已发出但没有收到响应
                console.error(`[购物车API] 网络错误，未收到响应`);
                console.error(`[购物车API] 请求配置:`, error.config);
            } else {
                // 其他错误
                console.error(`[购物车API] 请求配置错误:`, error.config);
            }
            
            // 重新抛出错误，保持原有的错误处理逻辑
            throw error;
        }
    },
    //获取购物车信息
    async getCartInfo(storeCode, sid) {
        const result = await axios.post(`${getApiBaseUrl()}/order/confirmation/validationinfo`, {
            "storeCode": storeCode,
            "orderType": "1",
            "cartType": "1",
            "channelCode": "03",
            "beCode": "",
            "addressId": "",
            "debug_sid": sid
        })
        return result
    },
    //提交订单
    async makeOrderDirectly(sid, cardId, cartItems, realTotalAmount, storeCode, eatType, cashCouponCode, cashCouponId, payByBalance, proxyId, promotionList) {
        const result = await axios.post(`${getApiBaseUrl()}/order/orders2`, {
            "cartItems": cartItems,
            "realTotalAmount": realTotalAmount,
            "storeCode": storeCode,
            "orderType": '1',
            "cartType": '1',
            "channelCode": '03',
            "beCode": "",
            "addressId": "",
            "realDeliveryPrice": "",
            "deliveryTime": "",
            "eatType": eatType,
            "cashCouponCode": cashCouponCode,
            "cashCouponId": cashCouponId,
            "payByBalance": payByBalance,
            "proxyId": proxyId,
            "promotionList": promotionList,
            "cardId": cardId,
            "debug_sid": sid
        })
        return result
    },
    //查询用户券信息
    async getUserCouponInfo(storeCode, sid) {
        const result = await axios.get(`${getApiBaseUrl()}/promotion/coupons/v2?debug_sid=${sid}`)
        return result.data
    },
    //充值现金券
    async doExchangeCoupon(sid, exchangeCouponCode) {
        const result = await axios.post(`${getApiBaseUrl()}/coupon/exchanges`, {
            "debug_sid": sid,
            "couponCode": exchangeCouponCode
        })
        return result
    },
    //选择支付方式
    async preOrder(payId, sid) {
        const result = await axios.post(`${getApiBaseUrl()}/cashier/preorder`, {
            "payChannel": "ALI",
            "payId": payId,
            "debug_sid": sid
        })
        return result
    },
    //余额支付
    async payByBallance(payId, sid) {
        const result = await axios.post(`${getApiBaseUrl()}/cashier/archcard`, {
            "debug_sid": sid,
            "payId": payId
        })
        return result
    },
    //获取订单详情
    async getOrderDetail(oderId, sid) {
        const result = await axios.get(`${getApiBaseUrl()}/order/orders/${oderId}?debug_sid=${sid}`)
        return result
    },
    //获取门店菜单
    async getStoreMe(storeCode, orderType) {
        const result = await axios.get(`${getApiBaseUrl()}/spc/menu?storeCode=${storeCode}&orderType=${orderType}&beType=1`)
        return result
    },
    //获取用户早餐卡信息
    async findUserRight(sid) {
        const result = await axios.get(`${getApiBaseUrl()}/promotion/coupons/rightCards?debug_sid=${sid}`)
        return result
    },
    //二次提交订单
    async return_order(sid,orderId,payId,proxyId) {
        const result = await axios.post(`${getApiBaseUrl()}/return_order`, {
            "sid": sid,
            "orderId": orderId,
	        "payId": payId,
	        "proxyId": proxyId
        })
        return result
    },
    //获取早餐卡详情
    async getRightCardDetail(sid, carID, card_no) {
        const result = await axios.get(`${getApiBaseUrl()}/promotion/coupons/rightCard/detail/v2?debug_sid=${sid}&card_type=2&card_id=${carID}&card_no=${card_no}`)
        return result
    },
    //获取支付信息
    async getPayInfo(sid, orderId) {
        let parmas = {
            "freePayFlg": false,
            "payChannel": "ALI",
            "payId": "11717736770415149056",
            "source": 0
        }
        let result = await requestEncapsulation.post_ayncRequest(parmas, 'c7ac68ac3173e54df52e4d0d3d9e1aa6_', 'https://api.mcd.cn/bff/cashier/preorder', 'POST', 'https://api.mcd.cn/bff/cashier/preorder')
        return result
    },
}
router.get("/cities/group", async (req, res) => {
    let result = await requestEncapsulation.get_ayncRequest(config.apiUrlObj.AcquisitionCity)
    res.json(result)
})
//根据定位获取城市
router.get("/store/cities", async (req, res) => {
    let { latitude, longitude } = req.query
    if (!latitude || !longitude) {
        res.json({ code: 500, messge: "参数错误" })
        return
    }
    let result = await requestEncapsulation.get_ayncRequest(config.apiUrlObj.GetCitiesBasedOnLocation + '?latitude=' + latitude + '&longitude=' + longitude)
    res.json(result)
})


//根据定位获取城市
router.get("/store/stores", async (req, res) => {

})
//根据门店获取菜单
router.get("/spc/menu", async (req, res) => {

})
//根据坐标获取附近门店
router.get("/store/stores/vicinity", async (req, res) => {
    let { latitude, longitude, orderType, showType } = req.query
    if (!latitude || !longitude || !orderType || !showType) {
        res.json({ code: 500, messge: "参数错误" })
        return
    }
    let result = await requestEncapsulation.get_ayncRequest(config.apiUrlObj.GetNearbyStoresBasedOnCoordinates + '?latitude=' + latitude + '&longitude=' + longitude + '&orderType=' + orderType + '&showType=' + showType)
    res.json(result)
})
//门店代码获取餐厅详情
router.get("/store/storesCode", async (req, res) => {
    let { code } = req.query
    if (!code) {
        res.json({ code: 500, messge: "参数错误" })
        return
    }
    let result = await requestEncapsulation.get_ayncRequest(config.apiUrlObj.StoreCodeGetRestaurantDetails + '/' + code, '', config.apiUrlObj.StoreCodeGetRestaurantDetails + '/' + '{' + code + '}')
    res.json(result)
    // res.json({ code: 500, target: config.apiUrlObj.StoreCodeGetRestaurantDetails + '/' + code, message: "接口不通畅" })
})
//商品详情接口
router.post("/store/goods/detail", async (req, res) => {
    let { cardId, beCode, pageSource, productPromotions, storeCode, productCode, orderType, daypartCode } = req.body
    let body_data = { "cardId": cardId, "beCode": beCode, "pageSource": parseInt(pageSource), "productPromotions": productPromotions, "storeCode": storeCode, "productCode": productCode, "orderType": orderType, "daypartCode": daypartCode }
    let result = await requestEncapsulation.post_ayncRequest(body_data, '', config.apiUrlObj.commodityCetails + '{' + productCode + '}', "POST", config.apiUrlObj.commodityCetails + productCode)
    res.json(result)
})
//获取用户优惠券的列表信息
router.get('/user/couponList', async (req, res) => {

})
