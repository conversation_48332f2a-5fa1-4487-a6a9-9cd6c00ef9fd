const express = require("express");
const router = express.Router();
const pool = require("../pool.js");
const utils = require("../utils/index.js");
const jwt = require("jsonwebtoken"); // JWT token生成
const { systemSettings } = require("../utils/menuString");
const { findCouponFoodMethod, getMdlUserInfoMethod, qxFind,findCouponFoodk } = require("../utils/findCouponFood.js");
const child_process = require('child_process');
const { getSpcMenu, getStoreMe,return_order,getUserInfos } = require("../config/request_api_config.js");
const cpuMuns = require('os').cpus().length;
const path = require('path');
const { json } = require("body-parser");
const {prefix} = require("../config/config_config.js")



//获取明细
router.post("/getBalance",async(req,res)=>{
    let user = await utils.getUserRole(req, res);
    let userId = user.user.id
    let {q,l} = req.body
    let sql = `SELECT * FROM particulars WHERE proxyId = ${userId} ORDER BY createdTime DESC LIMIT ${q-1}, ${l};`
    console.log(sql)
    let sql1 = 'SELECT COUNT(*) FROM particulars WHERE proxyId = '+userId
    let cont = await utils.dbUse(sql1,[])
    let result =  await utils.dbUse(sql,[])
    res.json({
        code:200,
        data:result,
        cont
    })
})

//修改方案
router.post("/bianjitaocan",async(req,res)=>{
    let obj = req.body.obj
    let sql = `UPDATE mdl_combo_meal SET TAB = ?,MY_KA_USE = ?,MYKA_BREAKFAST_USE = ?,COFFEE_USE = ?, BENEFITS_USE = ?,FREE_DELIVERY_USE = ? ,XINPINCHANG_USE = ?, CASHCOUPON = ?,APPOINT= ? , IMAGE = ?,PAYPRICELIMIT = ? , TITLE =?,BALANCE_PAYMENT = ? ,BANK_CARD_BREAKFAST = ? ,BANK_CARD_VIP = ?, PLAN = ?, ACCOUNT =?,POSITIVE=?,TICKET= ?,START_TIME= ?,END_TIME = ?,CREATED_TIME = ?,PRICE =?,BREAKFAST_CARD_USE = ? ,CIRCULATE =? WHERE ID = ?`
    await utils.dbUse(sql,[obj.TAB,obj.MY_KA_USE,obj.MYKA_BREAKFAST_USE,obj.COFFEE_USE,obj.BENEFITS_USE,obj.FREE_DELIVERY_USE,obj.XINPINCHANG_USE,obj.CASHCOUPON,obj.APPOINT,obj.IMAGE,obj.PAYPRICELIMIT,obj.TITLE,obj.BALANCE_PAYMENT ? 1 : 0,obj.BANK_CARD_BREAKFAST,obj.BANK_CARD_VIP,obj.PLAN,obj.ACCOUNT,JSON.stringify(obj.POSITIVE),JSON.stringify(obj.TICKET),obj.START_TIME,obj.END_TIME,obj.CREATED_TIME,obj.PRICE,obj.BREAKFAST_CARD_USE,obj.CIRCULATE,obj.ID])
    res.json({
        code:200,
        message:"修改成功"
    })
})

//sid增加备注
router.post("/updataMakeData",async(req,res)=>{
    let {id,make} = req.body
    let sql = 'UPDATE mdl_sid SET remark = ? WHERE ID = ?'
    await utils.dbUse(sql,[make,id])
    res.json({
        code:200,
        message:"成功"
    })
})
//修改sid信息
router.post("/updataSid",async(req,res)=>{
    const startTime = Date.now();
    try {
        const { ID, PHONE, MEDDYID, AVAILABLE_POINTS, PARENT_MENU, LOGIN_CODE, START, ENABLED, remark } = req.body;
        
        // 如果没有ID，则是批量重置操作（保持原有功能）
        if (!ID) {
            let sql = 'UPDATE `mdl_sid` SET START = 0 WHERE START = 1'
            await utils.dbUse(sql)
            return res.json({
                code:200,
                message:"批量重置成功"
            })
        }
        
        // 单个账号编辑操作
        let updateFields = [];
        let updateValues = [];
        
        if (PHONE !== undefined) {
            updateFields.push('PHONE = ?');
            updateValues.push(PHONE);
        }
        if (MEDDYID !== undefined) {
            updateFields.push('MEDDYID = ?');
            updateValues.push(MEDDYID);
        }
        if (AVAILABLE_POINTS !== undefined) {
            updateFields.push('AVAILABLE_POINTS = ?');
            updateValues.push(AVAILABLE_POINTS);
        }
        if (PARENT_MENU !== undefined) {
            updateFields.push('PARENT_MENU = ?');
            updateValues.push(PARENT_MENU);
        }
        if (LOGIN_CODE !== undefined) {
            updateFields.push('LOGIN_CODE = ?');
            updateValues.push(LOGIN_CODE);
        }
        if (START !== undefined) {
            updateFields.push('START = ?');
            updateValues.push(START);
        }
        if (ENABLED !== undefined) {
            updateFields.push('ENABLED = ?');
            updateValues.push(ENABLED);
        }
        if (remark !== undefined) {
            updateFields.push('remark = ?');
            updateValues.push(remark);
        }
        
        if (updateFields.length === 0) {
            return res.json({
                code: 400,
                message: "没有需要更新的字段"
            });
        }
        
        // 添加WHERE条件的ID
        updateValues.push(ID);
        
        let sql = `UPDATE mdl_sid SET ${updateFields.join(', ')} WHERE ID = ?`;
        await utils.dbUse(sql, updateValues);
        
        res.json({
            code: 200,
            message: "修改成功"
        });
    } catch (error) {
        const duration = Date.now() - startTime;
        console.error('updataSid处理错误:', error);
        
        res.status(500).json({
            code: 500,
            msg: "服务器内部错误", 
            error: error.message,
            performance: { duration }
        });
    }
});

//更新所有用户积分
router.post("/updateFens",async(req,res)=>{
    upsid()
    res.json({
        code:200,
        message:"成功"
    })
})
async function upsid(){
    let sql = `SELECT * FROM mdl_sid`
    let user = await utils.dbUse(sql)
    for(let i = 0 ; i <user.length;i++){
        let result = await getUserInfos(user[i].SID)
        if(result.code !== 200){
            let sqls = 'UPDATE mdl_sid SET LOGIN_CODE = ? WHERE SID =?'
            await utils.dbUse(sqls,[0,user[i].SID])
        }else{
            let sqls = 'UPDATE mdl_sid SET AVAILABLE_POINTS = ? WHERE SID =?'
            await utils.dbUse(sqls,[result.data.availablePoints,user[i].SID])
        }
        
    }
}
//二次订单提交
router.post("/handleEdit",async(req,res)=>{
    let {sid,orderId,payId,proxyId} = req.body
    let result = await return_order(sid,orderId,payId,proxyId)
    res.json(result.data)
})

//批量删除sid
router.post("/delSidMany", async (req, res) => {
    let sidArr = req.body.sidArr;
    //删除mdl_sid表中SID字段匹配sid的所有数据，并删除mdl_coupon的COUPON_SID_ID与当前sid匹配的数据
    for (let i = 0; i < sidArr.length; i++) {
        let sql = "DELETE FROM mdl_sid WHERE sid=?", sql2 = "DELETE FROM mdl_coupon WHERE coupon_sid_id=?";
        await utils.dbUse(sql, [sidArr[i]])
        await utils.dbUse(sql2, [sidArr[i]])
    }

    res.send({ code: 200, msg: "删除成功！" })
})
//退出登录
router.get("/logout", async (req, res) => {
    let { id } = req.query.id;
    // 修改用户的nowLogin字段
    await utils.dbUse(`UPDATE user SET nowLogin = 0 WHERE id = ?`, [id]);
    res.send(utils.returnData({ msg: "退出成功" }));
})
//获取用户信息
router.get("/getLoginUser", (req, res) => {
    let id = req.query.id
    let sql = `SELECT * FROM user WHERE id = ?`;
    pool.query(sql, [id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result[0] }));
    });
})

//获取套餐列表
router.post("/getComboMealList", async (req, res) => {
    let obj = req.body;
    // let sqls = `SELECT c.*, b.TAB_TITLE, a.CLASSIFY_TITLE FROM mdl_combo_meal c INNER JOIN mdl_tab b INNER JOIN classify a ON c.TAB = b.ID AND c.ACCOUNT = a.ID LIMIT ?,?`
    let sqls = `SELECT  * FROM mdl_combo_meal WHERE TITLE LIKE "%${obj.name || ''}%" LIMIT ?,?`
    let { page, size } = utils.pageSize(obj.page, obj.size);
    let { total } = await utils.getSum({ name: "mdl_combo_meal", where: `WHERE TITLE LIKE "%${obj.name || ''}%"`, res, req });
    // let sql = `SELECT id,name,remark,create_time AS createTime FROM more WHERE name LIKE "%${obj.name||''}%" LIMIT ?,?`;
    pool.query(sqls, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
})

//添加套餐
router.post("/addComboMeal", (req, res) => {
    let { tab, MY_KA_USE, MYKA_BREAKFAST_USE, COFFEE_USE, BENEFITS_USE, FREE_DELIVERY_USE, XINPINCHANG_USE, cashCoupon, appoint, image, payPriceLimit, title, BalancePayment, bankCardBreakfast, bankCardVip, plan, account, positive, ticket, startTime, endTime, CIRCULATE, BREAKFAST_CARD_USE,	PRICE,BANK_CARD_VIP,BANK_CARD_BREAKFAST } = req.body;
    if(image.length>250){
        res.json({
            code:200,
            message:"图片过长"
        })
    }
    //定义插入数据的sql语句 CASHCOUPON bankCardVip bankCardBreakfast
    let sql = "INSERT INTO mdl_combo_meal(ID,TAB,MY_KA_USE,MYKA_BREAKFAST_USE,COFFEE_USE,BENEFITS_USE,FREE_DELIVERY_USE,XINPINCHANG_USE,CASHCOUPON,APPOINT,IMAGE,PAYPRICELIMIT,TITLE,BALANCE_PAYMENT,BANK_CARD_BREAKFAST,BANK_CARD_VIP,PLAN,ACCOUNT,POSITIVE,TICKET,START_TIME,END_TIME,CREATED_TIME,CIRCULATE,BREAKFAST_CARD_USE,PRICE) VALUE (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    pool.query(sql, [utils.createId(), tab, MY_KA_USE ? 1 : 0, MYKA_BREAKFAST_USE ? 1 : 0, COFFEE_USE ? 1 : 0, BENEFITS_USE ? 1 : 0, FREE_DELIVERY_USE ? 1 : 0, XINPINCHANG_USE ? 1 : 0, cashCoupon, appoint, image, payPriceLimit, title, BalancePayment ? 1 : 0, bankCardBreakfast ? 1 : 0, bankCardVip ? 1 : 0, plan, account, JSON.stringify(positive), JSON.stringify(ticket), startTime, endTime, utils.createTime(), CIRCULATE, BREAKFAST_CARD_USE,	PRICE], (err, result) => {
        if (err) throw err;
        res.send({ code: 200, msg: "获取方案菜单成功", data: result })
    })
})

//获取方案菜单
router.post("/getClassTab", (req, res) => {
    let sql = "SELECT * FROM mdl_tab";
    pool.query(sql, (err, result) => {
        if (err) throw err;
        res.send({ code: 200, msg: "获取方案菜单成功", data: result ,prefix:prefix})
    })
})

router.post('/getcashCouponList', (req, res) => {
    let sql = "SELECT * FROM mdl_cash_coupon_class";
    pool.query(sql, (err, result) => {
        if (err) throw err;
        res.send({ code: 200, msg: "获取现金券列表成功", data: result })
    })
})
//获取所有账号分类
router.post("/getAccountpa", (req, res) => {
    let sql = "SELECT * FROM classify";
    pool.query(sql, (err, result) => {
        if (err) throw err;
        res.send({ code: 200, msg: "获取账号分类成功", data: result })
    })
})
//获取所有不同类型商品
router.post("/getRegularPriced", (req, res) => {
    let  query = req.body.query
    let sql
    if(!query){
        query = ''
        sql = `SELECT * FROM mdl_product WHERE TYPE=${req.body.key}`;
    }else{
        sql = `SELECT * FROM mdl_product WHERE TYPE=${req.body.key} AND  FOOD_TITLE LIKE '%${query}%'`;
    }
    
    pool.query(sql, (err, result) => {
        if (err) throw err;
        res.send({ code: 200, msg: "获取所有不同类型商品成功", data: result })
    })
})
//删除现金券
router.post("/delcahCoupons", async (req, res) => {
    let { id } = req.body;
    if (!id) {
        return res.send({ code: 500, msg: "请输入正确的现金券id！" });
    }
    let sql = "DELETE FROM mdl_cash_coupon WHERE ID=?"
    await utils.dbUse(sql, [id]);
    res.json({
        code: 200,
        msg: "删除成功！"
    })
})
//添加现金抵用券
router.post("/addcahCoupons", async (req, res) => {
    let { cashCouponCode } = req.body;
    if (!cashCouponCode) {
        return res.send({ code: 500, msg: "请输入正确的现金券编码！" });
    }
    let arr = cashCouponCode.split(",")
    //开始记录日志 创建 mdl_log表
    let log_id = utils.createId();//日志id
    let log_strt_sql = "INSERT INTO mdl_log(ID,LOG_TIME,TYPE,TITLE) VALUES (?,?,?,?)"
    await utils.dbUse(log_strt_sql, [log_id, utils.createTime(), 2, arr.length + '个抵用券入库,开始去重'])
    let newArrs = [...new Set(arr)];
    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `去重后${newArrs.length}个抵用券,开始进入查券入库`, '', 1])

    for (let i = 0; i < newArrs.length; i++) {
        //判断当前code数据库中是否存在
        let sql = "SELECT * FROM mdl_cash_coupon WHERE COUPON_CODE=?"
        let mysqlC = await utils.dbUse(sql, [newArrs[i]]);
        if (mysqlC.length == 0) {
            utils.getCashCouponList(newArrs[i], log_id)
        } else {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `当前抵用券已入库，不可重复入库`, newArrs[i], 0])
        }
    }
    res.json({
        code: 200,
        msg: "入库成功！"
    })
})

//正价商品入库
router.post("/addPriceFood", async (req, res) => {
    let { obj } = req.body;
    //验证PRODUCT_CODE的唯一性
    let sqlFind = "SELECT * FROM mdl_product WHERE PRODUCT_CODE=?"
    let find = await utils.dbUse(sqlFind, [obj.productCode])
    if (find.length > 0) {
        return res.send({ code: 500, msg: "商品编码已存在，请前往仓库查看！" })
    }
    let sql = "INSERT INTO mdl_product(ID,PRODUCT_CODE,IMAGE,DEFAULT_COMBOITEMS,PRICE_INFO,TYPE,CREATE_DATE,FOOD_TITLE) VALUES (?,?,?,?,?,?,?,?)"
    pool.query(sql, [utils.createId(), obj.productCode, obj.productImage, obj.memo, obj.priceInfo.eatInPriceText, 2, utils.createTime(), obj.productName], (err, result) => {
        if (err) return res.send({ code: 500, msg: "入库失败！", err });
        res.send({ code: 200, msg: "入库成功！" })
    })
})
//搜索门店商品
router.post("/searchFood", async (req, res) => {
    let { storeCode, stroeType } = req.body;
    if (!storeCode || !stroeType) {
        return res.send({ code: 500, msg: "请输入正确的门店编码和门店类型！" });
    }
    let menu = await getStoreMe(storeCode, stroeType)
    if (!menu.data.code == 200) {
        return res.send({ code: 500, msg: "官方异常，请稍后再试！" });
    }
    let menus = menu.data.data.menu
    let arr = [];
    for (let i = 0; i < menus.length; i++) {
        for (let j = 0; j < menus[i].productList.length; j++) {
            arr.push(menus[i].productList[j])
        }
        if (menus[i].categories) {
            for (let j = 0; j < menus[i].categories.length; j++) {
                for (let k = 0; k < menus[i].categories[j].productList.length; k++) {
                    arr.push(menus[i].categories[j].productList[k])
                }
            }
        }
    }
    res.json({
        code: 200,
        data: arr
    });
})
//登录
/**
 * @swagger
 * tags:
 *   name: Admin
 *   description: 管理员相关操作
 */

/**
 * @swagger
 * /admin/login:
 *   post:
 *     summary: 管理员登录
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *                 description: 用户名
 *                 example: admin
 *               password:
 *                 type: string
 *                 description: 密码
 *                 example: 123456
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 msg:
 *                   type: string
 *                   example: 登录成功
 *                 token:
 *                   type: string
 *                   example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwiaWF0IjoxNjI4NjY2NjY2fQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
 *       401:
 *         description: 登录失败
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 401
 *                 msg:
 *                   type: string
 *                   example: 用户名或密码错误
 */
// 简单测试路由
router.get('/test', (req, res) => {
    console.log('测试路由被访问');
    res.json({ message: 'Admin路由正常工作', timestamp: new Date().toISOString() });
});

router.post('/login', async (req, res) => {
    try {
        // 前端发送的字段是 name 和 pwd，不是 username 和 password
        let { name, pwd } = req.body;
        console.log('登录请求:', { name, pwd });

        if (!name || !pwd) {
            return res.send({ code: 401, msg: '用户名和密码不能为空' });
        }

        // 查询数据库验证用户
        let sql = "SELECT id, name, pwd, admin, status FROM user WHERE name = ? AND admin = 1";
        pool.query(sql, [name], (err, result) => {
            if (err) {
                console.error('数据库查询错误:', err);
                return res.send({ code: 500, msg: '服务器错误' });
            }

            if (result.length === 0) {
                return res.send({ code: 401, msg: '用户名或密码错误' });
            }

            let user = result[0];

            // 检查用户状态
            if (user.status !== 1) {
                return res.send({ code: 401, msg: '账号已被禁用' });
            }

            // 验证密码（MD5）
            if (user.pwd !== pwd) {
                return res.send({ code: 401, msg: '用户名或密码错误' });
            }

            // 生成JWT token (使用与验证相同的配置和格式)
            const config = require("../config/env_config.js");
            let token = jwt.sign(
                {
                    uid: user.id  // 只使用uid字段，与utils.verToken()保持一致
                },
                config.jwt.secret,
                {
                    expiresIn: config.jwt.expiresIn,
                    algorithm: config.jwt.algorithm
                }
            );

            // 更新登录状态
            pool.query("UPDATE user SET nowLogin = 1 WHERE id = ?", [user.id], (updateErr) => {
                if (updateErr) {
                    console.error('更新登录状态错误:', updateErr);
                }
            });

            res.send({
                code: 200,
                msg: '登录成功',
                token,
                user: {
                    id: user.id,
                    name: user.name,
                    admin: user.admin
                }
            });
        });
    } catch (err) {
        console.error('登录错误:', err);
        res.send({ code: 500, msg: '服务器错误' });
    }
});

//获取用户信息
/**
 * @swagger
 * /admin/getUserInfo:
 *   post:
 *     summary: 获取用户信息
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 msg:
 *                   type: string
 *                   example: 获取成功
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/getUserInfo", async (req, res) => {
    let user = await utils.getUserRole(req, res);
    let sql = `SELECT b.menu_bg AS menuBg,b.menu_sub_bg AS menuSubBg,b.menu_text AS menuText,b.menu_active_text AS menuActiveText,b.menu_sub_active_text AS menuSubActiveText,b.menu_hover_bg AS menuHoverBg FROM theme AS b WHERE user_id=?`;
    pool.query(sql, [user.user.id], (err, result) => {
        if (err || result.length === 0) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: { ...user, theme: result[0] } }));
    })

})
//获取用户今日订单数
/**
 * @swagger
 * /admin/getDayOderNumber:
 *   post:
 *     summary: 获取用户今日订单数
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       day_order_number:
 *                         type: integer
 *                         example: 10
 *       401:
 *         description: token无效
 */
router.post('/getDayOderNumber',async(req,res)=>{
    let user = await utils.getUserRole(req, res);
    let sql = `SELECT COUNT(*) AS day_order_number
FROM mdl_order
WHERE PROXY_ID = ?  -- 替换为具体的用户ID
  AND CREATRD_TIME >= CURDATE()
  AND CREATRD_TIME < CURDATE() + INTERVAL 1 DAY;`
  pool.query(sql, [user.user.id], (err, result) => {
        if (err ) return res.send(utils.returnData({ code: -1, err, req }));
        res.send({
            code:1,
            data:result
        });
    })
})
//获取用户信息
/**
 * @swagger
 * /admin/getUserInfos:
 *   post:
 *     summary: 获取用户信息
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/getUserInfos", async (req, res) => {
    let user = await utils.getUserRole(req, res);
    let sql = `SELECT * FROM user WHERE id = ?`;
    pool.query(sql, [user.user.id], (err, result) => {
        if (err || result.length === 0) return res.send(utils.returnData({ code: -1, err, req }));
        res.send({
            code:1,
            data:result[0]
        });
    })

})

//修改方案
/**
 * @swagger
 * /admin/updateMeal:
 *   post:
 *     summary: 修改方案
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               meal:
 *                 type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: string
 *                   example: 修改成功
 *       401:
 *         description: token无效
 */
router.post("/updateMeal", async (req, res) => {
    let { meal } = req.body;
    let sql = `UPDATE mdl_combo_meal SET TAB = ?,MY_KA_USE=?,MYKA_BREAKFAST_USE=?,COFFEE_USE=?,BENEFITS_USE=?,FREE_DELIVERY_USE=?,XINPINCHANG_USE=?,CASHCOUPON=?,APPOINT=?,IMAGE=?,PAYPRICELIMIT=?,TITLE=?,BALANCE_PAYMENT=?,BANK_CARD_BREAKFAST=?,BANK_CARD_VIP=?,PLAN=?,ACCOUNT=?,POSITIVE=?,TICKET=?,START_TIME=?,END_TIME=?,CREATED_TIME=?,PRICE=?,BREAKFAST_CARD_USE=?,TAB_TITLE=?,CLASSIFY_TITLE=? WHERE ID=?`
    let { ID, TAB, MY_KA_USE, MYKA_BREAKFAST_USE, COFFEE_USE, BENEFITS_USE, FREE_DELIVERY_USE, XINPINCHANG_USE, CASHCOUPON, APPOINT, IMAGE, PAYPRICELIMIT, TITLE, BALANCE_PAYMENT, BANK_CARD_BREAKFAST, BANK_CARD_VIP, PLAN, ACCOUNT, POSITIVE, TICKET, START_TIME, END_TIME, CREATED_TIME, PRICE, BREAKFAST_CARD_USE, TAB_TITLE, CLASSIFY_TITLE } = meal
    await utils.dbUse(sql, [TAB, MY_KA_USE, MYKA_BREAKFAST_USE, COFFEE_USE, BENEFITS_USE, FREE_DELIVERY_USE, XINPINCHANG_USE, CASHCOUPON, APPOINT, IMAGE, PAYPRICELIMIT, TITLE, BALANCE_PAYMENT, BANK_CARD_BREAKFAST, BANK_CARD_VIP, PLAN, ACCOUNT, POSITIVE, TICKET ? TICKET : [], START_TIME, END_TIME, CREATED_TIME, PRICE, BREAKFAST_CARD_USE, TAB_TITLE, CLASSIFY_TITLE, ID])
    res.json({
        code: 1,
        data: "修改成功"
    })
})



function getRouter(req, res, sidebar = false) {
    return new Promise(async (resolve, reject) => {
        let sql = "SELECT id,parent_id AS parentId,path,hidden,redirect,always_show AS alwaysShow,name,layout,parent_view AS parentView,meta,component,sort,update_time AS updateTime,alone,role_key AS roleKey,menu_type AS menuType FROM router_menu ORDER BY sort ASC, update_time DESC";
        let userRole = await utils.getUserRole(req, res);
        if (userRole == -1) return res.send(utils.returnData({ code: -1, req }));
        if (!userRole.userRole || userRole.userRole == null || userRole.userRole == "null") userRole.userRole = "";
        //角色权限
        let roles = userRole.userRole.split(",");
        pool.query(sql, (err, result) => {
            if (err) return res.send(utils.returnData({ code: -1, err, req }));
            let list = [...result], routerArr = [];
            let filterAsyncRoutes = (lists, parentId, pathView = "") => {
                let resArr = [], obj = {};
                lists.map((t) => {
                    let objs = { ...t };
                    try { objs.meta = JSON.parse(objs.meta); } catch (err) { objs.meta = {}; }
                    objs.title = objs.meta.title || "---";
                    objs.pathView = t.path;
                    //按钮自动隐藏
                    if (objs.menuType === "F") objs.hidden = 1;
                    //递归
                    if (objs.parentId == parentId) {
                        objs.path = pathView + objs.path;
                        obj = { ...objs, children: filterAsyncRoutes(list, objs.id, objs.path) };
                        //菜单下有子级，单独拿出来
                        if (obj.menuType === "C" && obj.children && obj.children.length != 0) {
                            routerArr.push(...obj.children)
                            sidebar && delete obj.children;
                        }
                        //如果是总管理
                        if (userRole.user.admin == 1 || userRole.roleAdmin) { resArr.push(obj); } else {
                            //只拿角色权限通过的
                            if (roles.some((role) => obj.id == role)) resArr.push(obj);
                        }
                    }
                });
                return resArr;
            };
            let routerMenu = filterAsyncRoutes(list, 0, "");
            //如果是独立的（一级）
            sidebar && routerMenu.forEach(t => {
                if (t.menuType === "C" && (!t.children || t.children.length === 0)) {
                    t.layout = 1;
                    t.children = [{ ...t, layout: 0, alone: 1, children: undefined, }]
                    t.path = "/" + Math.random();
                }
            });
            resolve({ routerMenu, routerArr })
        });

    })
}
//获取路由 侧边栏
/**
 * @swagger
 * /admin/getRouter:
 *   post:
 *     summary: 获取路由(侧边栏)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
//测试数据库连接
router.post("/testDb", (req, res) => {
    pool.query("SELECT 1 as test", (err, result) => {
        if (err) {
            console.error('数据库测试错误:', err);
            return res.send(utils.returnData({ code: -1, msg: "数据库连接失败", err }));
        }
        res.send(utils.returnData({ data: { test: "数据库连接正常", result } }));
    });
});

router.post("/getRouter", async (req, res) => {
    try {
        console.log('开始获取路由...');
        let { routerMenu, routerArr } = await getRouter(req, res, true);
        console.log('路由获取成功:', routerMenu.length, routerArr.length);
        function bianpinghua(list) {
            let arr = [];
            list.map(t => {
                if (t.children && t.children.length) arr.push(...bianpinghua(t.children))
                arr.push({ ...t, layout: 1, path: "/" + Math.random(), children: [{ ...t, layout: 0, alone: 1, children: undefined }], hidden: 1 });
            })
            return arr
        }
        routerArr = bianpinghua(routerArr);
        routerArr = routerArr.filter((obj, index, self) => index === self.findIndex((t) => (t.id === obj.id)));
        res.send(utils.returnData({ data: { routerMenu: routerMenu.concat(routerArr) } }))
    } catch (error) {
        console.error('获取路由错误:', error);
        res.send(utils.returnData({ code: -1, msg: "获取路由失败", error: error.message }));
    }
});
//菜单管理获取
/**
 * @swagger
 * /admin/getRouterSystem:
 *   post:
 *     summary: 菜单管理获取
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/getRouterSystem", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.menus.menuQuery] });
    let { routerMenu } = await getRouter(req, res);
    res.send(utils.returnData({ data: { routerMenu } }));
})
//添加菜单
/**
 * @swagger
 * /admin/addMenu:
 *   post:
 *     summary: 添加菜单
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addMenu", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.menus.menuAdd] });
    let sql = "INSERT INTO router_menu(parent_id,path,hidden,name,layout,parent_view,meta,component,sort,alone,role_key,menu_type,update_time) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)",
        obj = req.body;
    let meta = {};
    await utils.existName({ sql: "SELECT id FROM router_menu WHERE role_key=?", name: obj.roleKey, res, msg: "权限字符已存在！", req });
    await utils.existName({ sql: "SELECT id FROM router_menu WHERE name=?", name: obj.name, res, msg: "页面名称已存在！！", req });
    meta.title = obj.title;
    meta.icon = obj.icon;
    meta.noCache = obj.noCache;
    pool.query(sql, [obj.parentId, obj.path, obj.hidden, obj.name, obj.parentId == 0 ? 1 : 0, obj.parentView, JSON.stringify(meta), obj.component, obj.sort, obj.alone, obj.roleKey, obj.menuType, new Date(),], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//修改菜单
/**
 * @swagger
 * /admin/changeMenu:
 *   post:
 *     summary: 修改菜单
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/changeMenu", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.menus.menuUp] });
    let sql = "UPDATE  router_menu SET parent_id=?,path=?,hidden=?,name=?,layout=?,parent_view=?,meta=?,component=?,sort=?,alone=?,role_key=?,menu_type=?,update_time=? WHERE id=?",
        obj = req.body;
    let judgeUserNameRes = await utils.judgeUserName({ sql: "SELECT role_key FROM router_menu WHERE  id=?", sqlName: "role_key", name: obj.roleKey, id: obj.id });
    if (judgeUserNameRes === 1) await utils.existName({ sql: "SELECT id FROM router_menu WHERE role_key=?", name: obj.roleKey, res, msg: "权限字符已存在！", req });
    let judgeUserNameRes2 = await utils.judgeUserName({ sql: "SELECT name FROM router_menu WHERE  id=?", sqlName: "name", name: obj.name, id: obj.id });
    let meta = {};
    if (judgeUserNameRes2 === 1) await utils.existName({ sql: "SELECT id FROM router_menu WHERE name=?", name: obj.name, res, msg: "页面名称已存在！", req });
    meta.title = obj.title;
    meta.icon = obj.icon;
    meta.noCache = obj.noCache;
    pool.query(sql, [obj.parentId, obj.path, obj.hidden, obj.name, obj.parentId == 0 ? 1 : 0, obj.parentView, JSON.stringify(meta), obj.component, obj.sort, obj.alone, obj.roleKey, obj.menuType, new Date(), obj.id,], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//删除菜单
/**
 * @swagger
 * /admin/delMenu:
 *   post:
 *     summary: 删除菜单
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/delMenu", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.menus.menuDelte] });
    let sql = "DELETE FROM router_menu WHERE id=?";
    let selectSql = "SELECT id FROM router_menu WHERE parent_id=?";
    let obj = req.body;
    pool.query(selectSql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        if (result.length != 0) return res.send(utils.returnData({ code: -1, msg: "删除失败，请先删除子级", err, req }));
        pool.query(sql, [obj.id], (err2, result2) => {
            if (err2) return res.send(utils.returnData({ code: -1, err, req }));
            res.send(utils.returnData({ data: result }));
        });
    });
});
//查询角色
/**
 * @swagger
 * /admin/getRoles:
 *   post:
 *     summary: 查询角色
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getRoles", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.role.roleQuery] });
    let obj = req.body;
    let { page, size } = utils.pageSize(obj.page, obj.size);
    let { total } = await utils.getSum({ name: "roles", where: `WHERE name LIKE "%${obj.name || ''}%"`, res, req });
    let sql = `SELECT id,name,roles,checked_roles AS checkedRoles,role_key AS roleKey,create_time AS createTime FROM roles WHERE name LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
});
//查询角色全部
/**
 * @swagger
 * /admin/getRolesAll:
 *   post:
 *     summary: 查询角色全部
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: token无效
 */
router.post("/getRolesAll", async (req, res) => {
    let sql = `SELECT id,name,roles,checked_roles AS checkedRoles,role_key AS roleKey FROM roles`;
    pool.query(sql, (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//复刻
/**
 * @swagger
 * /admin/copay:
 *   post:
 *     summary: 复刻
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *               titlt:
 *                 type: string
 *     responses:
 *       200:
 *         description: 复刻成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 msg:
 *                   type: string
 *                   example: 复刻成功！
 *       500:
 *         description: 复刻失败
 */
router.post("/copay", async (req, res) => {
    //新增功能
        //有券商品复刻couponID 及 商品名称
    let { id, titlt } = req.body
    let sqlsq = `SELECT * FROM mdl_product WHERE FOOD_TITLE=?`
    let findS = await utils.dbUse(sqlsq, [titlt])
    if(findS.length >0){
        res.json({
            code:500,
            message:"名称已存在"
        })
        return
    }
    let sql = `SELECT * FROM mdl_product WHERE id=?`;
    let food = await utils.dbUse(sql, [id])
    let sql3 = "INSERT INTO mdl_product(ID,PRODUCT_CODE,IMAGE,DEFAULT_COMBOITEMS,PRICE_INFO,TYPE,CREATE_DATE,FOOD_TITLE,COUPON_ID,MANY_CODE) VALUES (?,?,?,?,?,?,?,?,?,?)"
    pool.query(sql3, [utils.createId(), food[0].PRODUCT_CODE, food[0].IMAGE, food[0].DEFAULT_COMBOITEMS, food[0].PRICE_INFO, food[0].TYPE, utils.createTime(), titlt,food[0].COUPON_ID,food[0].MANY_CODE], (err, result) => {
        if (err) return res.send({ code: 500, msg: "复刻失败！", err });
        res.send({ code: 200, msg: "复刻成功！" })
    })

})
//删除套餐
/**
 * @swagger
 * /admin/delCombo:
 *   post:
 *     summary: 删除套餐
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 msg:
 *                   type: string
 *                   example: 删除成功！
 */
router.post("/delCombo", async (req, res) => {
    let { id } = req.body;
    let sql = "DELETE FROM mdl_combo_meal WHERE id=?";
    let sql1 = "DELETE FROM mdl_code_tab_meal WHERE MDL_MEAL_ID=?";
    let clectsql = `SELECT * FROM mdl_code_tab_meal WHERE MDL_MEAL_ID=?`
    let all = await utils.dbUse(clectsql, [id])
    for (let i = 0; i < all.length; i++) {
        let sqlo = `DELETE FROM mdl_code_tab WHERE id=?`
        await utils.dbUse(sqlo, [all[i].MDL_CODE_TAB_ID])
        let sqlc = `DELETE FROM mdl_code WHERE FATHERR_ID=?`
        await utils.dbUse(sqlc, [all[i].MDL_CODE_TAB_ID])
    }
    await utils.dbUse(sql, [id])
    await utils.dbUse(sql1, [id])
    res.json({ code: 200, msg: "删除成功！" })
});
//删除sid
/**
 * @swagger
 * /admin/delsid:
 *   post:
 *     summary: 删除sid
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sid:
 *                 type: string
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 msg:
 *                   type: string
 *                   example: 删除成功！
 */
router.post("/delsid", async (req, res) => {
    let sid = req.body.sid;
    //删除mdl_sid表中SID字段匹配sid的所有数据，并删除mdl_coupon的COUPON_SID_ID与当前sid匹配的数据
    let sql = "DELETE FROM mdl_sid WHERE sid=?", sql2 = "DELETE FROM mdl_coupon WHERE coupon_sid_id=?";
    await utils.dbUse(sql, [sid])
    await utils.dbUse(sql2, [sid])
    res.send({ code: 200, msg: "删除成功！" })
})
//添加角色
/**
 * @swagger
 * /admin/addRoles:
 *   post:
 *     summary: 添加角色
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addRoles", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.role.roleAdd] });
    let sql = "INSERT INTO roles(name,roles,checked_roles,role_key,create_time) VALUES (?,?,?,?,?)", obj = req.body;
    await utils.existName({ sql: "SELECT id FROM roles WHERE role_key=?", name: obj.roleKey, res, msg: "权限字符已存在！", req });
    pool.query(sql, [obj.name, obj.roles, obj.checkedRoles, obj.roleKey, new Date()], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//修改角色
/**
 * @swagger
 * /admin/upRoles:
 *   post:
 *     summary: 修改角色
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upRoles", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.role.roleUp] });
    let sql = "UPDATE  roles SET roles=?,name=?,checked_roles=?,role_key=? WHERE id=?", obj = req.body;
    //总管理不能操作
    await utils.upAdminRole({ req, res, id: obj.id });
    let judgeUserNameRes = await utils.judgeUserName({ sql: "SELECT role_key FROM roles WHERE  id=?", sqlName: "role_key", name: obj.roleKey, id: obj.id });
    if (judgeUserNameRes === 1) await utils.existName({ sql: "SELECT id FROM roles WHERE role_key=?", name: obj.roleKey, res, msg: "权限字符已存在！", req });
    pool.query(sql, [obj.roles, obj.name, obj.checkedRoles, obj.roleKey, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//删除角色
/**
 * @swagger
 * /admin/delRoles:
 *   post:
 *     summary: 删除角色
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/delRoles", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.role.roleDelte] });
    let sql = "DELETE FROM roles WHERE id=?", obj = req.body;
    //总管理不能操作
    await utils.upAdminRole({ req, res, id: obj.id });
    pool.query(sql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//添加用户
/**
 * @swagger
 * /admin/addUser:
 *   post:
 *     summary: 添加用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addUser", async (req, res) => {
    if (req.body.rolesId !== '22') {
        await utils.checkPermi({ req, res, role: [systemSettings.user.userAdd] });
    }
    let sql = "INSERT INTO user(name,status,roles_id,remark,pwd,more_id,create_time) VALUES (?,?,?,?,?,?,?)", obj = req.body;
    await utils.existName({ sql: "SELECT id FROM user WHERE  name=?", name: obj.name, res, msg: "用户名已被使用！", req });
    pool.query(sql, [obj.name, obj.status, obj.rolesId, obj.remark, obj.pwd, obj.moreId, new Date()], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err }));
        let themeSql = "INSERT INTO theme(user_id,menu_bg,menu_sub_bg,menu_text,menu_active_text,menu_sub_active_text,menu_hover_bg) VALUES (?,?,?,?,?,?,?)";
        pool.query(themeSql, [result.insertId, "#304156", "#304156", "#bfcad5", "#409eff", "#fff", "#001528"], (themeErr, themeRes) => {
            if (themeErr) return res.send(utils.returnData({ code: -1, err: themeErr, req }));
            res.send(utils.returnData({ data: result }));
        })

    });
});

//站点查询用户
/**
 * @swagger
 * /admin/getSiteUser:
 *   post:
 *     summary: 站点查询用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getSiteUser", async (req, res) => {
    let obj = req.body;
    let roes = '22'
    let { page, size } = utils.pageSize(obj.page, obj.size);
    let { total } = await utils.getSum({ name: "user", where: `WHERE roles_id=${roes}  AND name LIKE "%${obj.name || ''}%"`, res, req });
    let sql = `SELECT a.id AS id,name,status,roles_id AS rolesId,remark,admin,more_id AS moreId,a.create_time AS createTime,b.menu_bg AS menuBg,b.menu_sub_bg AS menuSubBg,b.menu_text AS menuText,b.menu_active_text AS menuActiveText,b.menu_sub_active_text AS menuSubActiveText,b.menu_hover_bg AS menuHoverBg FROM user AS a LEFT JOIN theme b ON a.id=b.user_id WHERE a.roles_id=${roes} AND name LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
})
//查询用户
/**
 * @swagger
 * /admin/getUser:
 *   post:
 *     summary: 查询用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getUser", async (req, res) => {
    // await utils.checkPermi({req,res,role:[systemSettings.user.userQuery]});
    let obj = req.body;
    let { page, size } = utils.pageSize(obj.page, obj.size);
    let { total } = await utils.getSum({ name: "user", where: `WHERE name LIKE "%${obj.name || ''}%"`, res, req });
    let sql = `SELECT a.id AS id,name,status,balance,roles_id AS rolesId,remark,admin,more_id AS moreId,a.create_time AS createTime,b.menu_bg AS menuBg,b.menu_sub_bg AS menuSubBg,b.menu_text AS menuText,b.menu_active_text AS menuActiveText,b.menu_sub_active_text AS menuSubActiveText,b.menu_hover_bg AS menuHoverBg FROM user AS a LEFT JOIN theme b ON a.id=b.user_id WHERE name LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
});

//手动重置权益
/**
 * @swagger
 * /admin/startQy:
 *   post:
 *     summary: 手动重置权益
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 重置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 */
router.post("/startQy", async (req, res) => {
    let sql = "SELECT * FROM mdl_sid WHERE QY=?";
    let qxUser = await utils.dbUse(sql, [1])
    for (let i = 0; i < qxUser.length; i++) {
        qxFind(qxUser[i].SID)
    }
})

//修改主题
/**
 * @swagger
 * /admin/upTheme:
 *   post:
 *     summary: 修改主题
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upTheme", async (req, res) => {
    let sql = "UPDATE  theme SET menu_bg=?,menu_sub_bg=?,menu_text=?,menu_active_text=?,menu_sub_active_text=?,menu_hover_bg=? WHERE user_id=?", obj = req.body;
    pool.query(sql, [obj.menuBg, obj.menuSubBg, obj.menuText, obj.menuActiveText, obj.menuSubActiveText, obj.menuHoverBg, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//修改用户
/**
 * @swagger
 * /admin/upUser:
 *   post:
 *     summary: 修改用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upUser", async (req, res) => {
    if (req.body.rolesId !== '22') {
        await utils.checkPermi({ req, res, role: [systemSettings.user.userUp] });
    }
    let sql = "UPDATE  user SET balance=?, name=?,status=?,roles_id=?,remark=?,more_id=? WHERE id=?", obj = req.body;
    //总管理不能操作
    await utils.upAdmin({ req, res, id: obj.id });
    let judgeUserNameRes = await utils.judgeUserName({ sql: "SELECT name FROM user WHERE  id=?", name: obj.name, id: obj.id });
    if (judgeUserNameRes === 1) await utils.existName({ sql: "SELECT id FROM user WHERE  name=?", name: obj.name, res, msg: "用户名已被使用！", req });
    pool.query(sql, [obj.balance, obj.name, obj.status, obj.rolesId, obj.remark, obj.moreId, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
/**
 * 获取普通用户统计数据
 * @route POST /admin/getAccountStatistics
 * @group 用户
 * @param {object} body.body.required - 查询参数
 * @returns {object} 200 - 统计数据
 */
/**
 * @swagger
 * /admin/getAccountStatistics:
 *   post:
 *     summary: 获取普通用户统计数据
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/getAccountStatistics", async (req, res) => {
    let obj = req.body;
    let whereCondition = `WHERE QY=0 AND SID LIKE "%${obj.name || ''}%"`;
    
    try {
        // 获取总数
        let { total } = await utils.getSum({ name: "mdl_sid", where: whereCondition, res, req });
        
        // 获取启用账号数（ENABLED=1）
        let enabledCountSql = `SELECT COUNT(*) as count FROM mdl_sid ${whereCondition} AND ENABLED=1`;
        let enabledResult = await new Promise((resolve, reject) => {
            pool.query(enabledCountSql, (err, result) => {
                if (err) reject(err);
                else resolve(result[0].count);
            });
        });
        
        // 获取禁用账号数（ENABLED=0）
        let disabledCountSql = `SELECT COUNT(*) as count FROM mdl_sid ${whereCondition} AND ENABLED=0`;
        let disabledResult = await new Promise((resolve, reject) => {
            pool.query(disabledCountSql, (err, result) => {
                if (err) reject(err);
                else resolve(result[0].count);
            });
        });
        
        // 获取使用中账号数（START=1）
        let inUseCountSql = `SELECT COUNT(*) as count FROM mdl_sid ${whereCondition} AND START=1`;
        let inUseResult = await new Promise((resolve, reject) => {
            pool.query(inUseCountSql, (err, result) => {
                if (err) reject(err);
                else resolve(result[0].count);
            });
        });
        
        // 获取空闲账号数（START=0）
        let idleCountSql = `SELECT COUNT(*) as count FROM mdl_sid ${whereCondition} AND START=0`;
        let idleResult = await new Promise((resolve, reject) => {
            pool.query(idleCountSql, (err, result) => {
                if (err) reject(err);
                else resolve(result[0].count);
            });
        });
        
        // 获取今日新增数（假设CREATE_TIME是时间戳）
        let today = new Date();
        today.setHours(0, 0, 0, 0);
        let todayTimestamp = today.getTime();
        let todayCountSql = `SELECT COUNT(*) as count FROM mdl_sid ${whereCondition} AND CREATE_TIME >= ?`;
        let todayResult = await new Promise((resolve, reject) => {
            pool.query(todayCountSql, [todayTimestamp], (err, result) => {
                if (err) reject(err);
                else resolve(result[0].count);
            });
        });
        
        res.send(utils.returnData({ 
            data: {
                totalCount: total,
                enabledCount: enabledResult,
                disabledCount: disabledResult,
                inUseCount: inUseResult,
                idleCount: idleResult,
                todayCount: todayResult
            }
        }));
    } catch (err) {
        res.send(utils.returnData({ code: -1, err, req }));
    }
});
//修改用户密码
/**
 * @swagger
 * /admin/upUserPwd:
 *   post:
 *     summary: 修改用户密码
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *               pwd:
 *                 type: string
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upUserPwd", async (req, res) => {
    if (req.body.rolesId !== '22') {
        await utils.checkPermi({ req, res, role: [systemSettings.user.userPwd] });

    }
    let sql = "UPDATE  user SET pwd=? WHERE id=?", obj = req.body;
    let getUserIdRes = await utils.getUserId({ id: obj.id, req, res });
    if (getUserIdRes.admin === 1) {
        let user = await utils.getUserInfo(req, res);
        if (user.admin !== 1) return res.send(utils.returnData({ code: -1, msg: "总管理密码只能总管理账号修改！", req }));
    }
    pool.query(sql, [obj.pwd, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//删除用户
/**
 * @swagger
 * /admin/delUser:
 *   post:
 *     summary: 删除用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/delUser", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.user.userDelte] });
    let obj = req.body;
    //总管理不能操作
    await utils.upAdmin({ req, res, id: obj.id });
    let user = await utils.getUserInfo(req, res);
    if (obj.id == user.id) return res.send(utils.returnData({ code: -1, msg: "无法删除正在使用中的用户~", req }));
    let sql = "DELETE FROM user WHERE id=?";
    pool.query(sql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err: err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//添加多账号
/**
 * @swagger
 * /admin/addMore:
 *   post:
 *     summary: 添加多账号
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addMore", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.more.moreAdd] });
    let sql = "INSERT INTO more(name,remark,create_time) VALUES (?,?,?)", obj = req.body;
    await utils.existName({ sql: "SELECT id FROM more WHERE  name=?", name: obj.name, res, msg: "账号名已存在！", req });
    pool.query(sql, [obj.name, obj.remark, new Date()], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//查询多账号
/**
 * @swagger
 * /admin/getMore:
 *   post:
 *     summary: 查询多账号
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getMore", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.more.moreQuery] });
    let obj = req.body;
    let { page, size } = utils.pageSize(obj.page, obj.size);
    let { total } = await utils.getSum({ name: "more", where: `WHERE name LIKE "%${obj.name || ''}%"`, res, req });
    let sql = `SELECT id,name,remark,create_time AS createTime FROM more WHERE name LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
});
//查询多账号 全部
/**
 * @swagger
 * /admin/getMoreAll:
 *   post:
 *     summary: 查询多账号 全部
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: token无效
 */
router.post("/getMoreAll", async (req, res) => {
    let sql = "SELECT id,name,remark FROM more";
    pool.query(sql, (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//修改多账号
/**
 * @swagger
 * /admin/upMore:
 *   post:
 *     summary: 修改多账号
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upMore", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.more.moreUp] });
    let sql = "UPDATE  more SET name=?,remark=? WHERE id=?", obj = req.body;
    pool.query(sql, [obj.name, obj.remark, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//删除多账号
/**
 * @swagger
 * /admin/delMore:
 *   post:
 *     summary: 删除多账号
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/delMore", async (req, res) => {
    await utils.checkPermi({ req, res, role: [systemSettings.more.moreDelte] });
    let sql = "DELETE FROM more WHERE id=?", obj = req.body;
    pool.query(sql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//添加字典
/**
 * @swagger
 * /admin/addDict:
 *   post:
 *     summary: 添加字典
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addDict", async (req, res) => {
    let sql = "INSERT INTO dict(name,type,create_time,remark) VALUES (?,?,?,?)", obj = req.body;
    await utils.existName({ sql: "SELECT id FROM dict WHERE  type=?", name: obj.type, res, msg: "字典类型已存在！", req });
    pool.query(sql, [obj.name, obj.type, new Date(), obj.remark], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//查询字典
/**
 * @swagger
 * /admin/getDict:
 *   post:
 *     summary: 查询字典
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getDict", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT id,name,create_time AS createTime,remark,type FROM dict WHERE name LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    let { total } = await utils.getSum({ name: "dict", where: `WHERE name LIKE "%${obj.name || ''}%"`, res, req });
    let { page, size } = utils.pageSize(obj.page, obj.size);
    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
});
//查询普通用户
/**
 * @swagger
 * /admin/getAccount:
 *   post:
 *     summary: 查询普通用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getAccount", async (req, res) => {
    let obj = req.body;
    // 构建WHERE条件
    let whereConditions = ['QY=0'];
    
    // 添加名称筛选
    if (obj.name) {
        whereConditions.push(`SID LIKE "%${obj.name}%"`);
    }
    
    // 添加启用状态筛选
    if (obj.enabled !== undefined && obj.enabled !== '') {
        if (obj.enabled === '1') {
            whereConditions.push('(ENABLED IS NULL OR ENABLED = 1)');
        } else if (obj.enabled === '0') {
            whereConditions.push('ENABLED = 0');
        }
    }
    
    // 添加使用状态筛选
    if (obj.start !== undefined && obj.start !== '') {
        whereConditions.push(`START = ${obj.start}`);
    }
    
    let whereClause = whereConditions.join(' AND ');
    let sql = `SELECT *, COALESCE(ENABLED, 1) as ENABLED FROM mdl_sid WHERE ${whereClause} LIMIT ?,?`;
    let { total } = await utils.getSum({ name: "mdl_sid", where: `WHERE ${whereClause}`, res, req });
    let { page, size } = utils.pageSize(obj.page, obj.size);

    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));

        // 确保每个记录都有ENABLED字段
        const processedResult = result.map(item => ({
            ...item,
            ENABLED: item.ENABLED !== undefined ? item.ENABLED : 1
        }));

        res.send(utils.returnData({ data: processedResult, total }));
    });
});
//查询权益用户
/**
 * @swagger
 * /admin/getrights:
 *   post:
 *     summary: 查询权益用户
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getrights", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT * FROM mdl_sid WHERE QY=1 AND SID LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    let { total } = await utils.getSum({ name: "mdl_sid", where: `WHERE QY=1 AND SID LIKE "%${obj.name || ''}%"`, res, req });
    let { page, size } = utils.pageSize(obj.page, obj.size);

    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));


        res.send(utils.returnData({ data: result, total }));
    });
});
//查询字典(不分页)
/**
 * @swagger
 * /admin/getDictAll:
 *   post:
 *     summary: 查询字典(不分页)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: token无效
 */
router.post("/getDictAll", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT id,name,create_time AS createTime,remark,type FROM dict WHERE name LIKE "%${obj.name || ''}%"`;
    pool.query(sql, (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//修改字典
/**
 * @swagger
 * /admin/upDict:
 *   post:
 *     summary: 修改字典
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upDict", async (req, res) => {
    let sql = "UPDATE  dict SET name=?,type=?,remark=? WHERE id=?", obj = req.body;
    let judgeUserNameRes = await utils.judgeUserName({ sql: "SELECT type FROM dict WHERE  id=?", name: obj.type, id: obj.id, sqlName: "type" });
    if (judgeUserNameRes === 1) await utils.existName({ sql: "SELECT id FROM dict WHERE  type=?", name: obj.type, res, msg: "字典类型已存在！", req });

    pool.query(sql, [obj.name, obj.type, obj.remark, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//删除字典
/**
 * @swagger
 * /admin/delDict:
 *   post:
 *     summary: 删除字典
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/delDict", async (req, res) => {
    let sql = "DELETE FROM dict WHERE id=?", obj = req.body;
    pool.query(sql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//更新权益
/**
 * @swagger
 * /admin/upqy:
 *   post:
 *     summary: 更新权益
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sid:
 *                 type: string
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: string
 *                   example: 更新成功
 *       401:
 *         description: token无效
 */
router.post("/upqy", async (req, res) => {
    let sid = req.body.sid;
    let qxUser = [{ SID: sid }]
    for (let i = 0; i < qxUser.length; i++) {
        qxFind(qxUser[i].SID)
    }
    res.json({
        code: 200,
        data: "更新成功"
    })
})
//查券入库 - 优化版本
/**
 * @swagger
 * /admin/sidCouponlist:
 *   post:
 *     summary: 查券入库 - 优化版本
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sid:
 *                 type: string
 *               classify:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查券入库成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       400:
 *         description: 缺少必要参数
 *       500:
 *         description: 查券入库失败
 */
router.post("/sidCouponlist", async (req, res) => {
    const startTime = Date.now();
    const log_id = utils.createId();
    
    try {
        const { sid, classify } = req.body;
        
        // 参数验证
        if (!sid || !classify) {
            console.log(`[查券入库] 参数验证失败 - sid: ${sid}, classify: ${classify}`);
            return res.json({
                code: 400,
                msg: "缺少必要参数 sid 或 classify"
            });
        }
        
        console.log(`[查券入库] 开始处理 - SID: ${sid}, 分类: ${classify}`);
        
        // 记录开始日志
        const log_start_sql = "INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)";
        await utils.dbUse(log_start_sql, [utils.createId(), log_id, `开始查券入库 - SID: ${sid}`, sid, 1]);
        
        // 获取券和商品信息
        console.log(`[查券入库] 正在获取券和商品信息...`);
        const { coupon, findFood } = await findCouponFoodk(sid, {});
        
        console.log(`[查券入库] 获取到 ${coupon.length} 个券, ${findFood.length} 个商品`);
        
        // 处理券信息
        let couponStr = '';
        let addcoupon = [];
        let existingCoupons = 0;
        
        console.log(`[查券入库] 开始检查券重复性...`);
        for (let i = 0; i < coupon.length; i++) {
            try {
                const sqls = "SELECT * FROM mdl_coupon WHERE COUPON_CODE = ?";
                const result = await utils.dbUse(sqls, [coupon[i].code]);
                if (result.length == 0) {
                    addcoupon.push(coupon[i]);
                } else {
                    existingCoupons++;
                }
            } catch (error) {
                console.error(`[查券入库] 检查券 ${coupon[i].code} 时出错:`, error.message);
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                    [utils.createId(), log_id, `检查券 ${coupon[i].code} 时出错: ${error.message}`, sid, 0]);
            }
        }
        
        console.log(`[查券入库] 券检查完成 - 新券: ${addcoupon.length}, 已存在: ${existingCoupons}`);
        
        // 批量插入新券
        if (addcoupon.length > 0) {
            console.log(`[查券入库] 开始插入 ${addcoupon.length} 个新券...`);
            
            for (let i = 0; i < addcoupon.length; i++) {
                try {
                    const isLast = i === addcoupon.length - 1;
                    const couponData = [
                        utils.createId(),
                        addcoupon[i].id,
                        addcoupon[i].code,
                        addcoupon[i].title,
                        addcoupon[i].bigImage,
                        addcoupon[i].tradeStartDate,
                        addcoupon[i].tradeEndDate,
                        addcoupon[i].sid,
                        classify,
                        addcoupon[i].promotionId,
                        +new Date(addcoupon[i].tradeEndDate),
                        addcoupon[i].currentDayAvailableCount == 999 ? 1 : addcoupon[i].currentDayAvailableCount,
                        addcoupon[i].totalCount == 999 ? 1 : addcoupon[i].totalCount,
                        addcoupon[i].totalAvailableCount == 999 ? 1 : addcoupon[i].totalAvailableCount
                    ];
                    
                    const valueStr = `('${couponData.join("','")}')`;
                    couponStr += isLast ? valueStr + ';' : valueStr + ',';
                    
                } catch (error) {
                    console.error(`[查券入库] 处理券 ${addcoupon[i].code} 数据时出错:`, error.message);
                }
            }
            
            if (couponStr) {
                try {
                    const addcouponSql = 'INSERT INTO mdl_coupon (ID, COUPON_ID, COUPON_CODE, COUPON_TITLE, BIG_IMAGE, TRADE_START_DATE, TRADE_END_DATE, COUPON_SID_ID, CLASSIFYID,PROMOTION_ID,LOGTIME,CURRENT_DAY_AVAILABLE_COUNT,TOTAL_COUNT,TOTAL_AVAILABLE_COUNT) VALUES' + couponStr;
                    await utils.dbUse(addcouponSql);
                    console.log(`[查券入库] 成功插入 ${addcoupon.length} 个券`);
                    
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                        [utils.createId(), log_id, `成功入库 ${addcoupon.length} 个券`, sid, 1]);
                } catch (error) {
                    console.error(`[查券入库] 批量插入券时出错:`, error.message);
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                        [utils.createId(), log_id, `券入库失败: ${error.message}`, sid, 0]);
                    throw error;
                }
            }
        } else {
            console.log(`[查券入库] 没有新券需要入库`);
        }
        
        // 处理商品信息
        let foodList = [];
        let processedFoods = 0;
        let newFoods = 0;
        
        console.log(`[查券入库] 开始处理商品信息...`);
        
        // 根据id重复去重
        const set = new Set();
        const array = findFood.filter(item => {
            return !set.has(item.id) && set.add(item.id);
        });
        
        console.log(`[查券入库] 去重后需要处理 ${array.length} 个商品`);
        
        for (let i = 0; i < array.length; i++) {
            try {
                console.log(`[查券入库] 处理商品 ${i + 1}/${array.length}: ${array[i].title}`);
                const prouctArr = await findCouponFoodMethod(array[i], array[i].sid);
                foodList = [...foodList, ...prouctArr];
                processedFoods++;
            } catch (error) {
                console.error(`[查券入库] 处理商品 ${array[i].title} 时出错:`, error.message);
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                    [utils.createId(), log_id, `处理商品 ${array[i].title} 时出错: ${error.message}`, sid, 0]);
            }
        }
        
        console.log(`[查券入库] 商品处理完成 - 处理: ${processedFoods}, 获得商品: ${foodList.length}`);
        
        // 批量插入新商品
        if (foodList.length > 0) {
            console.log(`[查券入库] 开始检查和插入商品...`);
            
            for (let i = 0; i < foodList.length; i++) {
                try {
                    const sql = "select * from mdl_product where PRODUCT_CODE = ? AND FOOD_TITLE = ?";
                    const findFlag = await utils.dbUse(sql, [foodList[i].productCode, foodList[i].productName]);
                    
                    if (findFlag.length == 0) {
                        const addsqlnew = ` INSERT INTO mdl_product(ID, PRODUCT_CODE, IMAGE, DEFAULT_COMBOITEMS, PRICE_INFO, TYPE, CREATE_DATE,FOOD_TITLE,COUPON_ID)  VALUES (?,?,?,?,?,?,?,?,?) `;
                        await utils.dbUse(addsqlnew, [
                            utils.createId(),
                            foodList[i].productCode,
                            foodList[i].productImage || '无数据',
                            foodList[i].defaultComboItems || '无数据',
                            foodList[i].priceInfo ? foodList[i].priceInfo.discountPriceText : "无数据",
                            1,
                            utils.createTime(),
                            foodList[i].productName || "无数据",
                            foodList[i].couponID
                        ]);
                        newFoods++;
                        
                        if (newFoods % 10 === 0) {
                            console.log(`[查券入库] 已插入 ${newFoods} 个新商品...`);
                        }
                    }
                } catch (error) {
                    console.error(`[查券入库] 插入商品 ${foodList[i].productName} 时出错:`, error.message);
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                        [utils.createId(), log_id, `插入商品 ${foodList[i].productName} 时出错: ${error.message}`, sid, 0]);
                }
            }
            
            console.log(`[查券入库] 商品入库完成 - 新增: ${newFoods}`);
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`[查券入库] 处理完成 - SID: ${sid}, 耗时: ${duration}ms, 新券: ${addcoupon.length}, 新商品: ${newFoods}`);
        
        // 记录完成日志
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
            [utils.createId(), log_id, `查券入库完成 - 新券: ${addcoupon.length}, 新商品: ${newFoods}, 耗时: ${duration}ms`, sid, 1]);
        
        res.json({
            code: 200,
            data: {
                message: "查券入库成功",
                summary: {
                    newCoupons: addcoupon.length,
                    existingCoupons: existingCoupons,
                    newProducts: newFoods,
                    processedProducts: processedFoods,
                    duration: duration
                }
            }
        });
        
    } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.error(`[查券入库] 处理失败:`, error.message);
        console.error(`[查券入库] 错误堆栈:`, error.stack);
        
        // 记录错误日志
        try {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                [utils.createId(), log_id, `查券入库失败: ${error.message}, 耗时: ${duration}ms`, req.body.sid || 'unknown', 0]);
        } catch (logError) {
            console.error(`[查券入库] 记录错误日志失败:`, logError.message);
        }
        
        res.json({
            code: 500,
            msg: "查券入库失败",
            error: error.message
        });
    }

})
//添加字典项目
/**
 * @swagger
 * /admin/addDictItem:
 *   post:
 *     summary: 添加字典项目
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addDictItem", async (req, res) => {
    let sql = "INSERT INTO dict_item(dict_id,dict_label,dict_value,dict_sort,dict_class,status,create_time,remark) VALUES (?,?,?,?,?,?,?,?)", obj = req.body;
    pool.query(sql, [obj.dictId, obj.dictLabel, obj.dictValue, obj.dictSort, obj.dictClass, obj.status, new Date(), obj.remark], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//获取余额
// 获取API基础URL
function getApiBaseUrl() {
    if (process.env.MCD_USE_LOCAL_PROXY === 'true') {
        return process.env.MCD_LOCAL_PROXY_URL || 'http://mcd-api-server:9527';
    }
    return process.env.MCD_EXTERNAL_API_URL || 'http://server.pqkap.com';
}

// 查询用户钱包余额函数
async function getWalletBalance(sid) {
    try {
        const axios = require('axios');
        const result = await axios.get(`${getApiBaseUrl()}/user/wallet/balance?debug_sid=${sid}`, {
            timeout: 5000 // 5秒超时
        });
        return result.data;
    } catch (error) {
        console.error('获取钱包余额失败:', error.message);
        // 返回默认格式，避免上层代码出错
        return {
            code: 500,
            msg: '余额服务暂时不可用',
            data: { balance: '0.00' }
        };
    }
}


// 新增获取余额路由
/**
 * @swagger
 * /admin/getWalletBalance:
 *   post:
 *     summary: 获取余额
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sid:
 *                 type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     balance:
 *                       type: string
 *                     updateTime:
 *                       type: string
 *       400:
 *         description: 缺少必要参数
 */
router.post("/getWalletBalance", async (req, res) => {
    const { sid } = req.body;
    if (!sid) {
        return res.json(utils.returnData({ code: 400, msg: "缺少必要参数sid" }));
    }
    
try {
    // 直接调用API获取钱包余额
    const walletBalance = await getWalletBalance(sid);
    if (walletBalance.code === 200) {
        // 提取并验证余额数据
        let balance = walletBalance.data?.balance;

        // 数据验证和格式化
        if (balance === null || balance === undefined || balance === '') {
            balance = '0.00';
        } else if (typeof balance === 'string') {
            // 尝试转换字符串为数字
            const numericBalance = parseFloat(balance);
            if (!isNaN(numericBalance)) {
                balance = numericBalance.toFixed(2);
            } else {
                balance = '0.00'; // 无效字符串设为0
            }
        } else if (typeof balance === 'number') {
            balance = balance.toFixed(2);
        } else {
            balance = '0.00'; // 其他类型设为0
        }

        // 将余额写入数据库mdl_sid表
        await utils.dbUse(
            "UPDATE mdl_sid SET WALLET_BALANCE = ? WHERE SID = ?",
            [balance, sid]
        );

        return res.json(utils.returnData({
            code: 200,
            data: {
                balance: balance,
                updateTime: walletBalance.data?.updateTime
            },
            msg: "余额已写入数据库"
        }));
    }

    // 外部服务返回错误时，返回默认值
    return res.json(utils.returnData({
        code: 200,
        data: { balance: '0.00' },
        msg: "外部余额服务暂时不可用，显示默认余额"
    }));

} catch (error) {
    console.error('获取余额接口异常:', error);
    // 当外部服务不可用时，返回默认值
    return res.json(utils.returnData({
        code: 200,
        data: { balance: '0.00' },
        msg: "外部余额服务暂时不可用，显示默认余额"
    }));
}
});


/**
 * 添加普通账号
 * @route POST /admin/addSid
 * @group 账号
 * @param {string} classify.body.required - 分类ID
 * @param {string} sid_list.body.required - 账号列表（逗号分隔）
 * @param {number} qy.body.optional - 权益标识
 * @returns {object} 200 - 添加结果
 */
/**
 * @swagger
 * /admin/addSid:
 *   post:
 *     summary: 添加普通账号
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               classify:
 *                 type: string
 *               sid_list:
 *                 type: string
 *               qy:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 msg:
 *                   type: string
 *                   example: 账号上传完成
 *       400:
 *         description: 缺少必要参数
 *       500:
 *         description: 服务器错误
 */
router.post("/addSid", async (req, res) => {
        let classify = req.body.classify;
        let sid_list = req.body.sid_list;
    let qy = req.body.qy;
        if (!classify || !sid_list) {
            return res.send(utils.returnData({ code: -1, msg: "请输入账号和分类", req }));
        }
        
        // 确保sid_list是字符串类型
        if (typeof sid_list !== 'string') {
            return res.send(utils.returnData({ code: -1, msg: "账号列表格式错误", req }));
        }
        
    let arr1 = utils.getSidReturnArr(sid_list)
    //开始记录日志 创建 mdl_log表
    let log_id = utils.createId();//日志id
    let log_strt_sql = "INSERT INTO mdl_log(ID,LOG_TIME,TYPE,TITLE) VALUES (?,?,?,?)"
    await utils.dbUse(log_strt_sql, [log_id, utils.createTime(), 1, arr1.length + '个账号上传'])
    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `上传${arr1.length}个账号,进行去重`, '', 1])
    // 根据id重复去重
    let arr = [...new Set(arr1)];
    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `账号去重后余${arr.length}个账号`, '', 1])
    //依次请求查券接口
    let sidArr = []//sid以及用户信息
    let coupon = []//需要存的所有券
    let findFood = []//需要查商品的券
    let foodList = [] //所有查出来的商品
    let arrayClass = utils.splitArrayIntoChunks(arr, cpuMuns)
    if (arr.length > 50) {
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `上传数量可开启多进程,当前服务器最大限开启${cpuMuns}个进程`, '', 1])
        
        // 创建Promise数组来等待所有子进程完成
        let childPromises = []
        
        for (let i = 0; i < cpuMuns; i++) {
            if (arrayClass[i] && arrayClass[i].length > 0) {
                let childPromise = new Promise((resolve, reject) => {
                    let child = child_process.fork(path.resolve(__dirname, './child.js'))
                    
                    child.on('message', async (data) => {
                        console.log("子文件传来的数据")
                        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `${data.msg},${data.sid}个账号,${data.coupon}个优惠券,${data.food}个商品,已入库`, '', 1])
                        child.kill() // 关闭子进程
                        resolve(data)
                    })
                    
                    child.on('error', (error) => {
                        console.error('子进程错误:', error)
                        child.kill()
                        reject(error)
                    })
                    
                    child.on('exit', (code) => {
                        if (code !== 0) {
                            console.error(`子进程退出，退出码: ${code}`)
                        }
                    })
                    
                    child.send({
                        data: arrayClass[i],
                        classify,
                        log_id,
                        num: i,
                        qy: qy
                    })
                })
                childPromises.push(childPromise)
                }
            }
            
        try {
            // 等待所有子进程完成
            await Promise.all(childPromises)
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `所有子进程处理完成`, '', 1])
    } catch (error) {
            console.error('子进程处理出错:', error)
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `子进程处理出错: ${error.message}`, '', 0])
        }
        
        return res.send(utils.returnData({ code: 1, msg: "账号上传完成", req }))
    } else {
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `上传数量较少,无需开启多进程`, '', 1])
        for (let i = 0; i < arr.length; i++) {
            //每个sid进入查券
            let couponFindFood = await getMdlUserInfoMethod(arr[i], log_id)
            if (couponFindFood) {
                coupon = [...coupon, ...couponFindFood.coupon]
                findFood = [...findFood, ...couponFindFood.findFood]
                sidArr = [...sidArr, ...couponFindFood.sidInfoArr]
            }

        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `验证账号信息，查券，查商品已处理完毕，共${sidArr.length}个账号,${coupon.length}个优惠券,查出${findFood.length}个商品,商品进入去重`, '', 1])
        // 根据id重复去重
        const set = new Set();
        let array = findFood.filter(item => {
            return !set.has(item.id) && set.add(item.id)
        })
        for (let i = 0; i < array.length; i++) {
            let prouctArr = await findCouponFoodMethod(array[i], array[i].sid)
            foodList = [...foodList, ...prouctArr]
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `商品自动获取上传共${foodList.length}个`, '', 1])

        if (sidArr.length == 0) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `总计 ：可用账号0个`, '', 0])
            
            // 查询失败原因，提供更准确的错误信息
            let failureReasons = await utils.dbUse("SELECT MESSAGE FROM mdl_log_data WHERE LOG_ID = ? AND TYPE = 0 AND MESSAGE LIKE '%未登录%'", [log_id])
            let errorMsg = "没有可用账号";
            
            if (failureReasons.length > 0) {
                errorMsg = "账号验证失败：所有账号均未登录，请重新获取有效的登录凭证";
            } else {
                // 检查是否有其他类型的错误
                let otherErrors = await utils.dbUse("SELECT MESSAGE FROM mdl_log_data WHERE LOG_ID = ? AND TYPE = 0 LIMIT 1", [log_id])
                if (otherErrors.length > 0) {
                    errorMsg = "账号验证失败：" + otherErrors[0].MESSAGE.split(',')[1] || "账号不可用";
                }
            }

            return res.send(utils.returnData({ code: -1, msg: errorMsg, req }))
    }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `入库...`, '', 1])

        let str = ''
        for (let i = 0; i < sidArr.length; i++) {
            if (i == sidArr.length - 1) {
                str += `('${utils.createId()}','${sidArr[i].sid}','${classify}','${utils.createTime()}','${sidArr[i].userInfo.phoneNumber}','${sidArr[i].userInfo.meddyId}','${sidArr[i].userInfo.availablePoints}','${sidArr[i].userInfo.walletBalance}','${qy}');`
            } else {
                str += `('${utils.createId()}','${sidArr[i].sid}','${classify}','${utils.createTime()}','${sidArr[i].userInfo.phoneNumber}','${sidArr[i].userInfo.meddyId}','${sidArr[i].userInfo.availablePoints}','${sidArr[i].userInfo.walletBalance}','${qy}'),`
            }
        }
        let addSql = 'INSERT INTO mdl_sid (ID, SID, PARENT_MENU, CREATE_TIME,PHONE,MEDDYID,AVAILABLE_POINTS,WALLET_BALANCE,QY) VALUES ' + str
        utils.dbUse(addSql)
        let couponStr = ''
        console.log(coupon)
        for (let i = 0; i < coupon.length; i++) {
            if (i == coupon.length - 1) {
                couponStr += `('${utils.createId()}','${coupon[i].id}','${coupon[i].code}','${coupon[i].title}','${coupon[i].bigImage}','${coupon[i].tradeStartDate}','${coupon[i].tradeEndDate}','${coupon[i].sid}','${classify}','${coupon[i].promotionId}','${+new Date(coupon[i].tradeEndDate)}','${coupon[i].currentDayAvailableCount == 999 ? 1 :coupon[i].currentDayAvailableCount}','${coupon[i].totalCount == 999 ? 1 :coupon[i].totalCount}','${coupon[i].totalAvailableCount == 999 ? 1 :coupon[i].totalAvailableCount}');`
            } else {
                couponStr += `('${utils.createId()}','${coupon[i].id}','${coupon[i].code}','${coupon[i].title}','${coupon[i].bigImage}','${coupon[i].tradeStartDate}','${coupon[i].tradeEndDate}','${coupon[i].sid}','${classify}','${coupon[i].promotionId}','${+new Date(coupon[i].tradeEndDate)}','${coupon[i].currentDayAvailableCount == 999 ? 1 :coupon[i].currentDayAvailableCount}','${coupon[i].totalCount == 999 ? 1 : coupon[i].totalCount}','${coupon[i].totalAvailableCount == 999 ? 1 :coupon[i].totalAvailableCount}'),`
                            }
        }
        let addcouponSql = 'INSERT INTO mdl_coupon (ID, COUPON_ID, COUPON_CODE, COUPON_TITLE, BIG_IMAGE, TRADE_START_DATE, TRADE_END_DATE, COUPON_SID_ID, CLASSIFYID,PROMOTION_ID,LOGTIME,CURRENT_DAY_AVAILABLE_COUNT,TOTAL_COUNT,TOTAL_AVAILABLE_COUNT) VALUES' + couponStr
        utils.dbUse(addcouponSql)
        let addFood = ''
        for (let i = 0; i < foodList.length; i++) {
            let sql = "select * from mdl_product where PRODUCT_CODE = ? AND FOOD_TITLE = ?"
            let findFlag = await utils.dbUse(sql, [foodList[i].productCode,foodList[i].productName])
            if(findFlag.length == 0){
                let addsqlnew = ` INSERT INTO mdl_product(ID, PRODUCT_CODE, IMAGE, DEFAULT_COMBOITEMS, PRICE_INFO, TYPE, CREATE_DATE,FOOD_TITLE,COUPON_ID)  VALUES (?,?,?,?,?,?,?,?,?) `
                utils.dbUse(addsqlnew,[utils.createId(),foodList[i].productCode,foodList[i].productImage ? foodList[i].productImage : '无数据',foodList[i].defaultComboItems ? foodList[i].defaultComboItems : '无数据',foodList[i].priceInfo ? foodList[i].priceInfo.discountPriceText : "无数据",1,utils.createTime(),foodList[i].productName ? foodList[i].productName : "无数据",foodList[i].couponID])
            }
            
            //原添加方式
            // if (i == foodList.length - 1) {
            //     addFood += `('${utils.createId()}','${foodList[i].productCode}','${foodList[i].productImage ? foodList[i].productImage : '无数据'}','${foodList[i].defaultComboItems ? foodList[i].defaultComboItems : '无数据'}','${foodList[i].priceInfo ? foodList[i].priceInfo.discountPriceText : "无数据"}','${1}','${utils.createTime()}','${foodList[i].productName ? foodList[i].productName : "无数据"}','${foodList[i].couponID}');`
            // } else {
            //     addFood += `('${utils.createId()}','${foodList[i].productCode}','${foodList[i].productImage ? foodList[i].productImage : '无数据'}','${foodList[i].defaultComboItems ? foodList[i].defaultComboItems : '无数据'}','${foodList[i].priceInfo ? foodList[i].priceInfo.discountPriceText : "无数据"}','${1}','${utils.createTime()}','${foodList[i].productName ? foodList[i].productName : "无数据"}','${foodList[i].couponID}'),`
            // }
        }
         //原添加方式
        // if (addFood) {
        //     let addFoodSql = 'INSERT INTO mdl_product (ID, PRODUCT_CODE, IMAGE, DEFAULT_COMBOITEMS, PRICE_INFO, TYPE, CREATE_DATE,FOOD_TITLE,COUPON_ID) VALUES ' + addFood
        //     utils.dbUse(addFoodSql)
        // }
        
            //进入保存权益
            for (let i = 0; i < sidArr.length; i++) {
                qxFind(sidArr[i].sid)
    }
    
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `账号上传处理完成`, '', 1])
    
        return res.send(utils.returnData({ code: 1, msg: "账号上传完成", req }))
    }
})
//添加账号分类
/**
 * @swagger
 * /admin/addClass:
 *   post:
 *     summary: 添加账号分类
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/addClass", async (req, res) => {
    let id = utils.createId()
    let sql = "INSERT INTO classify(ID,CLASSIFY_TITLE,CREAT_DATA) VALUES (?,?,?)", obj = { ID: id, CLASSIFY_TITLE: req.body.name, CREAT_DATA: utils.createTime() };
    await utils.existName({ sql: "SELECT ID FROM classify WHERE  CLASSIFY_TITLE=?", name: obj.CLASSIFY_TITLE, res, msg: "名称已存在！", req });
    pool.query(sql, [obj.ID, obj.CLASSIFY_TITLE, obj.CREAT_DATA], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
})
//获取抵用券
/**
 * @swagger
 * /admin/getcashCode:
 *   post:
 *     summary: 获取抵用券
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *               oderID:
 *                 type: string
 *               AVAILABLE_QUANTITY:
 *                 type: string
 *               useOder:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getcashCode", async (req, res) => {
    let obj = req.body;
    console.log(obj)
    let aca = obj.AVAILABLE_QUANTITY == '0' ? 0 :1
    let sql,sql1

        if(obj.useOder == '0' || obj.useOder == '1'){
        //增
        if(obj.AVAILABLE_QUANTITY == '0' || obj.AVAILABLE_QUANTITY == '1'){
            sql = `SELECT * FROM mdl_cash_coupon WHERE B_USE = ${obj.useOder == '1'?0:1} AND	AVAILABLE_QUANTITY = ? AND ORDER_ID LIKE "%${obj.oderID || ''}%" AND COUPON_CODE LIKE "%${obj.name || ''}%" LIMIT ?,?`;
            sql1 = `WHERE B_USE = ${obj.useOder == '1'?0:1} AND AVAILABLE_QUANTITY = ${obj.AVAILABLE_QUANTITY == '0' ? 0 :1} AND ORDER_ID LIKE "%${obj.oderID || ''}%"   AND COUPON_CODE LIKE "%${obj.name || ''}%"`
        }else{
            sql = `SELECT * FROM mdl_cash_coupon WHERE B_USE = ${obj.useOder == '1'?0:1} AND ORDER_ID LIKE "%${obj.oderID || ''}%" AND COUPON_CODE LIKE "%${obj.name || ''}%" LIMIT ?,?`;
            sql1 = `WHERE  B_USE = ${obj.useOder == '1'?0:1} AND ORDER_ID LIKE "%${obj.oderID || ''}%" AND COUPON_CODE LIKE "%${obj.name || ''}%"`
        }
        console.log(sql)
        let { total } = await utils.getSum({ name: "mdl_cash_coupon", where: sql1, res, req });
        let { page, size } = utils.pageSize(obj.page, obj.size);
        if(obj.AVAILABLE_QUANTITY == '0' || obj.AVAILABLE_QUANTITY == '1'){
            console.log("选2")
            pool.query(sql, [aca,page, size], (err, result) => {
                if (err) return res.send(utils.returnData({ code: -1, err, req }));
                res.send(utils.returnData({ data: result, total }));
            });
        }else{
            pool.query(sql, [page, size], (err, result) => {
                if (err) return res.send(utils.returnData({ code: -1, err, req }));
                res.send(utils.returnData({ data: result, total }));
            });
        }
        //增
        
    }else{
         if(obj.AVAILABLE_QUANTITY == '0' || obj.AVAILABLE_QUANTITY == '1'){
            console.log("选1")
            sql = `SELECT * FROM mdl_cash_coupon WHERE 	AVAILABLE_QUANTITY = ? AND ORDER_ID LIKE "%${obj.oderID || ''}%" AND COUPON_CODE LIKE "%${obj.name || ''}%" LIMIT ?,?`;
            sql1 = `WHERE AVAILABLE_QUANTITY = ${obj.AVAILABLE_QUANTITY == '0' ? 0 :1} AND ORDER_ID LIKE "%${obj.oderID || ''}%"   AND COUPON_CODE LIKE "%${obj.name || ''}%"`
        }else{
            sql = `SELECT * FROM mdl_cash_coupon WHERE  ORDER_ID LIKE "%${obj.oderID || ''}%" AND COUPON_CODE LIKE "%${obj.name || ''}%" LIMIT ?,?`;
            sql1 = `WHERE  ORDER_ID LIKE "%${obj.oderID || ''}%" AND COUPON_CODE LIKE "%${obj.name || ''}%"`
        }
    
        let { total } = await utils.getSum({ name: "mdl_cash_coupon", where: sql1, res, req });
        let { page, size } = utils.pageSize(obj.page, obj.size);
        if(obj.AVAILABLE_QUANTITY == '0' || obj.AVAILABLE_QUANTITY == '1'){
            console.log("选2")
            pool.query(sql, [aca,page, size], (err, result) => {
                if (err) return res.send(utils.returnData({ code: -1, err, req }));
                res.send(utils.returnData({ data: result, total }));
            });
        }else{
            pool.query(sql, [page, size], (err, result) => {
                if (err) return res.send(utils.returnData({ code: -1, err, req }));
                res.send(utils.returnData({ data: result, total }));
            });
        }
    }
    
   
    
})
//获取分类列表
/**
 * @swagger
 * /admin/getClass:
 *   post:
 *     summary: 获取分类列表
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getClass", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT * FROM classify WHERE CLASSIFY_TITLE LIKE "%${obj.name || ''}%" LIMIT ?,?`;
    let { total } = await utils.getSum({ name: "classify", where: `WHERE CLASSIFY_TITLE LIKE "%${obj.name || ''}%"`, res, req });
    let { page, size } = utils.pageSize(obj.page, obj.size);
    pool.query(sql, [page, size], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result, total }));
    });
})
//获取日志列表
/**
 * @swagger
 * /admin/getLog:
 *   post:
 *     summary: 获取日志列表
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getLog", async (req, res) => {
    let obj = req.body;
    //定义sql语句：查询mdl_log表，并用mdl_log表查出来的每一条数据id查询mdl_log_data表数据
    let sql = `SELECT * FROM mdl_log WHERE TITLE LIKE "%${obj.name || ''}%" ORDER BY LOG_TIME  DESC LIMIT ?,?`;
    let { total } = await utils.getSum({ name: "mdl_log", where: `WHERE TITLE LIKE "%${obj.name || ''}%"`, res, req });
    let { page, size } = utils.pageSize(obj.page, obj.size);
    pool.query(sql, [page, size], async (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        for(let i = 0 ; i <result.length;i++){
            let sqllog = `SELECT * FROM mdl_log_data  WHERE TYPE = 0 AND LOG_ID = ?` ;
            let err = await utils.dbUse(sqllog,[result[i].ID])
          
            result[i].err = err.length
        }
        res.send(utils.returnData({ data: result, total }));
    });
})
//获取系统配置信息
/**
 * @swagger
 * /admin/getOption:
 *   post:
 *     summary: 获取系统配置信息
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/getOption", async (req, res) => {
    let sql = "SELECT * FROM config_data";
    pool.query(sql, (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result[0] }));
    })
})
//获取商品列表
/**
 * @swagger
 * /admin/getFood:
 *   post:
 *     summary: 获取商品列表
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getFood", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT * FROM mdl_product WHERE FOOD_TITLE LIKE "%${obj.name || ''}%"  LIMIT ?,?`;
    let { total } = await utils.getSum({ name: "mdl_product", where: `WHERE FOOD_TITLE LIKE "%${obj.name || ''}%"`, res, req });
    let { page, size } = utils.pageSize(obj.page, obj.size);
    pool.query(sql, [page, size], async(err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        //增加显示券的次数
        for(let i = 0 ; i <result.length;i++){
            if(result[i].COUPON_ID){
                //查询次数卡
                let findCountsql = 'SELECT * FROM mdl_coupon WHERE COUPON_ID = ?'
                let couponNum =  await utils.dbUse(findCountsql,[result[i].COUPON_ID])
                //将所有查出来的券的可用数量加起来
                let allNum  = 0 // 总剩余的可用次数
                let dayAllNum  = 0 // 总今日可用次数
                for(let k = 0 ; k <couponNum.length ; k++){
                    dayAllNum = parseInt(couponNum[k].CURRENT_DAY_AVAILABLE_COUNT) +dayAllNum
                    allNum = parseInt(couponNum[k].TOTAL_AVAILABLE_COUNT) + allNum
                }
                result[i].allNum = allNum
                result[i].dayAllNum = dayAllNum
            }
        }
        res.send(utils.returnData({ data: result, total }));
    });
})
//获取日志详情
/**
 * @swagger
 * /admin/getLogs:
 *   post:
 *     summary: 获取日志详情
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: token无效
 */
router.post("/getLogs", async (req, res) => {
    let obj = req.body;
    //定义sql语句：查询mdl_log表，并用mdl_log表查出来的每一条数据id查询mdl_log_data表数据
    let sql = `SELECT * FROM mdl_log_data  WHERE LOG_ID =?`;

    pool.query(sql, [obj.id], async (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send({
            code: 200,
            data: result
        });
    });
})
//获取优惠券
/**
 * @swagger
 * /admin/getCouponList:
 *   post:
 *     summary: 获取优惠券列表
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 description: 页码
 *                 example: 1
 *               size:
 *                 type: integer
 *                 description: 每页数量
 *                 example: 10
 *               name:
 *                 type: string
 *                 description: 优惠券标题或SID
 *                 example: ""
 *               status:
 *                 type: string
 *                 description: 优惠券状态 (available, used, expired)
 *                 enum: [available, used, expired]
 *                 example: ""
 *               sortBy:
 *                 type: string
 *                 description: 排序字段 (LOGTIME, TRADE_END_DATE, COUPON_TITLE, TOTAL_AVAILABLE_COUNT)
 *                 enum: [LOGTIME, TRADE_END_DATE, COUPON_TITLE, TOTAL_AVAILABLE_COUNT]
 *                 example: LOGTIME
 *               sortOrder:
 *                 type: string
 *                 description: 排序顺序 (ASC, DESC)
 *                 example: DESC
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       ID:
 *                         type: string
 *                       COUPON_TITLE:
 *                         type: string
 *                       COUPON_SID_ID:
 *                         type: string
 *                       COUPON_ID:
 *                         type: string
 *                       COUPON_CODE:
 *                         type: string
 *                       PROMOTION_ID:
 *                         type: string
 *                       TOTAL_COUNT:
 *                         type: integer
 *                       CURRENT_DAY_AVAILABLE_COUNT:
 *                         type: integer
 *                       TOTAL_AVAILABLE_COUNT:
 *                         type: integer
 *                       TRADE_START_DATE:
 *                         type: string
 *                         format: date-time
 *                       TRADE_END_DATE:
 *                         type: string
 *                         format: date-time
 *                       BIG_IMAGE:
 *                         type: string
 *                       CLASSIFYID:
 *                         type: string
 *                       LOGTIME:
 *                         type: string
 *                         format: date-time
 *                       status:
 *                         type: string
 *                         enum: [available, used, expired, unknown]
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 size:
 *                   type: integer
 *                 performance:
 *                   type: object
 *                   properties:
 *                     duration:
 *                       type: integer
 *                     recordCount:
 *                       type: integer
 *       401:
 *         description: token无效
 *       500:
 *         description: 服务器内部错误
 */
router.post("/getCouponList", async (req, res) => {
    const startTime = Date.now();
    
    try {
        let obj = req.body;
        const { page = 1, size = 10, name = '', status = '', sortBy = 'LOGTIME', sortOrder = 'ASC' } = obj;
        
        // 参数验证
        if (page < 1 || size < 1 || size > 1000) {
            return res.send(utils.returnData({ 
                code: -1, 
                message: '分页参数无效', 
                req 
            }));
        }
        
        // 构建WHERE条件
        let whereConditions = [];
        let queryParams = [];
        
        if (name) {
            whereConditions.push('(COUPON_TITLE LIKE ? OR COUPON_SID_ID LIKE ?)');
            queryParams.push(`%${name}%`, `%${name}%`);
        }
        
        if (status) {
            switch (status) {
                case 'available':
                    whereConditions.push('TOTAL_AVAILABLE_COUNT > 0');
                    break;
                case 'used':
                    whereConditions.push('TOTAL_AVAILABLE_COUNT = 0');
                    break;
                case 'expired':
                    whereConditions.push('TRADE_END_DATE < NOW()');
                    break;
            }
        }
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        
        // 验证排序字段
        const allowedSortFields = ['LOGTIME', 'TRADE_END_DATE', 'COUPON_TITLE', 'TOTAL_AVAILABLE_COUNT'];
        const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'LOGTIME';
        const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'ASC';
        
        // 构建查询SQL
        const sql = `
            SELECT 
                ID, COUPON_TITLE, COUPON_SID_ID, COUPON_ID, COUPON_CODE, 
                PROMOTION_ID, TOTAL_COUNT, CURRENT_DAY_AVAILABLE_COUNT, 
                TOTAL_AVAILABLE_COUNT, TRADE_START_DATE, TRADE_END_DATE, 
                BIG_IMAGE, CLASSIFYID, LOGTIME,
                CASE 
                    WHEN TOTAL_AVAILABLE_COUNT > 0 THEN 'available'
                    WHEN TOTAL_AVAILABLE_COUNT = 0 THEN 'used'
                    WHEN TRADE_END_DATE < NOW() THEN 'expired'
                    ELSE 'unknown'
                END as status
            FROM mdl_coupon 
            ${whereClause} 
            ORDER BY ${validSortBy} ${validSortOrder} 
            LIMIT ?, ?
        `;
        
        // 获取总数 - 构建完整的WHERE条件
        let countWhereClause = '';
        if (whereConditions.length > 0) {
            let countConditions = [];
            if (name) {
                countConditions.push(`(COUPON_TITLE LIKE '%${name}%' OR COUPON_SID_ID LIKE '%${name}%')`);
            }
            if (status) {
                switch (status) {
                    case 'available':
                        countConditions.push('TOTAL_AVAILABLE_COUNT > 0');
                        break;
                    case 'used':
                        countConditions.push('TOTAL_AVAILABLE_COUNT = 0');
                        break;
                    case 'expired':
                        countConditions.push('TRADE_END_DATE < NOW()');
                        break;
                }
            }
            countWhereClause = `WHERE ${countConditions.join(' AND ')}`;
        }
        
        const { total } = await utils.getSum({ 
            name: "mdl_coupon", 
            where: countWhereClause, 
            res, 
            req 
        });
        
        // 计算分页
        const { page: offset, size: limit } = utils.pageSize(page, size);
        const finalParams = [...queryParams, offset, limit];
        
        // 执行查询
        pool.query(sql, finalParams, (err, result) => {
            if (err) {
                console.error('getCouponList查询错误:', err);
                return res.send(utils.returnData({ 
                    code: -1, 
                    message: '查询失败', 
                    err, 
                    req 
                }));
            }
            
            // 添加性能日志
            const duration = Date.now() - startTime;
            console.log(`getCouponList查询完成，耗时: ${duration}ms, 返回${result.length}条记录`);
            
            // 返回结果
            res.send(utils.returnData({ 
                data: result, 
                total,
                page: parseInt(page),
                size: parseInt(size),
                performance: {
                    duration,
                    recordCount: result.length
                }
            }));
        });
        
    } catch (error) {
        console.error('getCouponList处理错误:', error);
        res.send(utils.returnData({ 
            code: -1, 
            message: '服务器内部错误', 
            error: error.message,
            req 
        }));
    }
})


//查询字典项目
/**
 * @swagger
 * /admin/getDictItem:
 *   post:
 *     summary: 查询字典项目
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dictId:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: token无效
 */
router.post("/getDictItem", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT a.id AS id,dict_id AS dictId,dict_label AS dictLabel,dict_value AS dictValue,dict_sort AS dictSort,dict_class AS dictClass,status,a.create_time AS dictItemCreateTime,a.remark AS remark,type FROM dict_item AS a LEFT JOIN dict b ON a.dict_id=b.id WHERE dict_id=? ORDER BY dict_sort ASC`;
    pool.query(sql, [obj.dictId], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//修改字典项目
/**
 * @swagger
 * /admin/upDictItem:
 *   post:
 *     summary: 修改字典项目
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *               dict_label:
 *                 type: string
 *               dict_value:
 *                 type: string
 *               dict_sort:
 *                 type: integer
 *               dict_class:
 *                 type: string
 *               status:
 *                 type: integer
 *               remark:
 *                 type: string
 *     responses:
 *       200:
 *         description: 修改成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/upDictItem", async (req, res) => {
    let obj = req.body;
    let sql = `UPDATE  dict_item SET dict_label=?,dict_value=?,dict_sort=?,dict_class=?,status=?,remark=? WHERE id=?`;
    pool.query(sql, [obj.dictLabel, obj.dictValue, obj.dictSort, obj.dictClass, obj.status, obj.remark, obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//删除字典项目
/**
 * @swagger
 * /admin/delDictItem:
 *   post:
 *     summary: 删除字典项目
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       401:
 *         description: token无效
 */
router.post("/delDictItem", async (req, res) => {
    let sql = "DELETE FROM dict_item WHERE id=?", obj = req.body;
    pool.query(sql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});

//根据类型查询字典项目
/**
 * @swagger
 * /admin/getDictType:
 *   post:
 *     summary: 根据类型查询字典项目
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: token无效
 */
router.post("/getDictType", async (req, res) => {
    let obj = req.body;
    let sql = `SELECT a.id AS id,dict_label AS dictLabel,dict_value AS dictValue,dict_sort AS dictSort,dict_class AS dictClass,a.remark AS remark,type FROM dict_item AS a LEFT JOIN dict b ON a.dict_id=b.id WHERE b.type=? AND a.status=1 ORDER BY dict_sort ASC`;
    pool.query(sql, [obj.type], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//获取数据可视化
/**
 * @swagger
 * /admin/getDataVisualization:
 *   post:
 *     summary: 获取数据可视化
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 OmaikaCount:
 *                   type: integer
 *                 zaoCount:
 *                   type: integer
 *                 offocCount:
 *                   type: integer
 *                 dineCount:
 *                   type: integer
 *                 nightCount:
 *                   type: integer
 *                 delCount:
 *                   type: integer
 *                 zao1Count:
 *                   type: integer
 *       401:
 *         description: token无效
 */
router.post("/getDataVisualization", async (req, res) => {
    //查询sid表中麦卡的账号
    let sql = `SELECT COUNT(*) AS Oma FROM mdl_sid WHERE MY_KA_USE >0 `
    let OmaikaCount = await utils.dbUse(sql)
    //麦卡可用:OmaikaCount[0].Oma
    //查询sid表中麦卡的账号
    let sql1 = `SELECT * FROM mdl_sid WHERE BREAKFAST_CARD_USE >0;`
    let zaoCount = await utils.dbUse(sql1)
    //早餐可用:zaoCount[0].zao
    let sql2 = `SELECT COUNT(*) AS offoc FROM mdl_sid WHERE COFFEE_USE = 1 `
    let offocCount = await utils.dbUse(sql2)
    //下午茶可用:offocCount[0].offoc
    let sql3 = `SELECT COUNT(*) AS dine FROM mdl_sid WHERE BENEFITS_USE > 0 `
    let dineCount = await utils.dbUse(sql3)
    //晚餐可用:dineCount[0].dine
    let sql4 = `SELECT COUNT(*) AS night FROM mdl_sid WHERE XINPINCHANG_USE >0 `
    let nightCount = await utils.dbUse(sql4)
    //夜宵可用:nightCount[0].night
    let sql5 = `SELECT COUNT(*) AS del FROM mdl_sid WHERE FREE_DELIVERY_USE >0`
    let delCount = await utils.dbUse(sql5)
    //免配送费:delCount[0].del
    let sql6 = `SELECT COUNT(*) AS zao1 FROM mdl_sid WHERE BREAKFAST_CARD_USE >0 `
    let zao1Count = await utils.dbUse(sql6)
    //早餐卡可用:zao1Count[0].zao1

    res.json({
        OmaikaCount: OmaikaCount[0].Oma,
        zaoCount: zaoCount.length,
        offocCount: offocCount[0].offoc,
        dineCount: dineCount[0].dine,
        nightCount: nightCount[0].night,
        delCount: delCount[0].del,
        zao1Count: zao1Count[0].zao1
    })
})
//查询优惠券可视化
/**
 * @swagger
 * /admin/getCouponVisualization:
 *   post:
 *     summary: 查询优惠券可视化
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       value:
 *                         type: integer
 *       401:
 *         description: token无效
 */
router.post("/getCouponVisualization", async (req, res) => {
    //查询优惠券可视化
    let sqlTitle = `SELECT c.COUPON_TITLE FROM mdl_coupon c GROUP BY c.COUPON_TITLE`
    let result = await utils.dbUse(sqlTitle)
    let resArr = []
    for (let i = 0; i < result.length; i++) {
        //查询每一个的数量
        let sqlCount = `SELECT COUNT(*) AS count FROM mdl_coupon c WHERE c.COUPON_TITLE = ?`
        let resultCount = await utils.dbUse(sqlCount, [result[i].COUPON_TITLE])
        resArr.push({
            name: result[i].COUPON_TITLE,
            value: resultCount[0].count
        })
    }
    res.json({
        code: 200,
        data: resArr
    })
})

// 更新账号启用/禁用状态
/**
 * @swagger
 * /admin/updateAccountEnabled:
 *   post:
 *     summary: 更新账号启用/禁用状态
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *               ENABLED:
 *                 type: integer
 *                 description: 1为启用，0为禁用
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 账号启用成功
 *       400:
 *         description: 参数不完整
 *       500:
 *         description: 服务器错误
 */
router.post("/updateAccountEnabled", async (req, res) => {
    try {
        const { ID, ENABLED } = req.body;
        
        if (!ID || ENABLED === undefined) {
            return res.json({
                code: 400,
                message: "参数不完整"
            });
        }
        
        // 更新数据库中的ENABLED字段
        const sql = 'UPDATE mdl_sid SET ENABLED = ? WHERE ID = ?';
        await utils.dbUse(sql, [ENABLED, ID]);
        
        res.json({
            code: 200,
            message: ENABLED === 1 ? "账号启用成功" : "账号禁用成功"
        });
    } catch (error) {
        console.error('更新账号启用状态失败:', error);
        res.json({
            code: 500,
            message: "服务器错误"
        });
    }
});

// 获取优惠券统计数据
/**
 * @swagger
 * /admin/getCouponStats:
 *   post:
 *     summary: 获取优惠券统计数据
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 优惠券标题或SID
 *                 example: ""
 *               status:
 *                 type: string
 *                 description: 优惠券状态 (available, used, expired)
 *                 enum: [available, used, expired]
 *                 example: ""
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalStats:
 *                       type: object
 *                       properties:
 *                         totalCount:
 *                           type: integer
 *                         availableCount:
 *                           type: integer
 *                         usedCount:
 *                           type: integer
 *                         expiredCount:
 *                           type: integer
 *                     nameStats:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           total:
 *                             type: integer
 *                           available:
 *                             type: integer
 *                           used:
 *                             type: integer
 *                 performance:
 *                   type: object
 *                   properties:
 *                     duration:
 *                       type: integer
 *       401:
 *         description: token无效
 *       500:
 *         description: 服务器内部错误
 */
router.post("/getCouponStats", async (req, res) => {
    const startTime = Date.now();
    
    try {
        const { name = '', status = '' } = req.body;
        
        // 构建WHERE条件
        let whereConditions = [];
        let queryParams = [];
        
        if (name) {
            whereConditions.push('(COUPON_TITLE LIKE ? OR COUPON_SID_ID LIKE ?)');
            queryParams.push(`%${name}%`, `%${name}%`);
        }
        
        if (status) {
            switch (status) {
                case 'available':
                    whereConditions.push('TOTAL_AVAILABLE_COUNT > 0');
                    break;
                case 'used':
                    whereConditions.push('TOTAL_AVAILABLE_COUNT = 0');
                    break;
                case 'expired':
                    whereConditions.push('TRADE_END_DATE < NOW()');
                    break;
            }
        }
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        
        // 获取总体统计
        const totalStatsSql = `
            SELECT 
                COUNT(*) as totalCount,
                SUM(CASE WHEN TOTAL_AVAILABLE_COUNT > 0 THEN 1 ELSE 0 END) as availableCount,
                SUM(CASE WHEN TOTAL_AVAILABLE_COUNT = 0 THEN 1 ELSE 0 END) as usedCount,
                SUM(CASE WHEN TRADE_END_DATE < NOW() THEN 1 ELSE 0 END) as expiredCount
            FROM mdl_coupon 
            ${whereClause}
        `;
        
        // 获取按券名称分组的统计
        const nameStatsSql = `
            SELECT 
                COUPON_TITLE as name,
                COUNT(*) as total,
                SUM(CASE WHEN TOTAL_AVAILABLE_COUNT > 0 THEN 1 ELSE 0 END) as available,
                SUM(CASE WHEN TOTAL_AVAILABLE_COUNT = 0 THEN 1 ELSE 0 END) as used
            FROM mdl_coupon 
            ${whereClause}
            GROUP BY COUPON_TITLE
            ORDER BY total DESC
            LIMIT 10
        `;
        
        // 执行查询
        const [totalStatsResult, nameStatsResult] = await Promise.all([
            new Promise((resolve, reject) => {
                pool.query(totalStatsSql, queryParams, (err, result) => {
                    if (err) reject(err);
                    else resolve(result[0]);
                });
            }),
            new Promise((resolve, reject) => {
                pool.query(nameStatsSql, queryParams, (err, result) => {
                    if (err) reject(err);
                    else resolve(result);
                });
            })
        ]);
        
        const duration = Date.now() - startTime;
        console.log(`getCouponStats查询完成，耗时: ${duration}ms`);
        
        res.send(utils.returnData({
            code: 200,
            data: {
                totalStats: totalStatsResult,
                nameStats: nameStatsResult
            },
            performance: {
                duration
            }
        }));
        
    } catch (error) {
        console.error('getCouponStats处理错误:', error);
        res.send(utils.returnData({
            code: -1,
            message: '服务器内部错误',
            error: error.message,
            req
        }));
    }
})


module.exports = router;
