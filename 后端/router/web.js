const express = require("express");
const router = express.Router();
const { pool } = require("../pool.js");
const utils = require("../utils/index.js");
const { GetNearbyStoresBasedOnCoordinatesMy, CityAcquisitionStoreMy, GetLTCity, StoreCodeGetRestaurantDetailsMy, commodityCetailsMy, clearCartSid, addCartSid, getCartInfo, makeOrderDirectly, getUserCouponInfo, doExchangeCoupon, preOrder, payByBallance,cancelOder, getOrderDetail, return_order, storeIdGetShop, getCouponXq, getZaoCaoCanList, couponListAddCart, getServerNotification } = require("../config/request_api_config.js");
const { json } = require("body-parser");
const redis = require("../utils/redis.js");
const { qxFind } = require("../utils/findCouponFood.js");
const { couponAdjustment, specialHandling9900009274, specialHandling9900006034 } = require("../config/repairFile.js")
const optimizedCouponLogic = require('../utils/optimized_coupon_logic.js');
const { errLog } = require("../utils/err.js");
//获取服务通知
/**
 * @swagger
 * tags:
 *   name: Web
 *   description: 用户前端相关接口
 */

/**
 * @swagger
 * /web/getServerNotification:
 *   get:
 *     summary: 获取服务通知
 *     tags: [Web]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: sid
 *         schema:
 *           type: string
 *         description: SID
 *     responses:
 *       200:
 *         description: 成功获取服务通知
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.get("/getServerNotification", async (req, res) => {
    let { page, sid } = req.query
    let result = await getServerNotification(page, sid)
    res.json(result)
})
//获取时间段门店菜单
/**
 * @swagger
 * /web/getTimeData:
 *   post:
 *     summary: 获取时间段门店菜单
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 description: 日期
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *               time:
 *                 type: string
 *                 description: 时间
 *     responses:
 *       200:
 *         description: 成功获取门店菜单
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 参数错误或服务器错误
 */
router.post("/getTimeData", async (req, res) => {
    let { date, storeCode, time } = req.body
    if (!date || !storeCode || !time) {
        res.json({
            code: 500,
            msg: "参数错误"
        })
    }
    let result = await getZaoCaoCanList(date, storeCode, time)
    res.json(result)
})

//更新可用券数量
/**
 * @swagger
 * /web/updateCoupon:
 *   post:
 *     summary: 更新可用券数量
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sid:
 *                 type: string
 *                 description: SID
 *     responses:
 *       200:
 *         description: 优惠券更新完成
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 优惠券更新完成
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalCoupons:
 *                       type: integer
 *                     processedCount:
 *                       type: integer
 *                     deletedCount:
 *                       type: integer
 *                     errorCount:
 *                       type: integer
 *       400:
 *         description: 参数缺失
 *       500:
 *         description: 服务器内部错误
 */
router.post("/updateCoupon", async (req, res) => {
    try {
        const { sid } = req.body;
        
        // 参数验证
        if (!sid) {
            return res.json({
                code: 400,
                message: "参数缺失：sid不能为空"
            });
        }

        // 获取所有券
        const getCouponsSQL = `SELECT * FROM mdl_coupon WHERE COUPON_SID_ID = ?`;
        const couponList = await utils.dbUse(getCouponsSQL, [sid]);
        
        if (!couponList || couponList.length === 0) {
            return res.json({
                code: 200,
                message: "没有找到相关优惠券",
                deletedCount: 0
            });
        }

        console.log(`开始处理优惠券更新 - SID: ${sid}, 总券数: ${couponList.length}`);

        // 收集需要删除的券ID
        const couponIdsToDelete = [];
        let processedCount = 0;
        let errorCount = 0;

        // 优化：增加并发数量，减少HTTP请求超时时间
        const batchSize = 20; // 增加批处理大小
        const requestTimeout = 3000; // 3秒超时
        
        for (let i = 0; i < couponList.length; i += batchSize) {
            const batch = couponList.slice(i, i + batchSize);
            console.log(`处理批次 ${Math.floor(i/batchSize) + 1}/${Math.ceil(couponList.length/batchSize)}, 券数: ${batch.length}`);
            
            const promises = batch.map(async (coupon) => {
                try {
                    // 添加超时控制
                    const timeoutPromise = new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Request timeout')), requestTimeout)
                    );
                    
                    const requestPromise = getCouponXq(coupon.COUPON_CODE, sid, coupon.COUPON_ID);
                    const result = await Promise.race([requestPromise, timeoutPromise]);
                    
                    processedCount++;
                    
                    if (result.code === 200 && 
                        result.data && 
                        result.data.coupon && 
                        result.data.coupon.currentDayAvailableCount === 0) {
                        couponIdsToDelete.push(coupon.ID);
                    }
                } catch (error) {
                    console.error(`检查优惠券失败 - ID: ${coupon.ID}, Code: ${coupon.COUPON_CODE}`, error.message);
                    errorCount++;
                    // 超时或网络错误的券也标记为删除（可选策略）
                    if (error.message === 'Request timeout' || error.code === 'ECONNRESET') {
                        couponIdsToDelete.push(coupon.ID);
                    }
                }
            });
            
            await Promise.all(promises);
            
            // 添加批次间的短暂延迟，避免服务器过载
            if (i + batchSize < couponList.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // 批量删除无效券
        let deletedCount = 0;
        if (couponIdsToDelete.length > 0) {
            const placeholders = couponIdsToDelete.map(() => '?').join(',');
            const deleteCouponsSQL = `DELETE FROM mdl_coupon WHERE ID IN (${placeholders})`;
            const deleteResult = await utils.dbUse(deleteCouponsSQL, couponIdsToDelete);
            deletedCount = deleteResult.affectedRows || couponIdsToDelete.length;
        }

        console.log(`优惠券更新完成 - SID: ${sid}, 处理: ${processedCount}, 删除: ${deletedCount}, 错误: ${errorCount}`);

        res.json({
            code: 200,
            message: "优惠券更新完成",
            data: {
                totalCoupons: couponList.length,
                processedCount,
                deletedCount,
                errorCount
            }
        });
        
    } catch (error) {
        console.error('更新优惠券时发生错误:', error);
        res.json({
            code: 500,
            message: "服务器内部错误",
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
})
//根据门店id搜索门店
/**
 * @swagger
 * /web/storeIdGetShops:
 *   post:
 *     summary: 根据门店id搜索门店
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               cityId:
 *                 type: string
 *                 description: 城市ID
 *               keyword:
 *                 type: string
 *                 description: 搜索关键词
 *               pageNo:
 *                 type: integer
 *                 description: 页码
 *               pageSize:
 *                 type: integer
 *                 description: 每页数量
 *     responses:
 *       200:
 *         description: 成功获取门店列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/storeIdGetShops", async (req, res) => {
    let { cityId, keyword, pageNo, pageSize } = req.body
    let result = await storeIdGetShop(cityId, keyword, pageNo, pageSize)
    res.json(result)
})
//验证兑换码
/**
 * @swagger
 * /web/vft:
 *   post:
 *     summary: 验证兑换码
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 兑换码
 *     responses:
 *       200:
 *         description: 验证成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 验证成功
 *                 data:
 *                   type: object
 *       201:
 *         description: 兑换码下单中
 *       500:
 *         description: 兑换码不存在、已失效、已使用或服务器错误
 */
router.post("/vft", async (req, res) => {
    if (!req.body.code) { return res.json({ code: 500, message: "兑换码不能为空" }) }
    let sql = `SELECT * FROM mdl_code WHERE CODE = ?`;
    let result = await utils.dbUse(sql, [req.body.code]);
    if (!result[0]) { return res.json({ code: 500, message: "兑换码不存在" }) }
    if (result[0].CODE_STATUS === 0) { return res.json({ code: 200, message: "验证成功", data: result[0] }) }
    if (result[0].CODE_STATUS === 1) { return res.json({ code: 500, message: "兑换码已失效" }) }
    //状态等于2 代表已使用，需要返回订单
    if (result[0].CODE_STATUS === 2) { return res.json({ code: 500, message: "兑换码已使用" }) }
    //状态等于3 代表下单中，需要返回订单
    if (result[0].CODE_STATUS === 3) {
        //查询套餐
        let returnResilt = []
        let sql2 = `SELECT * FROM mdl_order WHERE CODE = ?`;
        let oderList = await utils.dbUse(sql2, [req.body.code]);
        for (let i = 0; i < oderList.length; i++) {

            let oder = await getOrderDetail(oderList[i].ODER_ID, oderList[i].OPEN_ID)
            if (oder.data.data) {
                const { customerServiceUrl, ...filteredData } = oder.data.data;
                returnResilt.push(filteredData);
            } else {
                returnResilt.push(oder.data.data); // 无数据时直接保留原始值避免异常
            }
        }
        res.json({ code: 201, data: returnResilt })
    }
});
//根据经纬度获取附近门店
/**
 * @swagger
 * /web/getNearbyStoresBasedOnCoordinates:
 *   post:
 *     summary: 根据经纬度获取附近门店
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               latitude:
 *                 type: number
 *                 format: float
 *                 description: 纬度
 *               longitude:
 *                 type: number
 *                 format: float
 *                 description: 经度
 *     responses:
 *       200:
 *         description: 成功获取附近门店
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 经纬度不能为空或服务器错误
 */
router.post("/getNearbyStoresBasedOnCoordinates", async (req, res) => {
    let { latitude, longitude } = req.body;
    if (!latitude || !longitude) { return res.json({ code: 500, message: "经纬度不能为空" }) }
    let result = await GetNearbyStoresBasedOnCoordinatesMy(latitude, longitude)
    res.json(result)
})
//根据城市code获取门店
/**
 * @swagger
 * /web/getCityByLocation:
 *   post:
 *     summary: 根据城市code获取门店
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               cityCode:
 *                 type: string
 *                 description: 城市代码
 *               pageNo:
 *                 type: integer
 *                 description: 页码
 *               pageSize:
 *                 type: integer
 *                 description: 每页数量
 *     responses:
 *       200:
 *         description: 成功获取门店列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 参数不能为空或服务器错误
 */
router.post("/getCityByLocation", async (req, res) => {
    let { cityCode, pageNo, pageSize } = req.body;
    if (!cityCode || !pageNo || !pageSize) { return res.json({ code: 500, message: "参数不能为空" }) }
    let result = await CityAcquisitionStoreMy(cityCode, pageNo, pageSize)
    res.json(result)
})
//根据经纬度获取城市
/**
 * @swagger
 * /web/GetCitiesBasedOnLocation:
 *   post:
 *     summary: 根据经纬度获取城市
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               latitude:
 *                 type: number
 *                 format: float
 *                 description: 纬度
 *               longitude:
 *                 type: number
 *                 format: float
 *                 description: 经度
 *     responses:
 *       200:
 *         description: 成功获取城市信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/GetCitiesBasedOnLocation", async (req, res) => {
    let { latitude, longitude } = req.body;
    let result = await GetLTCity(latitude, longitude)
    res.json(result)
})
//根据门店信息获取菜单
/**
 * @swagger
 * /web/getMenu:
 *   post:
 *     summary: 根据门店信息获取菜单
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dayPartCode:
 *                 type: string
 *                 description: 日期部分代码
 *               orderType:
 *                 type: string
 *                 description: 订单类型
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *     responses:
 *       200:
 *         description: 成功获取菜单
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 参数错误或服务器错误
 */
router.post("/getMenu", async (req, res) => {
    let { dayPartCode, orderType, storeCode } = req.body
    if (!dayPartCode || !orderType || !storeCode) { return res.json({ code: 500, messge: "参数错误" }) }
    let result = await StoreCodeGetRestaurantDetailsMy(dayPartCode, orderType, storeCode)
    res.json(result)
})
//根据兑换码获取套餐信息
/**
 * @swagger
 * /web/getCashCouponList:
 *   post:
 *     summary: 根据兑换码获取套餐信息
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 兑换码
 *     responses:
 *       200:
 *         description: 成功获取套餐信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 查询成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 anum:
 *                   type: integer
 *                   description: 数量
 *       500:
 *         description: 兑换码不能为空或服务器错误
 */
router.post("/getCashCouponList", async (req, res) => {
    let { code } = req.body
    if (!code) { return res.json({ code: 500, message: "兑换码不能为空" }) }
    //查询兑换码
    let sql = `SELECT * FROM mdl_code WHERE CODE = ?`;
    let result = await utils.dbUse(sql, [code]);
    //查询套餐
    let sql2 = `SELECT * FROM mdl_code_tab_meal WHERE MDL_CODE_TAB_ID = ?`;
    let result2 = await utils.dbUse(sql2, [result[0].FATHERR_ID]);
    //查出来的结果是数组，几个套餐就返回几个长度
    let mealArr = []
    for (let i = 0; i < result2.length; i++) {
        let sql3 = `SELECT * FROM mdl_combo_meal WHERE ID = ?`;
        let result3 = await utils.dbUse(sql3, [result2[i].MDL_MEAL_ID]);
        mealArr.push(result3[0])
    }
    res.json({ code: 200, message: "查询成功", data: mealArr, anum: result[0].A_NUM })
})
//根据套餐id获取套餐详情
/**
 * @swagger
 * /web/getCashCouponListById:
 *   post:
 *     summary: 根据套餐id获取套餐详情
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                 description: 套餐ID数组
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *     responses:
 *       200:
 *         description: 成功获取套餐详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: 参数缺失、套餐不存在、商品不存在或服务器错误
 */
router.post("/getCashCouponListById", async (req, res) => {
    let { id, storeCode } = req.body
    if (!id || !storeCode) { return res.json({ code: 500, message: "id和门店不能为空" }) }
    let returnArr = []
    for (let k = 0; k < id.length; k++) {
        let obj = {
            ID: id[k].id,
            TICKET: [],
            POSITIVE: []
        }
        let sql = `SELECT * FROM mdl_combo_meal WHERE ID = ?`;
        let result = await utils.dbUse(sql, [id[k].id]);
        if (result.length == 0) { return res.json({ code: 500, message: id[k].id + "套餐不存在" }) }
        let POSITIVE = JSON.parse(result[0].POSITIVE);
        let TICKET = JSON.parse(result[0].TICKET);
        result[0].POSITIVE_arr = []
        result[0].TICKET_arr = []
        for (let i = 0; i < POSITIVE.length; i++) {
            // SELECT * FROM mdl_product WHERE PRODUCT_CODE = ? OR MANY_CODE LIKE "%${TICKET_code || ''}%"
            let sql2 = `SELECT * FROM mdl_product WHERE ID = ?`;
            let result2 = await utils.dbUse(sql2, [POSITIVE[i]]);
            if (result2[0] == undefined) { return res.json({ code: 500, message: POSITIVE[i] + "商品不存在" }) }

            let food = await commodityCetailsMy(storeCode, result2[0].PRODUCT_CODE)
            if (food.data.code == 200) {
                //配置了二级商品
                if (result2[0].SECONDARY_MENU && food.data.data.product.type !== 1) {
                    for (let cz = 0; cz < food.data.data.product.comboItems.length; cz++) {//length
                        for (cz1 = 0; cz1 < food.data.data.product.comboItems[cz].comboProducts.length; cz1++) {
                            food.data.data.product.comboItems[cz].comboProducts[cz1].isChecked = ''
                        }
                    }
                    let meun2 = JSON.parse(result2[0].SECONDARY_MENU)
                    if (!meun2.comboItems) {
                        return res.json({ code: 500, message: '配置数据异常', dill: meun2 })
                    }
                    for (let mu = 0; mu < meun2.comboItems.length; mu++) { //length
                        for (let mu1 = 0; mu1 < meun2.comboItems[mu].comboProducts.length; mu1++) {
                            // let name = meun2.comboItems[mu].comboProducts[mu1].name
                            //遍历官方数据
                            for (let g1 = 0; g1 < food.data.data.product.comboItems.length; g1++) {
                                for (let g2 = 0; g2 < food.data.data.product.comboItems[g1].comboProducts.length; g2++) {
                                    if (meun2.comboItems[mu].comboProducts[mu1].name == '扭扭薯条') {
                                        if (food.data.data.product.comboItems[g1].comboProducts[g2].name == '脆脆薯条') {
                                            food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = true
                                        }
                                    }
                                    if (meun2.comboItems[mu].comboProducts[mu1].name == '脆脆薯条') {
                                        if (food.data.data.product.comboItems[g1].comboProducts[g2].name == '扭扭薯条') {
                                            food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = true
                                        }
                                    }
                                    if (food.data.data.product.comboItems[g1].comboProducts[g2].name == meun2.comboItems[mu].comboProducts[mu1].name) {

                                        food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = meun2.comboItems[mu].comboProducts[mu1].isChecked
                                    }
                                    //增加
                                    if (food.data.data.product.comboItems[g1].comboProducts[g2].name == '脆鸡排蛋沙拉叠叠卷组合' || food.data.data.product.comboItems[g1].comboProducts[g2].name == '鸡肉蛋沙拉叠叠卷组合') {
                                        food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = true
                                    }
                                }
                            }
                        }
                    }
                }

                obj.POSITIVE.push(food.data.data.product)
            } else {
                if (food.data.code == 10001 && result2[0].MANY_CODE) {
                    let findManyCode = result2[0].MANY_CODE.split(",")
                    let flag = false
                    for (let i = 0; i < findManyCode.length; i++) {
                        let manyCode = await commodityCetailsMy(storeCode, findManyCode[i])
                        if (manyCode.data.code == 200) {
                            //配置了二级商品
                            if (result2[0].SECONDARY_MENU) {
                                for (let cz = 0; cz < manyCode.data.data.product.comboItems.length; cz++) {
                                    for (cz1 = 0; cz1 < manyCode.data.data.product.comboItems[cz].comboProducts.length; cz1++) {
                                        manyCode.data.data.product.comboItems[cz].comboProducts[cz1].isChecked = ''
                                    }
                                }
                                let meun2 = JSON.parse(result2[0].SECONDARY_MENU)
                                for (let mu = 0; mu < meun2.comboItems.length; mu++) {
                                    for (let mu1 = 0; mu1 < meun2.comboItems[mu].comboProducts.length; mu1++) {
                                        // let name = meun2.comboItems[mu].comboProducts[mu1].name
                                        //遍历官方数据
                                        for (let g1 = 0; g1 < manyCode.data.data.product.comboItems.length; g1++) {
                                            for (let g2 = 0; g2 < manyCode.data.data.product.comboItems[g1].comboProducts.length; g2++) {

                                                if (manyCode.data.data.product.comboItems[g1].comboProducts[g2].name == meun2.comboItems[mu].comboProducts[mu1].name) {

                                                    manyCode.data.data.product.comboItems[g1].comboProducts[g2].isChecked = meun2.comboItems[mu].comboProducts[mu1].isChecked
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            obj.POSITIVE.push(manyCode.data.data.product)
                            flag = true
                            break;
                        }
                    }
                    if (!flag) {
                        return res.json({ code: 500, message: food.data.message })
                    }
                } else {
                    return res.json({ code: 500, message: food.data.message })
                }
            }

            // if (!result2[0].SECONDARY_MENU) {

            // } else {
            //     obj.POSITIVE.push(JSON.parse(result2[0].SECONDARY_MENU))
            // }


        }
        for (let i = 0; i < TICKET.length; i++) {
            let sql2 = `SELECT * FROM mdl_product WHERE ID = ?`;
            let result2 = await utils.dbUse(sql2, [TICKET[i]]);
            if (result2[0] == undefined) { return res.json({ code: 500, message: TICKET[i] + "商品不存在" }) }

            let food = await commodityCetailsMy(storeCode, result2[0].PRODUCT_CODE)
            if (food.data.code == 200) {
                //配置了二级商品
                if (result2[0].SECONDARY_MENU) {
                    for (let cz = 0; cz < food.data.data.product.comboItems.length; cz++) {
                        for (cz1 = 0; cz1 < food.data.data.product.comboItems[cz].comboProducts.length; cz1++) {
                            food.data.data.product.comboItems[cz].comboProducts[cz1].isChecked = ''
                        }
                    }
                    let meun2 = JSON.parse(result2[0].SECONDARY_MENU)
                    for (let mu = 0; mu < meun2.comboItems.length; mu++) {
                        for (let mu1 = 0; mu1 < meun2.comboItems[mu].comboProducts.length; mu1++) {
                            // let name = meun2.comboItems[mu].comboProducts[mu1].name
                            //遍历官方数据
                            for (let g1 = 0; g1 < food.data.data.product.comboItems.length; g1++) {
                                for (let g2 = 0; g2 < food.data.data.product.comboItems[g1].comboProducts.length; g2++) {
                                    if (meun2.comboItems[mu].comboProducts[mu1].name == '扭扭薯条') {
                                        if (food.data.data.product.comboItems[g1].comboProducts[g2].name == '脆脆薯条') {
                                            food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = true
                                        }
                                    }
                                    if (meun2.comboItems[mu].comboProducts[mu1].name == '脆脆薯条') {
                                        if (food.data.data.product.comboItems[g1].comboProducts[g2].name == '扭扭薯条') {
                                            food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = true
                                        }
                                    }
                                    if (food.data.data.product.comboItems[g1].comboProducts[g2].name == meun2.comboItems[mu].comboProducts[mu1].name) {

                                        food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = meun2.comboItems[mu].comboProducts[mu1].isChecked
                                    }
                                    //增加
                                    if (food.data.data.product.comboItems[g1].comboProducts[g2].name == '脆鸡排蛋沙拉叠叠卷组合' || food.data.data.product.comboItems[g1].comboProducts[g2].name == '鸡肉蛋沙拉叠叠卷组合') {
                                        food.data.data.product.comboItems[g1].comboProducts[g2].isChecked = true
                                    }
                                }
                            }
                        }
                    }
                }
                obj.TICKET.push(food.data.data.product)
            } else {
                if (food.data.code == 10001 && result2[0].MANY_CODE) {

                    let findManyCode = result2[0].MANY_CODE.split(",")
                    let flag = false
                    for (let i = 0; i < findManyCode.length; i++) {
                        let manyCode = await commodityCetailsMy(storeCode, findManyCode[i])
                        if (manyCode.data.code == 200) {
                            obj.TICKET.push(manyCode.data.data.product)
                            flag = true
                            break;
                        }
                    }
                    if (!flag) {
                        return res.json({ code: 500, message: food.data.message })
                    }
                } else {
                    return res.json({ code: 500, message: food.data.message })
                }
            }
            // if (!result2[0].SECONDARY_MENU) {

            // } else {
            //     obj.TICKET.push(JSON.parse(result2[0].SECONDARY_MENU))
            // }

        }
        returnArr.push(obj)
        // returnArr.push( result[0])
    }
    res.json({ code: 200, data: returnArr })
})
//兑换码下单接口
/**
 * @swagger
 * /web/addCashCouponOrder:
 *   post:
 *     summary: 兑换码下单接口
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 兑换码
 *               foodData:
 *                 type: array
 *                 items:
 *                   type: object
 *                 description: 商品数据
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *               iphone:
 *                 type: string
 *                 description: 手机号
 *               TakeAMeal:
 *                 type: string
 *                 description: 取餐方式
 *               storeName:
 *                 type: string
 *                 description: 门店名称
 *     responses:
 *       200:
 *         description: 下单成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 参数缺失、兑换码无效、余额不足或服务器错误
 */
router.post("/addCashCouponOrder", async (req, res) => {
    let { code, foodData, storeCode, iphone, TakeAMeal, storeName } = req.body
    if (!code || !foodData || !storeCode || !iphone || !TakeAMeal) { return res.json({ code: 500, message: "参数缺失" }) }
    let sql = `SELECT * FROM mdl_code WHERE CODE = ?`;
    let codeStatus = await utils.dbUse(sql, [code]);
    if (codeStatus.length == 0 || codeStatus[0].CODE_STATUS == 1) { return res.json({ code: 500, message: "兑换码不存在" }) }
    if (codeStatus[0].CODE_STATUS == 2) {
        return res.json({ code: 500, message: "兑换码已使用" })
    }
    if (codeStatus[0].CODE_STATUS == 3) { return res.json({ code: 500, message: "兑换码下单中" }) }
    //余额判断
    let protId = codeStatus[0].USER_ID
    let findUserMoey = 'SELECT * FROM user WHERE id = ' + protId
    let UserMoey = await utils.dbUse(findUserMoey, []);
    if (!UserMoey[0]) {
        return res.json({ code: 501, message: "未找到用户" })
    }
    if (UserMoey[0].balance < 100) {
        return res.json({ code: 501, message: "账户余额不足" })
    }
     //余额判断
    //当前位置兑换码状态为未使用 修改用户的兑换码状态
    await editCodeStatu(res, code)
    //兑换码进入下单状态
    let mealInfo = await getMealLimitCode(codeStatus[0]) //套餐信息
    if (!mealInfo.ret) { return res.json({ code: 500, message: mealInfo.message }) }
    let oderArr = []
    let mealInfoAllFood = []
    //开始记录日志 创建 mdl_log表
    let log_id = utils.createId();//日志id
    let log_strt_sql = "INSERT INTO mdl_log(ID,LOG_TIME,TYPE,TITLE) VALUES (?,?,?,?)"
    await utils.dbUse(log_strt_sql, [log_id, utils.createTime(), 2, `兑换码：${code},开始下单,${mealInfo.mealInfo.length}个商品`])
    if (foodData.length !== mealInfo.mealInfo.length) {
        for (let i = 0; i < foodData.length; i++) {
            for (let j = 0; j < mealInfo.mealInfo.length; j++) {
                if (foodData[i].ID == mealInfo.mealInfo[j].ID) {
                    mealInfoAllFood.push(mealInfo.mealInfo[j])
                }
            }
        }
    } else {
        mealInfoAllFood = mealInfo.mealInfo
    }
    for (let i = 0; i < mealInfoAllFood.length; i++) {
        //先排序
        let newFoodData = [];
        for (let p = 0; p < mealInfoAllFood.length; p++) {
            for (let j = 0; j < foodData.length; j++) {
                if (mealInfoAllFood[p].ID == foodData[j].ID) {
                    newFoodData.push(foodData[j])
                }
            }
        }
        let oder = null;
        let indexNum = 0;
        let wehr = ''
        do {
            indexNum++
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `进入第${indexNum}次下单,上次下单原因：${wehr}`, '', 1])
            oder = await oderLogic(codeStatus[0], newFoodData[i], storeCode, iphone, TakeAMeal, mealInfoAllFood[i], storeName, log_id, code, oder ? oder.sid : null)
            if (oder.code == 502 && oder.sid) {
                //修改sid的状态
                setTimeout(async function () {
                    unlockSidOne(oder.sid)
                }, 5000);
            }
            if (oder.code == 502 && oder.nowmealInfo) {
                mealInfoAllFood[i] = oder.nowmealInfo
                wehr = oder.message
                if (oder.sid) {
                    //修改sid的状态
                    setTimeout(async function () {
                        unlockSidOne(oder.sid)
                    }, 5000);
                }
            } else if (oder.code == 501) {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `${code}下单失败:${oder.message},门店：${storeName}`, oder.sid, 0])
                if (oder.sid) {
                    //修改sid的状态
                    let sql = `UPDATE mdl_sid SET START = ? WHERE SID = ?`
                    await utils.dbUse(sql, [0, oder.sid])
                }
            }
        } while (oder.code == 502);

        oderArr.push(oder)
    }
    //修改兑换码使用时间
    let sqlc = `UPDATE mdl_code SET USE_TIME = ? WHERE CODE = ?`;
    await utils.dbUse(sqlc, [+new Date(), code]);
    for (let i = 0; i < oderArr.length; i++) {
        if (oderArr[i].code == 501) {
            oderArr[i].sid = '瞅啥呢?'
        }
    }
    res.json({
        code: 200,
        data: oderArr
    })
})
//获取定制信息
/**
 * @swagger
 * /web/getCustomization:
 *   post:
 *     summary: 获取定制信息
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *               productCode:
 *                 type: string
 *                 description: 商品代码
 *     responses:
 *       200:
 *         description: 成功获取定制信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 参数缺失或服务器错误
 */
router.post("/getCustomization", async (req, res) => {
    let { storeCode, productCode } = req.body
    if (!storeCode || !productCode) { return res.json({ code: 500, message: "参数缺失" }) }
    let result = await commodityCetailsMy(storeCode, productCode)
    if (result.data.code != 200) { return res.json({ code: 500, message: result.data.message }) }
    let food = result.data.data.product;
    if (food.type == 7 || food.type == 2) {
        if (food.comboItems) {
            for (let i = 0; i < food.comboItems.length; i++) {
                for (let j = 0; j < food.comboItems[i].comboProducts.length; j++) {
                    food.comboItems[i].comboProducts[j].isChecked = true
                }
            }
        }
    }

    res.json({ code: 200, data: food })
})
//获取取餐方式
/**
 * @swagger
 * /web/getTakeA:
 *   post:
 *     summary: 获取取餐方式
 *     tags: [Web]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *     responses:
 *       200:
 *         description: 成功获取取餐方式
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       code:
 *                         type: string
 *                       subText:
 *                         type: string
 *                       text:
 *                         type: string
 *       500:
 *         description: 服务器错误
 */
router.post("/getTakeA", async (req, res) => {
    let { storeCode } = req.body
    let findSid = `SELECT * FROM mdl_sid WHERE LOGIN_CODE = 1`
    // let sids = await utils.dbUse(findSid, [])

    let sid = '2d1d3c4183fc593d15ed1afccff945ce_'
    await clearCartSid(storeCode, sid)
    let codeArr = ['3000', '2430']//可乐
    let key = true;
    let addcart = await addCartSid(storeCode, sid, codeArr[0], '', '', '', { items: [], options: [] }, [], '', '1', '0', '')
    if (addcart.data.code == 200) {
        let cartInfo = await getCartInfo(storeCode, sid)
        if (cartInfo.data.code == 200) { //storePickUpInfo
            let takeFancth = cartInfo.data.data.confirmInfo.storePickUpInfo.eatTypeOptions
            for (let i = 0; i < takeFancth.length; i++) {
                if (takeFancth[i].text == '得来速') {
                    takeFancth.splice(i, 1);
                }
            }
            res.json({ code: 200, data: takeFancth })
            return
        } else {
            key = false
        }
    } else {
        key = false
    }
    if (!key) {
        let addcart1 = await addCartSid(storeCode, sid, codeArr[1], '', '', '', { items: [], options: [] }, [], '', '1', '0', '')
        if (addcart1.data.code == 200) {
            let cartInfo1 = await getCartInfo(storeCode, sid)
            if (cartInfo1.data.code == 200) {
                let takeFancth = cartInfo1.data.data.confirmInfo.storePickUpInfo.eatTypeOptions
                for (let i = 0; i < takeFancth.length; i++) {
                    if (takeFancth[i].text == '得来速') {
                        takeFancth.splice(i, 1);
                    }
                }
                res.json({ code: 200, data: takeFancth })
                return
            } else {
                res.json({
                    code: 200,
                    data: [
                        {
                            "code": "eat-in",
                            "subText": "店内用餐",
                            "text": "堂食"
                        },
                        {
                            "code": "take-in-store",
                            "selected": true,
                            "subText": "店内自提",
                            "text": "外带"
                        }
                    ]
                })
            }
        } else {
            res.json({
                code: 200,
                data: [
                    {
                        "code": "eat-in",
                        "subText": "店内用餐",
                        "text": "堂食"
                    },
                    {
                        "code": "take-in-store",
                        "selected": true,
                        "subText": "店内自提",
                        "text": "外带"
                    }
                ]
            })
        }
    }





})


//删除优惠券
async function deletedCouponInfo(couponInfo) {
    if (couponInfo.TOTAL_AVAILABLE_COUNT == 1) {
        let deletSql = `DELETE FROM mdl_coupon WHERE ID = ?`
        await utils.dbUse(deletSql, [couponInfo.ID]);
    } else {
        console.log(couponInfo)
        //让券的剩余次数与今日可用次数减1
        let deletedCouponSql = 'UPDATE mdl_coupon SET CURRENT_DAY_AVAILABLE_COUNT = CURRENT_DAY_AVAILABLE_COUNT - 1, TOTAL_AVAILABLE_COUNT = TOTAL_AVAILABLE_COUNT - 1 WHERE ID = ?'
        console.log(`UPDATE mdl_coupon SET CURRENT_DAY_AVAILABLE_COUNT = CURRENT_DAY_AVAILABLE_COUNT - 1, TOTAL_AVAILABLE_COUNT = TOTAL_AVAILABLE_COUNT - 1 WHERE ID = ${couponInfo.ID}`)
        
        await utils.dbUse(deletedCouponSql, [couponInfo.ID]);//deletSql
    }

}


//下单逻辑入口 返回501错误 则需要提示前端，返回502 则需要重新进入下单
async function oderLogic(codeInfo, foodData, storeCode, iphone, TakeAMeal, nowmealInfo, storeName, log_id, code,sid2) {

    let proxyId = codeInfo.USER_ID
    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `当前兑换码的代理Id为:${proxyId}`, '', 1])
    if (!foodData) {
        return { code: 501, message: "非法数据,请检查" }
    }
    //方案本别
    let PLAN = nowmealInfo.PLAN //方案;
    let APPOINT = nowmealInfo.APPOINT //指定账号
    let VIPBAN = ''
    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `开始匹配账号`, '', 1])

    if (APPOINT) {
        //指定账号
        let APPSID = APPOINT.split(',')
        for (let i = 0; i < APPSID.length; i++) {
            if (!APPSID[i]) { continue; }
            let getSidApp = await verifySid(APPSID[i])
            if (getSidApp.code == 200) {
                VIPBAN = getSidApp.sid
                break;
            }
        }
        if (nowmealInfo.CIRCULATE && !VIPBAN) {
            let time = 0
            let timeOut = 300
            let timeInterval = 3
            let kety = 1
            do {
                if (kety > APPSID.length) {
                    time = time + 300
                }
                kety++
                for (let i = 0; i < APPSID.length; i++) {
                    if (!APPSID[i]) { continue; }
                    let getSidApp = await verifySid(APPSID[i])
                    if (getSidApp.code == 200) {
                        VIPBAN = getSidApp.sid
                        break;
                    }
                }
                if (VIPBAN) {
                    time = timeInterval + 1500
                }
                await utils.sleep(timeInterval * 1000)
                time += timeInterval
            } while (time < timeOut)
        }
    }
    //不指定账号
    if (PLAN == 0) {
        //1个有券商品+N个正价商品(N>0),非权益
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `1个有券商品+N个正价商品(N>0),非权益模式`, '', 1])

        let TICKET = foodData.TICKET;//有券商品
        if (TICKET.length !== 1) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `模式不正确，返回:有券商品数量不正确`, '', 1])
            return { code: 501, message: '有券商品数量不正确' }
        }
        let POSITIVE = foodData.POSITIVE;//正价商品
        if (POSITIVE.length == 0) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `模式不正确，返回:正价商品数量不正确`, '', 1])
            return { code: 501, message: '正价商品数量不正确' }
        }
        //匹配账号
        let key = 0
        let sid = ""
        let couponInfo = null
        if (APPOINT) {
            //指定sid
            if (!VIPBAN) {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `未能查到可用的账号`, '', 0])
                return { code: 501, message: '未能查到可用的账号' }
            } else {
                //指定账号并且已经拿到账号
                //获取优惠券
                let couponCodeIn = await getAccountCoupon(TICKET[0].code, 0, nowmealInfo)
                if (!couponCodeIn.ret) { return { code: 501, message: couponCodeIn.message } }
                //查询sid的券获取sid的券信息
                let getSidlll = await getSidCouponInfo(storeCode, VIPBAN, couponCodeIn.couponInfo.COUPON_ID)
                if (getSidlll.code == 502) { return { code: 502, message: "无优惠券，请重新进入下单" } }
                //getSidlll.data
                couponInfo = { ID:couponCodeIn.couponInfo.ID , COUPON_ID: getSidlll.data.id, COUPON_CODE: getSidlll.data.code, PROMOTION_ID: getSidlll.data.promotionId }
                sid = VIPBAN
            }
        } else {
            do {
                let couponINfo = await getAccountCoupon(TICKET[0].code, key, nowmealInfo)
                if (!couponINfo.ret) { return { code: 501, message: couponINfo.message } }
                //取sid
                getSid = await getSidLimit(couponINfo.couponInfo.COUPON_SID_ID, couponINfo.couponInfo)
                key++
            } while (getSid.ret);
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '共计循环取出' + key + '次账号', '', 0])
            sid = getSid.sid
            couponInfo = getSid.couponInfo
            //修改sid的START参数
            let editSids = `UPDATE mdl_sid SET START = 1 WHERE SID = ?`
            await utils.dbUse(editSids, [sid]);
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `匹配账号完成，账号:${sid}；开始下单`, '', 1])
        //开始下单
        //清理购物车
        let peCart = await clearCart(storeCode, sid)
        if (peCart.code != 200) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `清理购物车失败返回,${peCart.message}`, '', 0])
            return peCart
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `清理购物车完成`, '', 1])
        //加入购物车
        //有券商品加入
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用券code：${couponInfo.COUPON_CODE},id:${couponInfo.COUPON_ID}`, '', 1])
        let addCartCouponResult = await addCartCoupon(TICKET, storeCode, sid, couponInfo)
        if (addCartCouponResult.code != 200) {
            if (addCartCouponResult.gfMsg == '此卡券不支持在该门店使用，请换家门店使用哦！') {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `加入购物车失败返回:${addCartCouponResult.message}` + ',状态码:' + addCartCouponResult.code, '', 0])
                return { code: 501, message: "此卡券不支持在该门店使用，请换家门店使用哦！", sid: sid }
            }
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `加入购物车失败返回:${addCartCouponResult.message}` + ',状态码:' + addCartCouponResult.code, '', 0])
            return addCartCouponResult
        }
        console.log("加入成功")
        //使用优化后的券逻辑
        const validateResult = await validateCouponBeforeUse(couponInfo.ID, log_id);
        if (validateResult.code !== 200) {
            return validateResult;
        }
        await optimizedCouponLogic.markCouponAsUsed(couponInfo, null, sid, storeCode, null);
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `有券加入购物车成功,标记券为已使用code：${couponInfo.COUPON_CODE}`, '', 1])
        //正价商品加入
        let addCartPositiveResult = await addCartPositive(POSITIVE, storeCode, sid, false, false)
        if (addCartPositiveResult.code != 200) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `加入购物车失败返回,${addCartPositiveResult.message}` + ',状态码:' + addCartPositiveResult.code, '', 0])
            return addCartPositiveResult
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `正价加入购物车成功`, '', 1])
        //获取购物车
        let cartInfo = await getSidCartInfo(storeCode, sid)
        if (cartInfo.code != 200) { return cartInfo }
        //限制价格
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `获取购物车成功`, '', 1])
        let limitPrice = parseFloat(nowmealInfo.PAYPRICELIMIT ? nowmealInfo.PAYPRICELIMIT : 100)
        let cartPrice = parseFloat(cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount)
        if (limitPrice !== 1 && cartPrice > limitPrice) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `购物车金额${cartPrice}超过限制${limitPrice},下单返回`, '', 0])
            //超过金额限制
            return { code: 501, message: '购物车金额超过限制', sid: sid }
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `金额限制通过`, '', 1])
        let cashCouponCode = '', cashCouponId = '', payByBalance = nowmealInfo.BALANCE_PAYMENT
        //判断是否需要现金券下单
        let cashID = ''
        if (nowmealInfo.CASHCOUPON) {
            //获取现金券
            let cashInfo = await getCashCouponInfo(nowmealInfo.CASHCOUPON, sid, storeCode)
            if (cashInfo.code == 200) {

                cashCouponCode = cashInfo.data.couponCode;
                cashCouponId = cashInfo.data.couponId;

                if (cashInfo.data.cashID) {
                    cashID = cashInfo.data.cashID
                }
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了现金券code：${cashCouponCode},id:${cashCouponId}`, '', 1])
            } else {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `没有拿到抵用券`, '', 1])
            }
        }
        //提交订单
        let postOder = await postOderMdl(sid, '', cartInfo.data.confirmInfo.productPriceInfo.cartProductList, cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount, storeCode, TakeAMeal, cashCouponCode, cashCouponId, payByBalance, proxyId, cartInfo.data.confirmInfo.productPriceInfo.promotionList ? cartInfo.data.confirmInfo.productPriceInfo.promotionList : [], nowmealInfo)
        if (postOder.code != 200) { return postOder }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `提交订单成功`, '', 1])
        let { eatTypeCode, orderId, payId } = postOder.data
        if (cashID) {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET ORDER_ID = ? ,VIP = ?,B_USE=? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [orderId, code, 1, cashID])
        }
        if (payByBalance) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了余额支付`, '', 1])
            //余额下单
            //如果余额下单的话，需要再次提交订单
            let preOrderResult = await preOrder(payId, sid)
            if (preOrderResult.data.code !== 200) {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额支付失败`, '', 0])
                return { code: 501, message: '选择支付方式失败', sid: sid }
            }
            if (preOrderResult.data.data.responseCode == "SUCCESS") {
                //余额下单成功
                let payByBallanceResult = await payByBallance(payId, sid)
                if (payByBallanceResult.data.code == '0001') {
                    //余额不足
                    nowmealInfo.BALANCE_PAYMENT = 0
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额不足`, '', 0])
                    return { code: 502, message: '余额不足', nowmealInfo, sid: sid }
                }
            }
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `下单完成，创建订单`, '', 0])
        //创建订单数据
        let OderIDCreated = utils.createId()
        let createdOder = `INSERT INTO mdl_order (ID,CODE,CREATRD_TIME,EATTYPE,MEAL_IMG,MEAL_TITLE,ODER_STATUS,OPEN_ID,ODER_ID,PAY_ID,PAY_TEXT,PROXY_ID,LIMIT_PRICE,STORE_CODE,STORE_NAME,USER_IPHONE,ODER_IPHONE,MEAL_ID,PRICE_NUM,COUPON_ID,	COUPON_CODE) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`
        await utils.dbUse(createdOder, [
            OderIDCreated,
            codeInfo.CODE,
            utils.createTime(),
            eatTypeCode,
            nowmealInfo.IMAGE,
            nowmealInfo.TITLE,
            0,
            sid,
            orderId,
            payId,
            payByBalance ? '余额支付' : '支付宝支付',
            proxyId,
            limitPrice,
            storeCode,
            storeName,
            iphone,
            '',
            nowmealInfo.ID,
            cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount,
            couponInfo.COUPON_ID,
            couponInfo.COUPON_CODE
        ])
        //获取订单详情
        let orderInfo = await getOrderDetailFun(orderId, sid)
        if (orderInfo.code != 200) { return orderInfo }
        checkOrderPay(orderId, sid, OderIDCreated, codeInfo.CODE, payId, proxyId,false, nowmealInfo.PRICE)//PRICE

        return orderInfo.data
    }
    if (PLAN == 1) {
        //单1个有券商品下单
        let TICKET = foodData.TICKET;//有券商品
        if (TICKET.length !== 1) { return { code: 501, message: '有券商品数量不正确' } }
        //匹配账号
        let key = 0
        let sid = ""
        let couponInfo = null
        if (APPOINT) {
            //指定sid
            if (!VIPBAN) {
                return { code: 501, message: '未能查到可用的sid,没有' }
            } else {
                //指定账号并且已经拿到账号
                //获取优惠券
                let couponCodeIn = await getAccountCoupon(TICKET[0].code, 0, nowmealInfo)
                if (!couponCodeIn.ret) { return { code: 501, message: couponCodeIn.message, sid: sid } }
                //查询sid的券获取sid的券信息
                let getSidlll = await getSidCouponInfo(storeCode, VIPBAN, couponCodeIn.couponInfo.COUPON_ID)
                if (getSidlll.code == 502) { return { code: 502, message: "无优惠券，请重新进入下单" } }
                //getSidlll.data
                couponInfo = { ID:couponCodeIn.couponInfo.ID, COUPON_ID: getSidlll.data.id, COUPON_CODE: getSidlll.data.code, PROMOTION_ID: getSidlll.data.promotionId }
                sid = VIPBAN
            }
        } else {
            do {
                let couponINfo = await getAccountCoupon(TICKET[0].code, key, nowmealInfo)
                if (!couponINfo.ret) { return { code: 501, message: couponINfo.message } }
                //取sid
                getSid = await getSidLimit(couponINfo.couponInfo.COUPON_SID_ID, couponINfo.couponInfo)
                key++
            } while (getSid.ret);
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '共计循环取出' + key + '次账号', '', 1])
            sid = getSid.sid
            couponInfo = getSid.couponInfo
            //修改sid的START参数
            let editSids = `UPDATE mdl_sid SET START = 1 WHERE SID = ?`
            await utils.dbUse(editSids, [sid]);
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `匹配账号完成，账号:${sid}；开始下单`, '', 1])
        //开始下单
        //清理购物车
        let peCart = await clearCart(storeCode, sid)
        if (peCart.code != 200) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, peCart.message, '', 0])
            return peCart
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `清理购物车完成`, '', 1])
        //加入购物车
        //有券商品加入
        let addCartCouponResult = await addCartCoupon(TICKET, storeCode, sid, couponInfo)
        if (addCartCouponResult.code != 200) {
            if (addCartCouponResult.gfMsg == '此卡券不支持在该门店使用，请换家门店使用哦！') {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `加入购物车失败返回:${addCartCouponResult.message}` + ',状态码:' + addCartCouponResult.code, '', 0])
                return { code: 501, message: "此卡券不支持在该门店使用，请换家门店使用哦！", sid: sid }
            }
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `加入购物车失败返回:${addCartCouponResult.message}` + ',状态码:' + addCartCouponResult.code, '', 0])
            return addCartCouponResult

        }
        if (couponInfo.ID !== 0) {
            //使用优化后的券逻辑
            const validateResult = await validateCouponBeforeUse(couponInfo.ID, log_id);
            if (validateResult.code !== 200) {
                return validateResult;
            }
            await optimizedCouponLogic.markCouponAsUsed(couponInfo, null, sid, storeCode, null);
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `有券加入购物车成功,标记券为已使用code：${couponInfo.COUPON_CODE}`, '', 1])
        }

        //获取购物车
        let cartInfo = await getSidCartInfo(storeCode, sid)
        if (cartInfo.code != 200) { return cartInfo }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `获取购物车完成`, '', 1])
        //限制价格
        let limitPrice = parseFloat(nowmealInfo.PAYPRICELIMIT ? nowmealInfo.PAYPRICELIMIT : 100)
        let priceKey = 0
        if (cartInfo.data.confirmInfo.productPriceInfo) {
            priceKey = parseFloat(cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount)//realTotalAmount
            if (limitPrice !== 1 && priceKey > limitPrice) {
                //超过金额限制
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `购物车金额${priceKey}超过限制${limitPrice},下单返回`, '', 0])
                return { code: 501, message: '购物车金额超过限制', sid: sid }
            }
        }
        let cashCouponCode = '', cashCouponId = '', payByBalance = nowmealInfo.BALANCE_PAYMENT
        //判断是否需要现金券下单
        let cashID = ''
        if (nowmealInfo.CASHCOUPON) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `需要现金券下单`, '', 0])
            //获取现金券
            let cashInfo = await getCashCouponInfo(nowmealInfo.CASHCOUPON, sid, storeCode)
            if (cashInfo.code == 200) {
                cashCouponCode = cashInfo.data.couponCode;
                cashCouponId = cashInfo.data.couponId;
                if (cashInfo.data.cashID) {
                    cashID = cashInfo.data.cashID
                }
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了现金券code：${cashCouponCode},id:${cashCouponId}`, '', 1])
            } else {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `没有匹配到`, '', 0])
            }

        }
        //提交订单 cartProductList. cartProductList cartProductList
        let promotionLists = []
        if(cartInfo.data.confirmInfo.productPriceInfo && cartInfo.data.confirmInfo.productPriceInfo.promotionList){
            promotionLists = cartInfo.data.confirmInfo.productPriceInfo.promotionList
        }
        let postOder = await postOderMdl(sid, '', cartInfo.data.confirmInfo.productPriceInfo ? cartInfo.data.confirmInfo.productPriceInfo.cartProductList : [], priceKey, storeCode, TakeAMeal, cashCouponCode, cashCouponId, payByBalance, proxyId, promotionLists, nowmealInfo)
        if (postOder.code != 200) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `提交订单失败:${postOder.message}`, '', 0])
            return postOder
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `提交订单成功`, '', 1])
        let { eatTypeCode, orderId, payId } = postOder.data
        if (cashID) {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET ORDER_ID = ?, VIP = ?,B_USE=? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [orderId, code, 1, cashID])
        }
        if (payByBalance) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额支付`, '', 1])
            //余额下单
            //如果余额下单的话，需要再次提交订单
            let preOrderResult = await preOrder(payId, sid)
            if (preOrderResult.data.code !== 200) { return { code: 501, message: '选择支付方式失败', sid: sid } }
            if (preOrderResult.data.data.responseCode == "SUCCESS") {
                //余额下单成功
                let payByBallanceResult = await payByBallance(payId, sid)
                if (payByBallanceResult.data.code == '0001') {
                    //余额不足
                    nowmealInfo.BALANCE_PAYMENT = 0
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额不足`, '', 0])
                    return { code: 502, message: '余额不足', nowmealInfo, sid: sid }
                }
            }
        }
        //创建订单数据
        let OderIDCreated = utils.createId()
        let createdOder = `INSERT INTO mdl_order (ID,CODE,CREATRD_TIME,EATTYPE,MEAL_IMG,MEAL_TITLE,ODER_STATUS,OPEN_ID,ODER_ID,PAY_ID,PAY_TEXT,PROXY_ID,LIMIT_PRICE,STORE_CODE,STORE_NAME,USER_IPHONE,ODER_IPHONE,MEAL_ID,PRICE_NUM,COUPON_ID,COUPON_CODE) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`
        await utils.dbUse(createdOder, [
            OderIDCreated,
            codeInfo.CODE,
            utils.createTime(),
            eatTypeCode,
            nowmealInfo.IMAGE,
            nowmealInfo.TITLE,
            0,
            sid,
            orderId,
            payId,
            payByBalance ? '余额支付' : '支付宝支付',
            proxyId,
            limitPrice,
            storeCode,
            storeName,
            iphone,
            '',
            nowmealInfo.ID,
            cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount,
            couponInfo.COUPON_ID,
            couponInfo.COUPON_CODE
        ])
        //获取订单详情
        let orderInfo = await getOrderDetailFun(orderId, sid)
        if (orderInfo.code != 200) { return orderInfo }
        checkOrderPay(orderId, sid, OderIDCreated, codeInfo.CODE, payId, proxyId,false, nowmealInfo.PRICE)
        return orderInfo.data
    }
    if (PLAN == 2) {
        //N正价商品下单(N>0)
        let POSITIVE = foodData.POSITIVE;//正价商品
        if (POSITIVE.length == 0) { return { code: 501, message: '正价商品数量不正确' } }
        let TICKET = foodData.TICKET;//有券商品
        if (TICKET.length > 0) { return { code: 501, message: '有券商品数量不正确' } }
        //匹配账号
        //取sid
        let sid = ''
        if (APPOINT) {
            if (!VIPBAN) {
                return { code: 501, message: '未能查到可用的sid' }
            }
            sid = VIPBAN
        } else {
            let getSid = await getSidMdlRandom(nowmealInfo.ACCOUNT, 0)//无规则取号
            if (getSid.code != 200) { return getSid }
            sid = getSid.sid
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `取到账号：${sid}`, '', 1])
        //开始下单
        //清理购物车
        let peCart = await clearCart(storeCode, sid)
        if (peCart.code != 200) { return peCart }
        //正价商品加入
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `清理购物车成功`, '', 1])
        let addCartPositiveResult = await addCartPositive(POSITIVE, storeCode, sid, false, false)
        if (addCartPositiveResult.code != 200) { return addCartPositiveResult }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `加入购物车成功`, '', 1])
        //获取购物车
        let cartInfo = await getSidCartInfo(storeCode, sid)
        if (cartInfo.code != 200) { return cartInfo }
        //限制价格
        let limitPrice = parseFloat(nowmealInfo.PAYPRICELIMIT ? nowmealInfo.PAYPRICELIMIT : 100)
        let cartPrice = parseFloat(cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount)
        if (limitPrice !== 1 && cartPrice > limitPrice) {
            //超过金额限制
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `购物车金额${cartPrice}超过限制${limitPrice},下单返回`, '', 0])
            return { code: 501, message: '购物车金额超过限制', sid: sid }
        }
        let cashCouponCode = '', cashCouponId = '', payByBalance = nowmealInfo.BALANCE_PAYMENT
        //判断是否需要现金券下单
        let cashID = ''
        if (nowmealInfo.CASHCOUPON) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `需要现金券下单`, '', 1])
            //获取现金券
            let cashInfo = await getCashCouponInfo(nowmealInfo.CASHCOUPON, sid, storeCode)
            if (cashInfo.code == 200) {
                cashCouponCode = cashInfo.data.couponCode;
                cashCouponId = cashInfo.data.couponId;
                if (cashInfo.data.cashID) {
                    cashID = cashInfo.data.cashID
                }
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了现金券code：${cashCouponCode},id:${cashCouponId}`, '', 1])
            } else {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `没有可用的抵用券`, '', 1])
            }

        }
        //提交订单
        let postOder = await postOderMdl(sid, '', cartInfo.data.confirmInfo.productPriceInfo.cartProductList, cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount, storeCode, TakeAMeal, cashCouponCode, cashCouponId, payByBalance, proxyId, cartInfo.data.confirmInfo.productPriceInfo.promotionList ? cartInfo.data.confirmInfo.productPriceInfo.promotionList : [], nowmealInfo)
        if (postOder.code != 200) { return postOder }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `提交订单成功`, '', 1])
        let { eatTypeCode, orderId, payId } = postOder.data
        if (cashID) {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET ORDER_ID = ?, VIP = ?,B_USE=? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [orderId, code, 1, cashID])
        }
        if (payByBalance) {
            //余额下单
            //如果余额下单的话，需要再次提交订单
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额下单`, '', 1])
            let preOrderResult = await preOrder(payId, sid)
            if (preOrderResult.data.code !== 200) { return { code: 501, message: '选择支付方式失败', sid: sid } }
            if (preOrderResult.data.data.responseCode == "SUCCESS") {

                //余额下单成功
                let payByBallanceResult = await payByBallance(payId, sid)
                if (payByBallanceResult.data.code == '0001') {
                    //余额不足
                    nowmealInfo.BALANCE_PAYMENT = 0
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额不足`, '', 0])
                    return { code: 502, message: '余额不足', nowmealInfo, sid: sid }
                } else {

                }
            }
        }
        //创建订单数据
        let OderIDCreated = utils.createId()
        let createdOder = `INSERT INTO mdl_order (ID,CODE,CREATRD_TIME,EATTYPE,MEAL_IMG,MEAL_TITLE,ODER_STATUS,OPEN_ID,ODER_ID,PAY_ID,PAY_TEXT,PROXY_ID,LIMIT_PRICE,STORE_CODE,STORE_NAME,USER_IPHONE,ODER_IPHONE,MEAL_ID,PRICE_NUM) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`
        await utils.dbUse(createdOder, [
            OderIDCreated,
            codeInfo.CODE,
            utils.createTime(),
            eatTypeCode,
            nowmealInfo.IMAGE,
            nowmealInfo.TITLE,
            0,
            sid,
            orderId,
            payId,
            payByBalance ? '余额支付' : '支付宝支付',
            proxyId,
            limitPrice,
            storeCode,
            storeName,
            iphone,
            '',
            nowmealInfo.ID,
            cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount
        ])
        //获取订单详情
        let orderInfo = await getOrderDetailFun(orderId, sid)
        if (orderInfo.code != 200) { return orderInfo }
        checkOrderPay(orderId, sid, OderIDCreated, codeInfo.CODE, payId, proxyId,false,nowmealInfo.PRICE)
        return orderInfo.data

    }
    if (PLAN == 3) {
        //N个权益商品(N>0)
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `N个权益商品类`, '', 1])
        let POSITIVE = foodData.POSITIVE;//正价商品
        if (POSITIVE.length == 0) { return { code: 501, message: '正价商品数量不正确' } }
        let TICKET = foodData.TICKET;//有券商品
        if (TICKET.length > 0) { return { code: 501, message: '有券商品数量不正确' } }
        //匹配账号
        //取权益账号sid
        let sid = ''
        let bei = nowmealInfo.BANK_CARD_VIP ? true : false; //是否启用权益备用号，麦卡随单购
        if (!bei) {
            nowmealInfo.BANK_CARD_BREAKFAST ? true : false;//是否启用早餐卡随单购
        }
        //如果余额付款余额不足时，第二次进入仍使用这个账号
        if(!sid2){
            if (APPOINT) {
                if (!VIPBAN) {
                    return { code: 501, message: '未能查到可用的sid' }
                }
                sid = VIPBAN
            } else {
                let getSid = await getSidMdlRights(nowmealInfo)//获取权益账号
                if (getSid.code != 200) {
                    return getSid
                }
                sid = getSid.sid
                bei = getSid.bei
            }
        }else{
            sid = sid2
        }
        
        
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `匹配账号完成，账号:${sid}；开始下单`, '', 1])
        //开始下单
        //清理购物车
        let peCart = await clearCart(storeCode, sid)
        if (peCart.code != 200) { return peCart }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `清理购物车完成`, '', 1])
        let vip = false;
        let MYKA_BREAKFAST_USE = false //会员早餐 
        let MYKA_BREAKFAST = false //BANK_CARD_BREAKFAST
        if (nowmealInfo.MY_KA_USE) {
            vip = true
        }
        if (nowmealInfo.MYKA_BREAKFAST_USE) {
            MYKA_BREAKFAST_USE = true
        }
        if (nowmealInfo.BREAKFAST_CARD_USE) {
            MYKA_BREAKFAST = true
        }
        //正价商品加入
        let addCartPositiveResult = await addCartPositive(POSITIVE, storeCode, sid, vip, MYKA_BREAKFAST, MYKA_BREAKFAST_USE, bei)
        if (addCartPositiveResult.code != 200) { return addCartPositiveResult }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `正价商品加入成功`, '', 1])
        //获取购物车
        let cartInfo = await getSidCartInfo(storeCode, sid)
        if (cartInfo.code != 200) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `获取购物车失败`, '', 0])
            return cartInfo 
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `获取购物车成功`, '', 1])
        //限制价格
        let limitPrice = parseFloat(nowmealInfo.PAYPRICELIMIT ? nowmealInfo.PAYPRICELIMIT : 100)
        if (!cartInfo.data.confirmInfo.productPriceInfo) {
            return { code: 501, message: '下单异常，参数错误', data: cartInfo.data, sid: sid }
        }
        let cartPrice = parseFloat(cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount)//realTotalAmount
        if (limitPrice !== 1 && cartPrice > limitPrice) {
            //超过金额限制
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `购物车金额${cartPrice}超过限制${limitPrice},下单返回`, '', 0])
            return { code: 501, message: '购物车金额超过限制', sid: sid }
        }
        let cashCouponCode = '', cashCouponId = '', payByBalance = nowmealInfo.BALANCE_PAYMENT
        //判断是否需要现金券下单
        let cashID = ''
        if (nowmealInfo.CASHCOUPON) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `需要现金券下单`, '', 1])
            //获取现金券
            let cashInfo = await getCashCouponInfo(nowmealInfo.CASHCOUPON, sid, storeCode)

            if (cashInfo.code == 200) {
                cashCouponCode = cashInfo.data.couponCode;
                cashCouponId = cashInfo.data.couponId;
                if (cashInfo.data.cashID) {
                    cashID = cashInfo.data.cashID
                }
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了现金券code：${cashCouponCode},id:${cashCouponId}`, '', 1])
            } else {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `没有可用的抵用券`, '', 1])
            }

        }
        //提交订单
        let postOder = await postOderMdl(sid, '', cartInfo.data.confirmInfo.productPriceInfo.cartProductList, cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount, storeCode, TakeAMeal, cashCouponCode, cashCouponId, payByBalance, proxyId, cartInfo.data.confirmInfo.productPriceInfo.promotionList ? cartInfo.data.confirmInfo.productPriceInfo.promotionList : [], nowmealInfo, bei)
        if (postOder.code != 200) { return postOder }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `提交订单完成`, '', 1])
        let eatTypeCode,orderId,payId
        eatTypeCode = postOder.data.eatTypeCode
        orderId = postOder.data.orderId
        payId = postOder.data.payId
        if (cashID) {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET ORDER_ID = ?, VIP = ?,B_USE=? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [orderId, code, 1, cashID])
        }
        if (payByBalance) {
            //余额下单
            //如果余额下单的话，需要再次提交订单
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额下单`, '', 1])
            let preOrderResult = await preOrder(payId, sid)
            if (preOrderResult.data.code !== 200) { return { code: 501, message: '选择支付方式失败', sid: sid } }
            if (preOrderResult.data.data.responseCode == "SUCCESS") {
                //余额下单成功
                let payByBallanceResult = await payByBallance(payId, sid)
                if (payByBallanceResult.data.responseCode == '0001' && payByBallanceResult.data.responseMsg =='账户余额不足') {
                    //余额不足
                   let cacelRes = await cancelOder(orderId,sid)
                   console.log(cacelRes.data)
                    nowmealInfo.BALANCE_PAYMENT = 0
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `余额不足,跳转支付宝`, '', 0])
                    return { code: 502, message: '余额不足', nowmealInfo, sid: sid }
                } else {

                }
            }
        }
        //创建订单数据
        let OderIDCreated = utils.createId()
        let createdOder = `INSERT INTO mdl_order (ID,CODE,CREATRD_TIME,EATTYPE,MEAL_IMG,MEAL_TITLE,ODER_STATUS,OPEN_ID,ODER_ID,PAY_ID,PAY_TEXT,PROXY_ID,LIMIT_PRICE,STORE_CODE,STORE_NAME,USER_IPHONE,orderToOrder,ODER_IPHONE,MEAL_ID,PRICE_NUM) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`
        await utils.dbUse(createdOder, [
            OderIDCreated,
            codeInfo.CODE,
            utils.createTime(),
            eatTypeCode,
            nowmealInfo.IMAGE,
            nowmealInfo.TITLE,
            0,
            sid,
            orderId,
            payId,
            payByBalance ? '余额支付' : '支付宝支付',
            proxyId,
            limitPrice,
            storeCode,
            storeName,
            iphone,
            bei ? '随单购' : '权益单',
            '',
            nowmealInfo.ID,
            cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount
        ])
        //获取订单详情
        let orderInfo = await getOrderDetailFun(orderId, sid)
        if (orderInfo.code != 200) { return orderInfo }
        checkOrderPay(orderId, sid, OderIDCreated, codeInfo.CODE, payId, proxyId, bei,nowmealInfo.PRICE)
        return orderInfo.data
    }
    if (PLAN == 4) {
        //N个权益商品(N>0)+1个有券商品
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `N个权益商品(N>0)+1个有券商品`, '', 1])
        let TICKET = foodData.TICKET;//有券商品
        if (TICKET.length !== 1) { return { code: 501, message: '有券商品数量不正确' } }
        let POSITIVE = foodData.POSITIVE;//正价商品
        if (POSITIVE.length == 0) { return { code: 501, message: '正价商品数量不正确' } }
        //匹配账号
        let key = 0
        let sid = ""
        let couponInfo = null
        if (APPOINT) {
            //指定sid
            if (!VIPBAN) {
                return { code: 501, message: '未能查到可用的sid,没有' }
            } else {
                //指定账号并且已经拿到账号
                //获取优惠券
                let couponCodeIn = await getAccountCoupon(TICKET[0].code, 0, nowmealInfo)
                if (!couponCodeIn.ret) { return { code: 501, message: couponCodeIn.message, sid: VIPBAN } }
                //查询sid的券获取sid的券信息
                let getSidlll = await getSidCouponInfo(storeCode, VIPBAN, couponCodeIn.couponInfo.COUPON_ID)
                if (getSidlll.code == 502) { return { code: 502, message: "无优惠券，请重新进入下单", sid: VIPBAN } }
                //getSidlll.data
                couponInfo = { ID:couponCodeIn.couponInfo.ID, COUPON_ID: getSidlll.data.id, COUPON_CODE: getSidlll.data.code, PROMOTION_ID: getSidlll.data.promotionId }
                sid = VIPBAN
            }
        } else {
            do {
                let couponINfo = await getAccountCoupon(TICKET[0].code, key, nowmealInfo)
                if (!couponINfo.ret) { return { code: 501, message: couponINfo.message } }
                //取sid
                getSid = await getSidLimit(couponINfo.couponInfo.COUPON_SID_ID, couponINfo.couponInfo)
                key++
            } while (getSid.ret);
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '共计循环取出' + key + '次账号', '', 1])
            sid = getSid.sid
            couponInfo = getSid.couponInfo
            //修改sid的START参数
            let editSids = `UPDATE mdl_sid SET START = 1 WHERE SID = ?`
            await utils.dbUse(editSids, [sid]);
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '取出账号为' + sid, '', 1])
        //开始下单
        //清理购物车
        let peCart = await clearCart(storeCode, sid)
        if (peCart.code != 200) { return peCart }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '清理购物车完成', '', 1])
        //加入购物车
        //有券商品加入
        let addCartCouponResult = await addCartCoupon(TICKET, storeCode, sid, couponInfo)
        if (addCartCouponResult.code != 200) { return addCartCouponResult }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '有券商品加入购物车完成', '', 1])
        //使用优化后的券逻辑
        const validateResult = await validateCouponBeforeUse(couponInfo.ID, log_id);
        if (validateResult.code !== 200) {
            return validateResult;
        }
        await optimizedCouponLogic.markCouponAsUsed(couponInfo, null, sid, storeCode, null);
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `有券加入购物车成功,标记券为已使用code：${couponInfo.COUPON_CODE}`, '', 1])
        let vip = false;
        let MYKA_BREAKFAST_USE = false //会员早餐
        let MYKA_BREAKFAST = false
        if (nowmealInfo.MY_KA_USE) {
            vip = true
        }
        if (nowmealInfo.MYKA_BREAKFAST_USE) {
            MYKA_BREAKFAST_USE = true
        }
        if (nowmealInfo.BREAKFAST_CARD_USE) {
            MYKA_BREAKFAST = true
        }
        //正价商品加入
        let addCartPositiveResult = await addCartPositive(POSITIVE, storeCode, sid, vip, MYKA_BREAKFAST)
        if (addCartPositiveResult.code != 200) { return addCartPositiveResult }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '正价商品加入完成', '', 1])
        //获取购物车
        let cartInfo = await getSidCartInfo(storeCode, sid)
        if (cartInfo.code != 200) { return cartInfo }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '获取购物车完成', '', 1])
        //限制价格
        let limitPrice = parseFloat(nowmealInfo.PAYPRICELIMIT ? nowmealInfo.PAYPRICELIMIT : 100)
        let cartPrice = parseFloat(cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount)
        if (limitPrice !== 1 && cartPrice > limitPrice) {
            //超过金额限制
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `购物车金额${cartPrice}超过限制${limitPrice},下单返回`, '', 0])
            return { code: 501, message: '购物车金额超过限制', sid: sid }
        }
        let cashCouponCode = '', cashCouponId = '', payByBalance = nowmealInfo.BALANCE_PAYMENT
        //判断是否需要现金券下单
        let cashID = ''
        if (nowmealInfo.CASHCOUPON) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '需要现金券下单', '', 1])
            //获取现金券
            let cashInfo = await getCashCouponInfo(nowmealInfo.CASHCOUPON, sid, storeCode)
            if (cashInfo.code == 200) {
                cashCouponCode = cashInfo.data.couponCode;
                cashCouponId = cashInfo.data.couponId;
                if (cashInfo.data.cashID) {
                    cashID = cashInfo.data.cashID
                }
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了现金券code：${cashCouponCode},id:${cashCouponId}`, '', 1])
            } else {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `没有可用的抵用券`, '', 1])
            }

        }
        //提交订单
        let postOder = await postOderMdl(sid, '', cartInfo.data.confirmInfo.productPriceInfo.cartProductList, cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount, storeCode, TakeAMeal, cashCouponCode, cashCouponId, payByBalance, proxyId, cartInfo.data.confirmInfo.productPriceInfo.promotionList ? cartInfo.data.confirmInfo.productPriceInfo.promotionList : [], nowmealInfo)
        if (postOder.code != 200) { return postOder }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, '提交订单完成', '', 1])
        let { eatTypeCode, orderId, payId } = postOder.data
        if (cashID) {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET ORDER_ID = ?, VIP = ?,B_USE=? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [orderId, code, 1, cashID])
        }
        if (payByBalance) {
            //余额下单
            //如果余额下单的话，需要再次提交订单
            let preOrderResult = await preOrder(payId, sid)
            if (preOrderResult.data.code !== 200) { return { code: 501, message: '选择支付方式失败', sid: sid } }
            if (preOrderResult.data.data.responseCode == "SUCCESS") {
                //余额下单成功
                let payByBallanceResult = await payByBallance(payId, sid)
                if (payByBallanceResult.data.code == '0001') {
                    //余额不足
                    nowmealInfo.BALANCE_PAYMENT = 0
                    return { code: 502, message: '余额不足', nowmealInfo, sid: sid }
                }
            }
        }
        //创建订单数据
        let OderIDCreated = utils.createId()
        let createdOder = `INSERT INTO mdl_order (ID,CODE,CREATRD_TIME,EATTYPE,MEAL_IMG,MEAL_TITLE,ODER_STATUS,OPEN_ID,ODER_ID,PAY_ID,PAY_TEXT,PROXY_ID,LIMIT_PRICE,STORE_CODE,STORE_NAME,USER_IPHONE,ODER_IPHONE,MEAL_ID,PRICE_NUM,COUPON_ID,	COUPON_CODE) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`
        await utils.dbUse(createdOder, [
            OderIDCreated,
            codeInfo.CODE,
            utils.createTime(),
            eatTypeCode,
            nowmealInfo.IMAGE,
            nowmealInfo.TITLE,
            0,
            sid,
            orderId,
            payId,
            payByBalance ? '余额支付' : '支付宝支付',
            proxyId,
            limitPrice,
            storeCode,
            storeName,
            iphone,
            '',
            nowmealInfo.ID,
            cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount,
            couponInfo.COUPON_ID,
            couponInfo.COUPON_CODE
        ])
        //获取订单详情
        let orderInfo = await getOrderDetailFun(orderId, sid)
        if (orderInfo.code != 200) { return orderInfo }
        checkOrderPay(orderId, sid, OderIDCreated, codeInfo.CODE, payId, proxyId, null,nowmealInfo.PRICE)
        return orderInfo.data
    }
    if (PLAN == 5) {
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `N个权益商品(N>0)+N个正价商品`, '', 1])
        //N个权益商品(N>0)+N个正价商品
        let POSITIVE = foodData.POSITIVE;//正价商品
        if (POSITIVE.length == 0) { return { code: 501, message: '正价商品数量不正确' } }
        let TICKET = foodData.TICKET;//有券商品
        if (TICKET.length > 0) { return { code: 501, message: '有券商品数量不正确' } }
        //匹配账号
        //取权益账号sid
        let sid = ''
        if (APPOINT) {
            if (!VIPBAN) {
                return { code: 501, message: '未能查到可用的sid' }
            }
            sid = VIPBAN
        } else {
            let getSid = await getSidMdlRights(nowmealInfo)//权益账号
            if (getSid.code != 200) { return getSid }
            sid = getSid.sid
        }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `账号匹配成功${sid},开始下单`, '', 1])
        //开始下单
        //清理购物车
        let peCart = await clearCart(storeCode, sid)
        if (peCart.code != 200) { return peCart }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `清理购物车完成`, '', 1])
        let vip = false;
        let MYKA_BREAKFAST_USE = false //会员早餐
        let MYKA_BREAKFAST = false
        if (nowmealInfo.MY_KA_USE) {
            vip = true
        }
        if (nowmealInfo.MYKA_BREAKFAST_USE) {
            MYKA_BREAKFAST_USE = true
        }
        if (nowmealInfo.BREAKFAST_CARD_USE) {
            MYKA_BREAKFAST = true
        }
        //正价商品加入
        let addCartPositiveResult = await addCartPositive(POSITIVE, storeCode, sid, vip, MYKA_BREAKFAST, MYKA_BREAKFAST_USE)
        if (addCartPositiveResult.code != 200) { return addCartPositiveResult }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `正价商品加入完成`, '', 1])
        //获取购物车
        let cartInfo = await getSidCartInfo(storeCode, sid)

        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `获取购物车完成`, '', 1])
        if (cartInfo.code != 200) { return cartInfo }
        //限制价格
        let limitPrice = parseFloat(nowmealInfo.PAYPRICELIMIT ? nowmealInfo.PAYPRICELIMIT : 100)
        let cartPrice = parseFloat(cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount)
        if (limitPrice !== 1 && cartPrice > limitPrice) {
            //超过金额限制
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `购物车金额${cartPrice}超过限制${limitPrice},下单返回`, '', 0])
            return { code: 501, message: '购物车金额超过限制', sid: sid }
        }
        let cashCouponCode = '', cashCouponId = '', payByBalance = nowmealInfo.BALANCE_PAYMENT
        //判断是否需要现金券下单
        let cashID = ''
        if (nowmealInfo.CASHCOUPON) {
            //获取现金券
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `需要现金券下单`, '', 1])
            let cashInfo = await getCashCouponInfo(nowmealInfo.CASHCOUPON, sid, storeCode)
            if (cashInfo.code == 200) {
                cashCouponCode = cashInfo.data.couponCode;
                cashCouponId = cashInfo.data.couponId;
                if (cashInfo.data.cashID) {
                    cashID = cashInfo.data.cashID
                }
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `使用了现金券code：${cashCouponCode},id:${cashCouponId}`, '', 1])
            } else {
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `没有可用的抵用券`, '', 1])
            }

        }
        //提交订单
        let postOder = await postOderMdl(sid, '', cartInfo.data.confirmInfo.productPriceInfo.cartProductList, cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount, storeCode, TakeAMeal, cashCouponCode, cashCouponId, payByBalance, proxyId, cartInfo.data.confirmInfo.productPriceInfo.promotionList ? cartInfo.data.confirmInfo.productPriceInfo.promotionList : [], nowmealInfo)
        if (postOder.code != 200) { return postOder }
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `提交订单成功`, '', 1])
        let { eatTypeCode, orderId, payId } = postOder.data
        if (cashID) {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET ORDER_ID = ? ,VIP = ?,B_USE=? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [orderId, code, 1, cashID])
        }
        if (payByBalance) {
            //余额下单
            //如果余额下单的话，需要再次提交订单
            let preOrderResult = await preOrder(payId, sid)
            if (preOrderResult.data.code !== 200) { return { code: 501, message: '选择支付方式失败', sid: sid } }
            if (preOrderResult.data.data.responseCode == "SUCCESS") {
                //余额下单成功
                let payByBallanceResult = await payByBallance(payId, sid)
                if (payByBallanceResult.data.code == '0001') {
                    //余额不足
                    nowmealInfo.BALANCE_PAYMENT = 0
                    return { code: 502, message: '余额不足', nowmealInfo }
                }
            }
        }
        //创建订单数据
        let OderIDCreated = utils.createId()
        let createdOder = `INSERT INTO mdl_order (ID,CODE,CREATRD_TIME,EATTYPE,MEAL_IMG,MEAL_TITLE,ODER_STATUS,OPEN_ID,ODER_ID,PAY_ID,PAY_TEXT,PROXY_ID,LIMIT_PRICE,STORE_CODE,STORE_NAME,USER_IPHONE,ODER_IPHONE,MEAL_ID,PRICE_NUM) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`
        await utils.dbUse(createdOder, [
            OderIDCreated,
            codeInfo.CODE,
            utils.createTime(),
            eatTypeCode,
            nowmealInfo.IMAGE,
            nowmealInfo.TITLE,
            0,
            sid,
            orderId,
            payId,
            payByBalance ? '余额支付' : '支付宝支付',
            proxyId,
            limitPrice,
            storeCode,
            storeName,
            iphone,
            '',
            nowmealInfo.ID,
            cartInfo.data.confirmInfo.productPriceInfo.realTotalAmount
        ])
        //获取订单详情
        let orderInfo = await getOrderDetailFun(orderId, sid)
        if (orderInfo.code != 200) { return orderInfo }
        checkOrderPay(orderId, sid, OderIDCreated, codeInfo.CODE, payId, proxyId,null,nowmealInfo.PRICE)
        return orderInfo.data
    }
}


//查询sid的优惠券，根据couponid查
async function getSidCouponInfo(storeCode, sid, couponId) {
    let result = await getUserCouponInfo(storeCode, sid)
    if (result.code !== 200) {
        return { code: 502, message: '查券请求失败', sid: sid }
    }
    let couponList = result.data.productCouponType.coupons
    let userCoupon = []
    for (let i = 0; i < couponList.length; i++) {
        for (let j = 0; j < couponList[i].coupons.length; j++) {
            if (couponList[i].coupons[j].id == couponId) {
                userCoupon.push(couponList[i].coupons[j])
            }
        }
    }
    if (userCoupon.length == 0) {
        return { code: 502, message: '账号不存在该优惠券', sid: sid }
    }
    return { code: 200, data: userCoupon[0] }
}

//解锁sid
async function unlockSidOne(sid) {
    //修改sid的状态
    let sql = `UPDATE mdl_sid SET START = ? WHERE SID = ?`
    await utils.dbUse(sql, [0, sid])
}
//验证指定sid
async function verifySid(sid) {
    //判断当前账号是都在数据库中存在
    let sql = `SELECT * FROM mdl_sid WHERE SID = ?`
    let sidInfo = await utils.dbUse(sql, [sid])
    if (sidInfo.length == 0) {
        // 当前账号不存在 
        let sql = `INSERT INTO mdl_sid (ID,SID,PARENT_MENU,START,CREATE_TIME) VALUES (?,?,?,?,?)`
        await utils.dbUse(sql, [utils.createId(), sid, '指定账号', 0, utils.createTime()])
        let redLockRe = await getSidGBF(sid)
        if (redLockRe.code == 501) {
            return { code: 501, message: '当前账号不可用' }
        } else {
            return { code: 200, sid: sid }
        }
    } else {
        if (sidInfo[0].START == 1) {
            //当前账号已经存在,并正在下单
            return { code: 501, message: '当前账号不可用' }
        } else {
            let redLockRe = await getSidGBF(sid)
            if (redLockRe.code == 501) {
                return { code: 501, message: '当前账号不可用' }
            } else {
                return { code: 200, sid: sid }
            }
        }
    }
}
//根据分类无规则取号
async function getSidMdlRandom(account) {
    //随机取号，排除禁用的账号
    let sql = `SELECT * FROM mdl_sid WHERE PARENT_MENU = ? AND LOGIN_CODE =? AND START =? AND (ENABLED IS NULL OR ENABLED = 1)`
    let sidInfo = await utils.dbUse(sql, [account, 1, 0])
    if (sidInfo.length == 0) { return { code: 501, message: '当前无账号' } }
    let keys = 0
    let result = null
    do {
        if (keys > sidInfo.length - 1) { return { code: 501, message: '当前无账号' } }
        result = await getSidGBF(sidInfo[keys].SID)
        keys++
    } while (result.code == 501);
    return { code: 200, sid: result.sid }
}
//取权益号
async function getSidMdlRights(meal) {

    let sql = `SELECT * FROM mdl_sid WHERE QY = ? AND LOGIN_CODE =? AND START =? AND (ENABLED IS NULL OR ENABLED = 1) ${meal.MY_KA_USE ? ' AND MY_KA_USE > 0' : ''} ${meal.MYKA_BREAKFAST_USE ? ' AND MYKA_BREAKFAST_USE = 1' : ''} ${meal.COFFEE_USE ? ' AND COFFEE_USE > 0' : ''} ${meal.BENEFITS_USE ? ' AND BENEFITS_USE > 0' : ''} ${meal.FREE_DELIVERY_USE ? ' AND FREE_DELIVERY_USE > 0 ' : ''} ${meal.XINPINCHANG_USE ? ' AND XINPINCHANG_USE > 0 ' : ''} ${meal.BREAKFAST_CARD_USE ? ' AND BREAKFAST_CARD_USE >0 ' : ''}`
    let sidInfo1 = await utils.dbUse(sql, [1, 1, 0])

    let sidInfo = []
    if (meal.BANK_CARD_VIP || meal.BANK_CARD_BREAKFAST) {
        for (let i = 0; i < sidInfo1.length; i++) {
            if (sidInfo1[i].PARENT_MENU !== 'fd61b920-4dc9-11ef-bdb5-09aba16c0e86') {
                //fd61b920-4dc9-11ef-bdb5-09aba16c0e86这个id的库不能删除
                sidInfo.push(sidInfo1[i])
            }
        }
    } else {
        sidInfo = sidInfo1
    }

    if (sidInfo.length == 0) {
        if (meal.BANK_CARD_VIP == 1 || meal.BANK_CARD_BREAKFAST == 1) {
            let sqlSDG = 'SELECT * FROM mdl_sid WHERE PARENT_MENU = ? AND START =? AND (ENABLED IS NULL OR ENABLED = 1)'
            let sdgOpen = await utils.dbUse(sqlSDG, ['fd61b920-4dc9-11ef-bdb5-09aba16c0e86', 0])
            if (sdgOpen.length == 0) { return { code: 501, message: '当前备用权益空' } }
            let bsid = await getSidGBF(sdgOpen[0].SID)
            if (bsid.code == 501) { return { code: 501, message: '备用权益冲突' } }
            return { code: 200, sid: sdgOpen[0].SID, bei: true }
        }
    }

    if (sidInfo.length == 0) {
        return { code: 501, message: '当前账号已用完' }
    }


    let keys = 0
    let result = null
    do {
        if (keys > sidInfo.length - 1) { return { code: 501, message: '当前无符合条件的权益账号' } }
        result = await getSidGBF(sidInfo[keys].SID)
        keys++
    } while (result.code == 501);
    // 修改sid的状态
    let strSplice = `${meal.MY_KA_USE ? ',MY_KA_USE = 0' : ''} ${meal.MYKA_BREAKFAST_USE ? ',MYKA_BREAKFAST_USE = 0' : ''} ${meal.COFFEE_USE ? ',COFFEE_USE = 0' : ''} ${meal.BENEFITS_USE ? ',BENEFITS_USE = 0' : ''} ${meal.FREE_DELIVERY_USE ? ',FREE_DELIVERY_USE = 0' : ''} ${meal.XINPINCHANG_USE ? ',XINPINCHANG_USE = 0' : ''} ${meal.BREAKFAST_CARD_USE ? ',BREAKFAST_CARD_USE = 0' : ''} `
    let sqle = `UPDATE mdl_sid SET  LOGIN_CODE = 1 ${strSplice} WHERE SID = ?`
    await utils.dbUse(sqle, [result.sid])
    return { code: 200, sid: result.sid, bei: false }
}
//防止高并发取号冲突
async function getSidGBF(sid) {
    let ttl = 100000;
    let lock = null;
    try {
        // 在锁保护的临界区执行操作
        lock = await redis.lockResource(sid, ttl);
        //修改sid的START参数
        let editSids = `UPDATE mdl_sid SET START = 1 WHERE SID = ?`
        await utils.dbUse(editSids, [sid]);
        return { code: 200, sid: sid }
    } catch (err) {
        return { code: 501, message: '取号失败' }
    } finally {
        redis.unlockResource(lock);
    }
}

//清理购物车
async function clearCart(storeCode, sid) {
    let clear = await clearCartSid(storeCode, sid)
    if (clear.data.code !== 200) { return { code: 502, message: clear.data.message, sid: sid } }
    return { code: 200, message: clear.data.message }
}
//正价商品加入购物车
async function addCartPositive(POSITIVE, storeCode, sid, vip, breakfast, MYKA_BREAKFAST_USE, suidangou) {
    errLog({err: null, code: 200, msg: `[购物车] 开始添加普通商品，店铺代码：${storeCode}，SID：${sid}`, funName: "info"});
    errLog({err: null, code: 200, msg: `[购物车] 普通商品列表(POSITIVE): ${JSON.stringify(POSITIVE, null, 2)}`, funName: "info"});
    errLog({err: null, code: 200, msg: `[购物车] 普通商品数量: ${POSITIVE.length}`, funName: "info"});
    errLog({err: null, code: 200, msg: `[购物车] 其他参数: ${JSON.stringify({ vip, breakfast, MYKA_BREAKFAST_USE, suidangou })}`, funName: "info"});
    //MYKA_BREAKFAST_USE 会员早餐
    let productType = '1'
    let membershipCode = ""
    let cardId = ""
    let cardType = "0"
    if (suidangou && vip) {
        membershipCode = '106';
    }
    if (suidangou) {
        if (vip || breakfast) {
            productType = '0'
        }
    }

    if (suidangou && breakfast) {
        cardType = '2'
    }
    if (suidangou && breakfast) {
        cardId = "CRD9311B796110E942234F726EF4B4FE994"
    }

    for (let i = 0; i < POSITIVE.length; i++) {
        errLog({err: null, code: 200, msg: `[购物车] 处理第${i+1}个普通商品: ${POSITIVE[i].name} (${POSITIVE[i].code})`, funName: "info"});
        let { comboItems, customization } = await customizationFood(POSITIVE[i])
        errLog({err: null, code: 200, msg: `[购物车] 普通商品addCartSid调用参数: ${JSON.stringify({
            storeCode,
            sid,
            productCode: POSITIVE[i].code,
            couponCode: '',
            couponId: '',
            promotionId: '',
            customization: JSON.stringify(customization),
            comboItems: JSON.stringify(comboItems),
            membershipCode,
            productType,
            cardType,
            cardId
        })}`, funName: "info"});
        let addResult = await addCartSid(storeCode, sid, POSITIVE[i].code, '', '', '', customization, comboItems, membershipCode, productType, cardType, cardId)
        errLog({err: null, code: 200, msg: `[购物车] 普通商品addCartSid返回结果：${JSON.stringify(addResult.data)}`, funName: "info"});
        if (addResult.data.code == 10004) { return { code: 501, message: addResult.data.message, sid } }
        if (addResult.data.code == 708018) { return { code: 501, message: addResult.data.message, sid } }
        if (addResult.data.code !== 200) { return { code: 502, message: POSITIVE[i].name + sid + addResult.data.message, sid: sid } }
    }
    return { code: 200, message: "加入购物车成功" }
}
//验证订单是否支付，15分钟内每3秒验证一次，如果支付成功就停止，没有支付则一直验证，直到15分钟
//验证订单是否支付，15分钟内每3秒验证一次，如果支付成功就停止，没有支付则一直验证，直到15分钟
async function checkOrderPay(orderId, sid, OderIDCreated, codee, payId, proxyId, bei,dprice) {
    let payResult = null
    let time = 0
    let timeOut = 1500
    let timeInterval = 3
    dprice = parseFloat(dprice)
    if(!dprice){
        dprice = 1
    }
    do {
        payResult = await getOrderDetail(orderId, sid)
        if (payResult.data.code !== 200) {
            time = 1500
        } else {
            if (payResult.data.code == 200 && payResult.data.data.orderStatusCode == '10' || payResult.data.data.orderStatusCode == '3' || payResult.data.data.orderStatusCode == '2' || payResult.data.data.orderStatusCode == '6') {
                //已支付
                try {
                //添加余额扣款
                //增加余额明细
                let adddetail = `INSERT INTO particulars(id, oderId, myoderId, code, proxyId, createdTime,price) VALUES (?,?,?,?,?,?,?)`
                await utils.dbUse(adddetail, [utils.createId(),orderId,OderIDCreated,codee,proxyId,utils.createTime(),dprice]);
                let upBane = `UPDATE user SET balance = balance - ? WHERE id = ?`
                await utils.dbUse(upBane, [dprice,proxyId]);
                } catch (error) {
                    console.log(error)
                    console.log("明细添加异常")
                }
                //修改订单信息
                let editOrder = `UPDATE mdl_order SET PICKUP_CODE = ?,USE_TIME = ?, ODER_IPHONE=?, ODER_STATUS = ? WHERE ID = ?`
                await utils.dbUse(editOrder, [payResult.data.data.pickupCode, payResult.data.data.createTime, '', 1, OderIDCreated]);
                //修改兑换码状态
                let editCoupon = `UPDATE mdl_code SET CODE_STATUS = 2 WHERE ID = ?`
                await utils.dbUse(editCoupon, [codee]);
                //解锁sid
                unlockSidOne(sid)
                if (bei) {
                    editSidClass(sid)
                }
                return { code: 200, message: "订单支付成功" }
            } else {
                //   if(time == 300){
                //       console.log("300再次提交")
                //       return_order(sid,orderId,payId,proxyId)
                //   }
                //   if(time == 600){
                //         console.log("600再次提交")
                //       return_order(sid,orderId,payId,proxyId)
                //   }
                //   if(time == 900){
                //         console.log("900再次提交")
                //       return_order(sid,orderId,payId,proxyId)
                //   }
            }
        }
        await utils.sleep(timeInterval * 1000)
        time += timeInterval
    } while (time < timeOut)
    //修改订单信息
    let editOrder1 = `UPDATE mdl_order SET ODER_STATUS = ? WHERE ID = ?`
    await utils.dbUse(editOrder1, [3, OderIDCreated]);
    let editCouponls = `UPDATE mdl_code SET CODE_STATUS = 4 WHERE ID = ?`
    await utils.dbUse(editCouponls, [codee]);
    unlockSidOne(sid)
}
//修改sid仓库为权益账号
function editSidClass(sid) {
    let sql = `UPDATE mdl_sid SET PARENT_MENU = ? WHERE SID = ?`
    utils.dbUse(sql, ['8a645b20-4dca-11ef-bdb5-09aba16c0e86', sid])
    qxFind(sid)
}
router.post("/soldOut",async(req,res)=>{
        let {CODE} =req.body
        let sql = 'UPDATE mdl_code SET CODE_STATUS= 1 WHERE CODE=?'
        await utils.dbUse(sql, [CODE])
        res.json({
            code:200,
            msg:"下架完成"
        })
    })
//加入有券购物车
/** 
   * 将优惠券商品添加到购物车 
   * 
   * @async 
   * @function addCartCoupon 
   * @param {Array} TICKET - 商品列表，包含商品的代码、名称、定制选项等信息 
   * @param {string} storeCode - 店铺代码 
   * @param {string} sid - 会话ID，用于标识用户的购物车 
   * @param {Object} couponInfo - 优惠券信息 
   * @param {string} couponInfo.COUPON_CODE - 优惠券代码 
   * @param {string} couponInfo.COUPON_ID - 优惠券ID 
   * @param {string} couponInfo.PROMOTION_ID - 促销ID 
   * @param {string} couponInfo.COUPON_TITLE - 优惠券标题 
   * @param {string} couponInfo.ID - 数据库中的优惠券记录ID 
   * @returns {Promise<Object>} 返回添加结果，包含状态码和消息 
   * @returns {number} returns.code - 状态码：200成功，501不在使用时间范围内，502其他错误 
   * @returns {string} returns.message - 结果消息 
   * @returns {string} [returns.gfMsg] - 原始错误消息 
   * @returns {string} [returns.sid] - 会话ID 
   */ 
  async function addCartCoupon(TICKET, storeCode, sid, couponInfo) { 
      errLog({err: null, code: 200, msg: `[购物车] 开始添加优惠券商品，店铺代码：${storeCode}，SID：${sid}，优惠券：${couponInfo.COUPON_TITLE}(${couponInfo.COUPON_CODE})`, funName: "info"}); 
      errLog({err: null, code: 200, msg: `[购物车] 优惠券详细信息: ${JSON.stringify(couponInfo, null, 2)}`, funName: "info"});
      errLog({err: null, code: 200, msg: `[购物车] 商品列表(TICKET): ${JSON.stringify(TICKET, null, 2)}`, funName: "info"});
      errLog({err: null, code: 200, msg: `[购物车] 商品数量: ${TICKET.length}`, funName: "info"}); 
  
      for (let i = 0; i < TICKET.length; i++) { 
          // 获取商品定制选项 
          errLog({err: null, code: 200, msg: `[购物车] 处理第${i+1}个商品：${TICKET[i].name}(${TICKET[i].code})`, funName: "info"}) 
          let { comboItems, customization } = await customizationFood(TICKET[i])
          
          // 针对9900009274优惠券的特殊处理
          if (couponInfo.COUPON_CODE === '9900009274') {
              errLog({err: null, code: 200, msg: `[购物车] 检测到9900009274优惠券，进行特殊处理`, funName: "info"})
              let specialData = {
                  comboItems: comboItems,
                  customization: customization,
                  productInfo: TICKET[i]
              }
              let processedData = specialHandling9900009274(couponInfo.COUPON_CODE, specialData)
              comboItems = processedData.comboItems
              customization = processedData.customization
              errLog({err: null, code: 200, msg: `[购物车] 9900009274特殊处理完成`, funName: "info"})
          }
          
          // 针对9900006034优惠券的特殊处理
          if (couponInfo.COUPON_CODE === '9900006034') {
              errLog({err: null, code: 200, msg: `[购物车] 检测到9900006034优惠券，进行特殊处理`, funName: "info"})
              let specialData = {
                  comboItems: comboItems,
                  customization: customization,
                  productInfo: TICKET[i]
              }
              let processedData = specialHandling9900006034(couponInfo.COUPON_CODE, specialData)
              comboItems = processedData.comboItems
              customization = processedData.customization
              errLog({err: null, code: 200, msg: `[购物车] 9900006034特殊处理完成`, funName: "info"})
          } 
          
          // 处理新店专享券，直接添加优惠券到购物车 
          if (couponInfo.COUPON_TITLE == '【新店专享】麦辣鸡腿堡11.9') { 
              errLog({err: null, code: 200, msg: `[购物车] 检测到新店专享券，使用couponListAddCart方法添加`, funName: "info"}); 
              errLog({err: null, code: 200, msg: `[购物车] couponListAddCart调用参数: ${JSON.stringify({
                  storeCode,
                  couponCode: couponInfo.COUPON_CODE,
                  couponId: couponInfo.COUPON_ID,
                  promotionId: couponInfo.PROMOTION_ID,
                  sid
              })}`, funName: "info"});
              
              let addCouponResult = await couponListAddCart(storeCode, couponInfo.COUPON_CODE, couponInfo.COUPON_ID, couponInfo.PROMOTION_ID, sid) 
              errLog({err: null, code: 200, msg: `[购物车] couponListAddCart返回结果：${JSON.stringify(addCouponResult)}`, funName: "info"}) 
              
              // 检查优惠券是否在使用时间范围内 
              if (addCouponResult.message == '此卡券不在使用时间内，请在有效时间内来使用哦！') { 
                  errLog({err: null, code: 200, msg: `[购物车] 优惠券不在使用时间范围内`, funName: "info"}) 
                  return { code: 501, message: '不在使用范围内哦!', gfMsg: addCouponResult.message, sid: sid } 
              } 
              
              // 处理其他错误情况 
              if (addCouponResult.code !== 200) { 
                  errLog({err: null, code: 200, msg: `[购物车] 添加优惠券失败，错误码：${addCouponResult.code}，错误信息：${addCouponResult.message}`, funName: "info"}) 
                  
                  // 如果优惠券已用完，从数据库中删除 
                  if (addCouponResult.code == 11010 || addCouponResult.message == '当前优惠券次数已用完！') { 
                      errLog({err: null, code: 200, msg: `[购物车] 优惠券已用完，从数据库中删除ID：${couponInfo.ID}`, funName: "info"}) 
                      let deletSql = `DELETE FROM mdl_coupon WHERE ID = ?` 
                      await utils.dbUse(deletSql, [couponInfo.ID]); 
                  } 
                  
                  return { code: 502, message: TICKET[i].name + sid + addCouponResult.message, gfMsg: addCouponResult.message, sid: sid } 
              } 
          } else { 
              // 处理其他类型的优惠券，先调整商品组合，再添加到购物车 
              errLog({err: null, code: 200, msg: `[购物车] 处理普通优惠券商品`, funName: "info"}); 
              
              // 针对特定优惠券（如"两份汉堡任选"）调整商品组合 
              errLog({err: null, code: 200, msg: `[购物车] 调整商品组合，商品名称：${TICKET[i].name}`, funName: "info"}) 
              comboItems = await couponAdjustment(TICKET[i].name, comboItems) 
              
              // 添加商品到购物车 
              errLog({err: null, code: 200, msg: `[购物车] 使用addCartSid方法添加商品到购物车`, funName: "info"}); 
              errLog({err: null, code: 200, msg: `[购物车] addCartSid调用参数: ${JSON.stringify({
                  storeCode,
                  sid,
                  productCode: TICKET[i].code,
                  couponCode: couponInfo.COUPON_CODE,
                  couponId: couponInfo.COUPON_ID,
                  promotionId: couponInfo.PROMOTION_ID,
                  customization: JSON.stringify(customization),
                  comboItems: JSON.stringify(comboItems),
                  productInfo: {
                      name: TICKET[i].name || '',
                      price: TICKET[i].price || 0,
                      isChoices: TICKET[i].isChoices || false,
                      choicesCode: TICKET[i].choicesCode || ''
                  }
              })}`, funName: "info"});
              
              let addResult = await addCartSid(storeCode, sid, TICKET[i].code, couponInfo.COUPON_CODE, couponInfo.COUPON_ID, couponInfo.PROMOTION_ID, customization, comboItems, '', '1', '0', '', {
                  name: TICKET[i].name || '',
                  price: TICKET[i].price || 0,
                  isChoices: TICKET[i].isChoices || false,
                  choicesCode: TICKET[i].choicesCode || ''
              }) 
              errLog({err: null, code: 200, msg: `[购物车] addCartSid返回结果：${JSON.stringify(addResult.data)}`, funName: "info"}) 
              
              // 检查优惠券是否在使用时间范围内 
              if (addResult.data.message == '此卡券不在使用时间内，请在有效时间内来使用哦！') { 
                  errLog({err: null, code: 200, msg: `[购物车] 优惠券不在使用时间范围内`, funName: "info"}) 
                  return { code: 501, message: '不在使用范围内哦!', gfMsg: addResult.data.message, sid: sid } 
              } 
              
              // 处理其他错误情况 
              if (addResult.data.code !== 200) { 
                  errLog({err: null, code: 200, msg: `[购物车] 添加商品失败，错误码：${addResult.data.code}，错误信息：${addResult.data.message}`, funName: "info"}) 
                  
                  // 如果优惠券已用完，从数据库中删除 
                  if (addResult.data.code == 11010 || addResult.data.message == '当前优惠券次数已用完！') { 
                      errLog({err: null, code: 200, msg: `[购物车] 优惠券已用完，从数据库中删除ID：${couponInfo.ID}`, funName: "info"}) 
                      let deletSql = `DELETE FROM mdl_coupon WHERE ID = ?` 
                      await utils.dbUse(deletSql, [couponInfo.ID]); 
                  } 
                  
                  return { code: 502, message: TICKET[i].name + sid + addResult.data.message, gfMsg: addResult.data.message, sid: sid } 
              } 
          } 
      } 
      
      errLog({err: null, code: 200, msg: `[购物车] 所有优惠券商品添加成功`, funName: "info"}) 
      return { code: 200, message: "加入购物车成功" } 
  }
//获取购物车
async function getSidCartInfo(storeCode, sid) {
    let sidCart = await getCartInfo(storeCode, sid)
    if (sidCart.data.code !== 200) {
        return { code: 501, message: sidCart.data.message, sid: sid }
    }
    return { code: 200, message: sidCart.data.message, data: sidCart.data.data }
}
//提交订单
async function postOderMdl(sid, cardId, cartItems, realTotalAmount, storeCode, eatType, cashCouponCode, cashCouponId, payByBalance, proxyId, promotionList, nowmealInfo, bei) {
    let order = await makeOrderDirectly(sid, cardId, cartItems, realTotalAmount, storeCode, eatType, cashCouponCode, cashCouponId, payByBalance, proxyId, promotionList)
    if (order.data.code !== 200) {
        let da = { sid, cardId, cartItems, realTotalAmount, storeCode, eatType, cashCouponCode, cashCouponId, payByBalance, proxyId, promotionList }
        return { code: 501, message: order.data.message, nowmealInfo, sid: sid, bei, da }
    }
    return {
        code: 200, message: order.data.message, data: order.data.data
    }
}
//获取订单详情
async function getOrderDetailFun(oderId, sid) {
    let oder = await getOrderDetail(oderId, sid)
    if (oder.data.code !== 200) { return { code: 501, message: oder.data.message } }
    return { code: 200, message: oder.data.message, data: oder.data.data }
}
//获取现金券
async function getCashCouponInfo(classId, sid, storeCode) {
    //通过分类id查找券
    let sql = `SELECT * FROM mdl_cash_coupon WHERE CREATED_CLASS_ID = ?`
    let cashCoupon = await utils.dbUse(sql, [classId])
    if (!cashCoupon[0]) { return { code: 501, message: '没有可用的现金券' } }
    let COUPON_TITLE = cashCoupon[0].COUPON_TITLE //现金券名称
    //先查找用户账号是否有当前现金券
    let userCashCoupon = await getUserCouponInfo(storeCode, sid)
    if (userCashCoupon.code !== 200) {
        return { code: 502, message: '查券请求失败', sid: sid }
    }
    let couponList = userCashCoupon.data.productCouponType.coupons
    let userCoupon = []
    for (let i = 0; i < couponList.length; i++) {
        for (let j = 0; j < couponList[i].coupons.length; j++) {
            if (couponList[i].coupons[j].title == COUPON_TITLE) {
                userCoupon.push(couponList[i].coupons[j])
            }
        }
    }
    //判断是否有
    if (userCoupon.length > 0) {
        return { code: 200, message: '用户有当前现金券', data: { couponId: userCoupon[0].id, couponCode: userCoupon[0].code } }
    }
    let findsh = 'SELECT * FROM mdl_cash_coupon WHERE CREATED_CLASS_ID = ? AND AVAILABLE_QUANTITY = ?'
    cashCoupon = await utils.dbUse(sql, [classId, 1])
    //没有就查找是否有券
    //给用用户充值
    let key = 0;
    let exprotCouponResult = null

    do {
        let exportCoupon = await doExchangeCoupon(sid, cashCoupon[key].COUPON_CODE)//COUPON_CODE
        if (exportCoupon.data.code == 200) {
            //充值成功
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET AVAILABLE_QUANTITY = ? ,USE_DATA =? ,SID = ?  WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [0, '现金券code：' + cashCoupon[key].COUPON_CODE + '充值给账号:' + sid + '成功！', sid, cashCoupon[key].ID])

            //查询用户券
            let exprotUserCashCoupon = await getUserCouponInfo(storeCode, sid)
            if (exprotUserCashCoupon.code !== 200) {
                return { code: 501, message: '查券请求失败', sid: sid }
            }
            let couponListExprot = exprotUserCashCoupon.data.productCouponType.coupons
            let exprotuserCoupon = []
            for (let i = 0; i < couponListExprot.length; i++) {
                for (let j = 0; j < couponListExprot[i].coupons.length; j++) {
                    if (couponListExprot[i].coupons[j].title == COUPON_TITLE) {
                        exprotuserCoupon.push(couponListExprot[i].coupons[j])
                    }
                }
            }
            if (exprotuserCoupon.length > 0) {
                return { code: 200, message: '用户有当前现金券', data: { couponId: exprotuserCoupon[0].id, couponCode: exprotuserCoupon[0].code, cashID: cashCoupon[key].ID } }
            } else {
                return { code: 501, message: "现金券充值成功，但未找到" }
            }
        } else {
            //修改当前券的状态
            let updateCouponSql = `UPDATE mdl_cash_coupon SET AVAILABLE_QUANTITY = ? ,USE_DATA =? WHERE ID = ?`
            await utils.dbUse(updateCouponSql, [0, '现金券code：' + cashCoupon[key].COUPON_CODE + '不能使用,原因:' + exportCoupon.data.message, cashCoupon[key].ID])
            exprotCouponResult = { ret: true, message: exportCoupon.data.message }
            if (key == cashCoupon.length - 1) {
                return { code: 501, message: '现金券已用完，请联系管理', sid: sid }
            } else {
                key++
            }
        }
    } while (exprotCouponResult.ret);
}

//127参数定制
async function customizationFood(food) {
    let comboItems = []
    let customization = {
        items: [],
        options: []
    }
    // type类型等于1的话，并且定制，就证明
    if (food.type == 1 && food.customizationMode) {
        if (food.customization.options[0]) {
            //多选
            let discountArrCustomization = food.customization.options
                for (let keyi = 0; keyi < discountArrCustomization.length; keyi++) {
                if (discountArrCustomization[keyi].checked == 0) {
                    let keyiobj = {
                        checked: discountArrCustomization[keyi].checked,
                        code: discountArrCustomization[keyi].code
                    }
                    customization.options.push(keyiobj)
                }
            }

        } else {
            let discountArrCustomization = food.customization.items
            //单选
            let opobj = {
                code: discountArrCustomization[0].code,
                values: discountArrCustomization[0].values
            }
            customization.items.push(opobj)
        }

    }
    if ((food.type == 2 || food.type == 7) && food.comboItems) {
        for (let y = 0; y < food.comboItems.length; y++) {
            for (let k = 0; k < food.comboItems[y].comboProducts.length; k++) {
                if (food.comboItems[y].comboProducts[k].isDefault === 1) {
                    let obj = {
                        "code": food.comboItems[y].comboProducts[k].code,
                        "hasCustomization": 0,
                        "round": food.comboItems[y].round
                    }
                    if (food.comboItems[y].comboProducts[k].hasCustomization) {
                        if (food.comboItems[y].comboProducts[k].customization.items[0]) {
                            obj.hasCustomization = 1;
                            obj.items = []
                            if (food.comboItems[y].comboProducts[k].customization) {
                                for (let b = 0; b < food.comboItems[y].comboProducts[k].customization.items[0].values.length; b++) {
                                    if (food.comboItems[y].comboProducts[k].customization.items[0].values[b].checked == 1) {
                                        obj.items.push({
                                            "code": food.comboItems[y].comboProducts[k].customization.items[0].code,
                                            "values": [
                                                {
                                                    "code": food.comboItems[y].comboProducts[k].customization.items[0].values[b].code,
                                                    "checked": food.comboItems[y].comboProducts[k].customization.items[0].values[b].checked
                                                }
                                            ]
                                        })
                                    }
                                }
                            } else {
                                obj.hasCustomization = 0
                            }
                        } else {
                            obj.hasCustomization = 1;
                            obj.options = []
                            if (food.comboItems[y].comboProducts[k].customization && food.comboItems[y].comboProducts[k].hasCustomization) {
                                for (let b = 0; b < food.comboItems[y].comboProducts[k].customization.options.length; b++) {
                                    if (food.comboItems[y].comboProducts[k].customization.options[b].checked == 0) {
                                        obj.options.push({
                                            code: food.comboItems[y].comboProducts[k].customization.options[b].code,
                                            checked: food.comboItems[y].comboProducts[k].customization.options[b].checked
                                        })
                                    }
                                }
                            }
                            if (obj.options.length == 0) {
                                obj.hasCustomization = 0
                            }
                        }
                    } else {
                        obj.options = []
                    }
                    comboItems.push(obj)
                }
            }
        }
    }
    return { comboItems: comboItems, customization: customization }
}
//根据兑换码信息获取套餐信息
async function getMealLimitCode(codeInfo) {
    let findGMCSql = `SELECT * FROM mdl_code_tab_meal WHERE MDL_CODE_TAB_ID = ?`
    let fatherrInfo = await utils.dbUse(findGMCSql, [codeInfo.FATHERR_ID]);
    if (fatherrInfo.length == 0) { return { ret: false, message: "套餐不存在" } }
    let mealArrs = []
    for (let i = 0; i < fatherrInfo.length; i++) {
        let findMeal = `SELECT * FROM mdl_combo_meal WHERE ID = ?`
        let mealInfo = await utils.dbUse(findMeal, [fatherrInfo[i].MDL_MEAL_ID]);
        if (mealInfo.length == 0) { return { ret: false, message: "套餐不存在" } }
        mealArrs.push(mealInfo[0])
    }
    return { ret: true, mealInfo: mealArrs }
}

async function editCodeStatu(res, code) {
    let ttl = 100000;
    let lock = null;
    try {
        // 在锁保护的临界区执行操作
        lock = await redis.lockResource(code, ttl);
        let sql = `UPDATE mdl_code SET CODE_STATUS = 3 WHERE CODE = ?`;
        let editCode = await utils.dbUse(sql, [code]);
        if (editCode.affectedRows == 0) {
            return res.json({ code: 500, message: "兑换码下单中" })
        }
    } catch (err) {
        return res.json({ code: 500, message: "非法操作" })
    } finally {
        redis.unlockResource(lock);
    }
}
//券匹配账号
async function getAccountCoupon(TICKET_code, key, nowmealInfo) {
    let ACCOUNTClass = ''
    let couponIDs = ''
    if (nowmealInfo) {
        let couponIDsJson = JSON.parse(nowmealInfo.TICKET)
        couponIDs = couponIDsJson[0]
        ACCOUNTClass = nowmealInfo.ACCOUNT
    }

    if (couponIDs) {
        //新增修复bug
        let sql = `SELECT * FROM mdl_product WHERE ID = ?`;
        let foodInfo = await utils.dbUse(sql, [couponIDs]);
        if (!foodInfo[0] || !foodInfo[0].COUPON_ID) { return { ret: false, message: "查不到该" + TICKET_code + "商品    ,或该商品没有couponid" } }
        let findCoupon = `SELECT * FROM mdl_coupon WHERE CLASSIFYID LIKE "%${ACCOUNTClass || ''}%" AND CURRENT_DAY_AVAILABLE_COUNT > 0 AND COUPON_ID = ? ORDER BY LOGTIME ASC `;
        let couponInfo = await utils.dbUse(findCoupon, [foodInfo[0].COUPON_ID]);
        if (couponInfo.length == 0) { return { ret: false, message: "查不到该" + foodInfo[0].COUPON_ID + "券," + foodInfo[0].FOOD_TITLE + "券已用完" } }
        if (!couponInfo[key]) { return { ret: false, message: "当前有少量券，但账号都在任务中" } }
        return { ret: true, couponInfo: couponInfo[key] }
    } else {
        //根据code搜索商品表的数据
        let sql = `SELECT * FROM mdl_product WHERE PRODUCT_CODE = ? OR MANY_CODE LIKE "%${TICKET_code || ''}%"`;
        let foodInfo = await utils.dbUse(sql, [TICKET_code, TICKET_code]);
        if (!foodInfo[0] || !foodInfo[0].COUPON_ID) { return { ret: false, message: "查不到该" + TICKET_code + "商品    ,或该商品没有couponid" } }
        let findCoupon = `SELECT * FROM mdl_coupon WHERE  CLASSIFYID LIKE "%${ACCOUNTClass || ''}%" AND CURRENT_DAY_AVAILABLE_COUNT > 0 AND COUPON_ID = ? ORDER BY LOGTIME ASC `;
        let couponInfo = await utils.dbUse(findCoupon, [foodInfo[0].COUPON_ID]);
        if (couponInfo.length == 0) { return { ret: false, message: "查不到该" + foodInfo[0].COUPON_ID + "券," + foodInfo[0].FOOD_TITLE + "券已用完" } }
        if (!couponInfo[key]) { return { ret: false, message: "当前有少量券，但账号都在任务中" } }
        return { ret: true, couponInfo: couponInfo[key] }
    }

}
//取sid
async function getSidLimit(sid, couponInfo) {
    let ttl = 100000;
    let lock = null;
    try {
        // 在锁保护的临界区执行操作
        lock = await redis.lockResource(sid, ttl);
        let findSidSTART = `SELECT * FROM mdl_sid WHERE SID = ?`
        let sidInfoCoupon = await utils.dbUse(findSidSTART, [sid]);
        if (sidInfoCoupon[0].START) {
            return { ret: true, message: "sid正在使用" }
        }
        return { ret: false, sid: sid, couponInfo: couponInfo }
    } catch (err) {
        return { ret: true, message: "sid正在使用" }
    } finally {
        redis.unlockResource(lock);
    }
}


// ==================== 新增功能函数 ====================

/**
 * 新增：检查券可用性的中间件函数
 * 在使用券之前先检查其可用性
 */
async function validateCouponBeforeUse(couponId, log_id) {
    try {
        const checkResult = await optimizedCouponLogic.checkCouponAvailability(couponId);
        
        if (!checkResult.available) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                [utils.createId(), log_id, `券不可用: ${checkResult.reason}`, '', 0])
            return { code: 501, message: checkResult.reason };
        }
        
        return { code: 200, couponInfo: checkResult.couponInfo };
    } catch (error) {
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
            [utils.createId(), log_id, `券验证失败: ${error.message}`, '', 0])
        return { code: 501, message: `券验证失败: ${error.message}` };
    }
}

/**
 * 新增：订单取消时撤销券使用的函数
 * 当订单被取消时，需要恢复券的可用状态
 */
async function handleOrderCancellation(orderId, log_id) {
    try {
        const revokeResult = await optimizedCouponLogic.revokeCouponUsage(orderId, '订单取消');
        
        if (revokeResult.success) {
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
                [utils.createId(), log_id, `订单取消，已恢复 ${revokeResult.revokedCoupons} 张券的可用状态`, '', 1])
        }
        
        return revokeResult;
    } catch (error) {
        await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", 
            [utils.createId(), log_id, `撤销券使用失败: ${error.message}`, '', 0])
        return { success: false, message: error.message };
    }
}

module.exports = router;