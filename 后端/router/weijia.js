const express = require("express");
const router = express.Router();
const { pool } = require("../pool.js");
const utils = require("../utils/index.js");
const {commodityCetailsMy,getPayInfo} =  require("../config/request_api_config.js")


//全部删除兑换码信息i
/**
 * @swagger
 * tags:
 *   name: Weijia
 *   description: 微加相关接口
 */

/**
 * @swagger
 * /mdl/faid:
 *   post:
 *     summary: 全部删除兑换码信息
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: 兑换码父ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除成功
 */
router.post("/faid",async(req,res)=>{
    let id = req.body.id
    let sql1 = 'DELETE FROM mdl_code_tab_meal WHERE MDL_CODE_TAB_ID =?'
    let sql2 = 'DELETE FROM mdl_code_tab WHERE ID =?'
    let sql3 = 'DELETE FROM mdl_code WHERE FATHERR_ID = ?'
     await utils.dbUse(sql1, [id])
     await utils.dbUse(sql2, [id])
      await utils.dbUse(sql3, [id])
      res.json({
          code:200,
          message:"删除成功"
      })
})
//下架
/**
 * @swagger
 * /mdl/soldOut:
 *   post:
 *     summary: 下架兑换码
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: 兑换码ID
 *     responses:
 *       200:
 *         description: 下架成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 msg:
 *                   type: string
 *                   example: 下架完成
 */
router.post("/soldOut",async(req,res)=>{
    let {id} =req.body
    let sql = 'UPDATE mdl_code SET CODE_STATUS= 1 WHERE ID =?'
    await utils.dbUse(sql, [id])
    res.json({
        code:200,
        msg:"下架完成"
    })
})
//测试
/**
 * @swagger
 * /mdl/text:
 *   get:
 *     summary: 测试接口
 *     tags: [Weijia]
 *     responses:
 *       200:
 *         description: 测试成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 */
router.get("/text",async(req,res)=>{
    let result = await getPayInfo()
    res.json(result)
})
//阿奇索生成兑换码
/**
 * @swagger
 * /mdl/achsoGeneratesLinks:
 *   post:
 *     summary: 阿奇索生成兑换码
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               oderId:
 *                 type: string
 *                 description: 订单号
 *               requestId:
 *                 type: string
 *                 description: 请求GUID
 *               foodAllPrice:
 *                 type: number
 *                 format: float
 *                 description: 商品实付总价
 *               foodItemPrice:
 *                 type: number
 *                 format: float
 *                 description: 商品实付单价
 *               foodId:
 *                 type: string
 *                 description: 商品ID
 *               userPhone:
 *                 type: string
 *                 description: 联系人手机
 *               buyerId:
 *                 type: string
 *                 description: 买家ID
 *               oderPrice:
 *                 type: number
 *                 format: float
 *                 description: 订单金额
 *               shipmentsCount:
 *                 type: integer
 *                 description: 发货数量
 *               buyCount:
 *                 type: integer
 *                 description: 购买数量
 *               foodSKUName:
 *                 type: string
 *                 description: 商品Sku名称
 *               ProductSkuMerchantCode:
 *                 type: string
 *                 description: 商品Sku商家编码
 *               foodName:
 *                 type: string
 *                 description: 商品名称
 *               foodSKUID:
 *                 type: string
 *                 description: 商品SkuId
 *               timeText:
 *                 type: string
 *                 description: 时间字符串
 *               timestamp:
 *                 type: integer
 *                 description: 时间戳
 *               agency:
 *                 type: string
 *                 description: 代理
 *               keys:
 *                 type: string
 *                 description: 密钥
 *     responses:
 *       200:
 *         description: 成功
 *       302:
 *         description: 身份验证失败
 */
router.post("/achsoGeneratesLinks",async(req,res)=>{
    //oderId 订单号
    //requestId 请求GUID
    //foodAllPrice 商品实付总价
    //foodItemPrice 商品实付单价
    //foodId 商品ID
    //userPhone 联系人手机
    //buyerId 买家ID
    //oderPrice 订单金额
    //shipmentsCount 发货数量
    //buyCount 购买数量
    //foodSKUName 商品Sku名称
    //ProductSkuMerchantCode 商品Sku商家编码
    //foodName 商品名称
    //foodSKUID 商品SkuId
    //timeText 时间str
    //timestamp 时间戳
    //agency 代理
    //keys 密钥
    let {oderId,requestId,foodAllPrice,foodItemPrice,foodId,userPhone,buyerId,oderPrice,shipmentsCount,buyCount,foodSKUName,ProductSkuMerchantCode,foodName,foodSKUID,timeText,timestamp,agency,keys} = req.body
    //获取商品编码
    let FoodCode = ProductSkuMerchantCode.split(',')
    //当前使用“，”分割商品编码，生成为数组 []
    //获取配置的AppSecret值 
    let AppSecret = '存数据库'
    if(keys !== AppSecret){return res.json({code:302,message:"身份验证失败"})}
    //分别使用商品编码获取套餐信息 FOOD_CODE
    for(let i = 0 ; i < FoodCode.length; i++){
        //获取套餐信息
        let sql = "SELECT * FROM mdl_combo_meal WHERE FOOD_CODE = ?";
        let result = await dbUse(sql, [FoodCode[i]]);
    }
    
})
//查询套餐列表
/**
 * @swagger
 * /mobile/menu/list:
 *   get:
 *     summary: 查询套餐列表
 *     tags: [Weijia]
 *     responses:
 *       200:
 *         description: 成功获取套餐列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       500:
 *         description: 服务器错误
 */
router.get("/mobile/menu/list", async (req, res) => {
    let sql = "SELECT * FROM mdl_tab";
    pool.query(sql, [], (err, result) => {
        if (err) throw err;
        res.send(utils.returnData({ data: result, total: 0 }));
    })
})
//获取tab分类
/**
 * @swagger
 * /mobile/product/list/{tid}:
 *   get:
 *     summary: 获取tab分类下的商品列表
 *     tags: [Weijia]
 *     parameters:
 *       - in: path
 *         name: tid
 *         schema:
 *           type: string
 *         required: true
 *         description: TAB ID
 *     responses:
 *       200:
 *         description: 成功获取商品列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *       500:
 *         description: 服务器错误
 */
router.get("/mobile/product/list/:tid", (req, res) => {
    let sql = "SELECT * FROM mdl_combo_meal WHERE TAB = ?";
    pool.query(sql, [req.params.tid], (err, result) => {
        if (err) throw err;
        res.send(utils.returnData({ data: result, total: 0 }));
    })
})
//生成兑换码
/**
 * @swagger
 * /mobile/menu/cdkey:
 *   post:
 *     summary: 生成兑换码
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               anum:
 *                 type: integer
 *                 description: 可用数量
 *               cdkNum:
 *                 type: integer
 *                 description: 生成数量
 *               dateline:
 *                 type: string
 *                 description: 到期时间
 *               domainName:
 *                 type: string
 *                 description: 链接域名
 *               note:
 *                 type: string
 *                 description: 短信通知
 *               price:
 *                 type: number
 *                 format: float
 *                 description: 波动价格
 *               setmeals:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 套餐ID数组
 *               userId:
 *                 type: string
 *                 description: 用户ID
 *     responses:
 *       200:
 *         description: 兑换码生成成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 msg:
 *                   type: string
 *                   example: 兑换码生成成功
 *       400:
 *         description: 参数缺失
 */
router.post("/mobile/menu/cdkey", async (req, res) => {
    //可用数量 、 生成数量、到期时间、链接域名、短信通知、波动价格、套餐id、用户id
    let { anum, cdkNum, dateline, domainName, note, price, setmeals, userId } = req.body;
    if (!userId) {
        userId = '阿奇索';
    }
    if (!setmeals) {
        return res.json({ code: -1, msg: "至少选择一个套餐" })
    }
    if (!domainName) {
        return res.json({ code: -1, msg: "请输入域名" })
    }
    if (!dateline) {
        return res.json({ code: -1, msg: "请选择过期时间" })
    }
    if (!cdkNum) {
        return res.json({ code: -1, msg: "请输入生成数量" })
    }
    if (!anum) {
        return res.json({ code: -1, msg: "请输入可用数量" })
    }
    let fatherId = utils.createId();
    let createdSql = 'INSERT INTO mdl_code_tab (ID,COUNT,END_TIME,AVAILABLE_COUNT,DOMAIN_NAME,LIMIT_PRICE,USER_ID,CREATED_TIME) VALUES (?,?,?,?,?,?,?,?)';
    await utils.dbUse(createdSql, [fatherId, cdkNum, dateline, anum, domainName, price, userId, new Date()])
    let sqlStr = ''
    for (let i = 0; i < setmeals.length; i++) {
        if (i == setmeals.length - 1) {
            sqlStr = sqlStr + `('${utils.createId()}','${fatherId}','${setmeals[i]}');`
        } else {
            sqlStr = sqlStr + `('${utils.createId()}','${fatherId}','${setmeals[i]}'),`
        }
    }

    await utils.dbUse('INSERT INTO mdl_code_tab_meal (ID,MDL_CODE_TAB_ID,MDL_MEAL_ID) VALUES' + sqlStr, [])

    for (let i = 0; i < cdkNum; i++) {
        let codeVip = await utils.generateUniqueCode()
        await utils.dbUse('INSERT INTO mdl_code (ID,CODE,CREATED_TIME,USER_ID,FATHERR_ID,DOMAIN_NAME,LIMIT_PRICE,A_NUM) VALUES (?,?,?,?,?,?,?,?)', [utils.createId(), codeVip, utils.createTime(), userId, fatherId, domainName, price, anum])
    }
    res.json({ code: 1, msg: "兑换码生成成功" })


})
//获取套餐详情
/**
 * @swagger
 * /setmeal/detail:
 *   get:
 *     summary: 获取套餐详情
 *     tags: [Weijia]
 *     parameters:
 *       - in: query
 *         name: smId
 *         schema:
 *           type: string
 *         required: true
 *         description: 套餐ID
 *     responses:
 *       200:
 *         description: 成功获取套餐详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                 total:
 *                   type: integer
 *       500:
 *         description: 服务器错误
 */
router.get("/setmeal/detail", async (req, res) => {
    let { smId } = req.query;
    let sql = 'SELECT * FROM mdl_combo_meal WHERE ID = ?';
    pool.query(sql, [smId], (err, result) => {
        if (err) throw err;
        res.send(utils.returnData({ data: result, total: 0 }));
    })
})
//查询兑换码订单
/**
 * @swagger
 * /mobile/cdkmenu/list:
 *   post:
 *     summary: 查询兑换码订单 (POST)
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 description: 页码
 *               size:
 *                 type: integer
 *                 description: 每页数量
 *               userId:
 *                 type: string
 *                 description: 用户ID
 *     responses:
 *       200:
 *         description: 成功获取兑换码订单列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *       500:
 *         description: 服务器错误
 */
router.post("/mobile/cdkmenu/list", async (req, res) => {
    let { page, size, userId } = req.body;
    let pages = utils.pageSize(parseInt(page), parseInt(size))
    let sql = `SELECT * FROM mdl_code_tab WHERE USER_ID= '${userId}'  ORDER BY CREATED_TIME DESC  LIMIT ${pages.page}, ${pages.size} `
    pool.query(sql, async (err, result) => {
        if (err) throw err;
        for (let i = 0; i < result.length; i++) {
            result[i].meal = []
            let findSql = `SELECT * FROM mdl_code_tab_meal WHERE MDL_CODE_TAB_ID ='${result[i].ID}'`
            let flagSql = await utils.dbUse(findSql, [])
            for (let y = 0; y < flagSql.length; y++) {
                let findMeal = `SELECT * FROM mdl_combo_meal WHERE ID ='${flagSql[y].MDL_MEAL_ID}'`
                let meal = await utils.dbUse(findMeal, [])
                result[i].meal.push(meal[0])
            }
            let findCodeLengthd = `SELECT COUNT(1) weijia FROM mdl_code WHERE FATHERR_ID = '${result[i].ID}' AND CODE_STATUS IN (0,3) `
            let findCodeLengthy = `SELECT COUNT(1) weijia FROM mdl_code WHERE FATHERR_ID = '${result[i].ID}' AND CODE_STATUS = 2`
            let findCodeLengthg = `SELECT COUNT(1) weijia FROM mdl_code WHERE FATHERR_ID = '${result[i].ID}' AND CODE_STATUS = 1`
            result[i].arr = []
            result[i].arr[0] = await utils.dbUse(findCodeLengthd, [])
            result[i].arr[1] = await utils.dbUse(findCodeLengthy, [])
            result[i].arr[2] = await utils.dbUse(findCodeLengthg, [])
        }
        let totlalSql = `SELECT COUNT(1) weijia FROM mdl_code_tab WHERE USER_ID= '${userId}'`

        let total = await utils.dbUse(totlalSql, [])
        res.send(utils.returnData({ data: { list: result, total: total[0] } }));
    })
})
//查询兑换码订单
/**
 * @swagger
 * /mobile/cdkmenu/list:
 *   get:
 *     summary: 查询兑换码订单 (GET)
 *     tags: [Weijia]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: 用户ID
 *     responses:
 *       200:
 *         description: 成功获取兑换码订单列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *       500:
 *         description: 服务器错误
 */
router.get("/mobile/cdkmenu/list", async (req, res) => {
    let { page, size, userId } = req.query;
    let pages = utils.pageSize(parseInt(page), parseInt(size))
    let sql = `SELECT * FROM mdl_code_tab WHERE USER_ID= '${userId}'  ORDER BY CREATED_TIME DESC  LIMIT ${pages.page}, ${pages.size} `
    pool.query(sql, async (err, result) => {
        if (err) throw err;
        for (let i = 0; i < result.length; i++) {
            result[i].meal = []
            let findSql = `SELECT * FROM mdl_code_tab_meal WHERE MDL_CODE_TAB_ID ='${result[i].ID}'`
            let flagSql = await utils.dbUse(findSql, [])
            for (let y = 0; y < flagSql.length; y++) {
                let findMeal = `SELECT * FROM mdl_combo_meal WHERE ID ='${flagSql[y].MDL_MEAL_ID}'`
                let meal = await utils.dbUse(findMeal, [])
                result[i].meal.push(meal[0])
            }
            let findCodeLengthd = `SELECT COUNT(1) weijia FROM mdl_code WHERE FATHERR_ID = '${result[i].ID}' AND CODE_STATUS IN (0,3) `
            let findCodeLengthy = `SELECT COUNT(1) weijia FROM mdl_code WHERE FATHERR_ID = '${result[i].ID}' AND CODE_STATUS = 2`
            let findCodeLengthg = `SELECT COUNT(1) weijia FROM mdl_code WHERE FATHERR_ID = '${result[i].ID}' AND CODE_STATUS = 1`
            result[i].arr = []
            result[i].arr[0] = await utils.dbUse(findCodeLengthd, [])
            result[i].arr[2] = await utils.dbUse(findCodeLengthy, [])
            result[i].arr[1] = await utils.dbUse(findCodeLengthg, [])
        }
        let totlalSql = `SELECT COUNT(1) weijia FROM mdl_code_tab WHERE USER_ID= '${userId}'`

        let total = await utils.dbUse(totlalSql, [])
        res.send(utils.returnData({ data: { list: result, total: total[0] } }));
    })
})
//通过父id查询兑换码
/**
 * @swagger
 * /mobile/cdk/list/{fatherId}:
 *   get:
 *     summary: 通过父id查询兑换码 (GET)
 *     tags: [Weijia]
 *     parameters:
 *       - in: path
 *         name: fatherId
 *         schema:
 *           type: string
 *         required: true
 *         description: 父ID
 *     responses:
 *       200:
 *         description: 成功获取兑换码列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *       500:
 *         description: 服务器错误
 */
router.get("/mobile/cdk/list/:fatherId", async (req, res) => {
    let { fatherId } = req.params;
    let sql = `SELECT * FROM mdl_code WHERE FATHERR_ID = '${fatherId}'`
    pool.query(sql, async (err, result) => {
        if (err) throw err;
        let totlalSql = `SELECT COUNT(1) FROM mdl_code WHERE FATHERR_ID= '${fatherId}'`
        let total = await utils.dbUse(totlalSql, [])
        res.send(utils.returnData({ data: { list: result, total: total } }));
    })
})
/**
 * @swagger
 * /mobile/cdk/list:
 *   post:
 *     summary: 通过父id查询兑换码 (POST)
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fatherId:
 *                 type: string
 *                 description: 父ID
 *     responses:
 *       200:
 *         description: 成功获取兑换码列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *       500:
 *         description: 服务器错误
 */
router.post("/mobile/cdk/list", async (req, res) => {
    let { fatherId } = req.body;
    let sql = `SELECT * FROM mdl_code WHERE FATHERR_ID = '${fatherId}'`
    pool.query(sql, async (err, result) => {
        if (err) throw err;
        let totlalSql = `SELECT COUNT(1) FROM mdl_code WHERE FATHERR_ID= '${fatherId}'`
        let total = await utils.dbUse(totlalSql, [])
        res.send(utils.returnData({ data: { list: result, total: total } }));
    })
})
//根据订单查询订单
/**
 * @swagger
 * /getOrderByOrderId:
 *   post:
 *     summary: 根据订单ID查询订单
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               orderId:
 *                 type: string
 *                 description: 订单ID
 *     responses:
 *       200:
 *         description: 成功获取订单信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/getOrderByOrderId",async (req,res)=>{
    let {orderId} = req.body;
    let sql = `SELECT * FROM mdl_order WHERE ODER_ID=?`;
    let oderList = await utils.dbUse(sql,[orderId]);
    res.json({
        code:1,
        data:oderList
    })
})
//获取订单列表
/**
 * @swagger
 * /getOrderList:
 *   post:
 *     summary: 获取订单列表
 *     tags: [Weijia]
 *     responses:
 *       200:
 *         description: 成功获取订单列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/getOrderList",async (req,res)=>{
    let sql = `SELECT * FROM mdl_order `
    let oderList = await utils.dbUse(sql,[]);
    res.json({
        code:1,
        data:oderList
    })
})
//查询兑换码详情
/**
 * @swagger
 * /mobile/cdk/{code}:
 *   get:
 *     summary: 查询兑换码详情 (GET)
 *     tags: [Weijia]
 *     parameters:
 *       - in: path
 *         name: code
 *         schema:
 *           type: string
 *         required: true
 *         description: 兑换码
 *     responses:
 *       200:
 *         description: 成功获取兑换码详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 兑换码不存在或服务器错误
 */
router.get("/mobile/cdk/:code", async (req, res) => {
    let { code } = req.params;
    let sql = `SELECT * FROM mdl_code WHERE CODE = '${code}'`, obj = req.body;
    pool.query(sql, async (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        if(result.length==0){return res.json({code:-1,message:"兑换码不存在"})}
        let fasql = `SELECT * FROM mdl_code_tab WHERE ID= '${result[0].FATHERR_ID}'`
        let father = await utils.dbUse(fasql, [])
        let sqlOder = `SELECT * FROM mdl_order WHERE CODE = ?`
        let oder = await utils.dbUse(sqlOder, [code])
        result[0].oder = oder
        result[0].endTime = father[0].END_TIME
        res.send(utils.returnData({ data: result }));
    })
})
//查询兑换码详情
/**
 * @swagger
 * /mobile/cdk:
 *   post:
 *     summary: 查询兑换码详情 (POST)
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 兑换码
 *     responses:
 *       200:
 *         description: 成功获取兑换码详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 兑换码不存在或服务器错误
 */
router.post("/mobile/cdk", async (req, res) => {
    let { code } = req.body;
    let sql = `SELECT * FROM mdl_code WHERE CODE = '${code}'`;
    pool.query(sql, async (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        if(result.length==0){return res.json({code:-1,message:"兑换码不存在"})}
        let fasql = `SELECT * FROM mdl_code_tab WHERE ID= '${result[0].FATHERR_ID}'`
        let father = await utils.dbUse(fasql, [])
        let sqlOder = `SELECT * FROM mdl_order WHERE CODE = ?`
        let oder = await utils.dbUse(sqlOder, [code])
        result[0].oder = oder
        result[0].endTime = father[0].END_TIME
        res.send(utils.returnData({ data: result }));
    })
})
//二级配置接口
/**
 * @swagger
 * /setMelFood:
 *   post:
 *     summary: 二级配置接口
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               option:
 *                 type: object
 *                 description: 配置选项
 *               foodId:
 *                 type: string
 *                 description: 商品ID
 *     responses:
 *       200:
 *         description: 配置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/setMelFood",(req,res)=>{
    let {option,foodId} = req.body
    let sql = `UPDATE mdl_product SET SECONDARY_MENU = ? WHERE ID = ?`
    pool.query(sql,[JSON.stringify(option),foodId],(err,result)=>{
        if(err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})
//获取二级配置接口
/**
 * @swagger
 * /getSetCustomization:
 *   post:
 *     summary: 获取二级配置接口
 *     tags: [Weijia]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               storeCode:
 *                 type: string
 *                 description: 门店代码
 *               foodid:
 *                 type: string
 *                 description: 商品ID
 *     responses:
 *       200:
 *         description: 成功获取二级配置信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       500:
 *         description: 参数缺失、暂无数据或服务器错误
 */
router.post("/getSetCustomization", async (req, res) => {
    let { storeCode, foodid } = req.body
    if (!storeCode || !foodid) { return res.json({ code: 500, message: "参数缺失" }) }
    let sql = `SELECT * FROM mdl_product WHERE ID = ?`
    let result = await utils.dbUse(sql, [foodid])
    if(result.length == 0){ return res.json({ code: -1, message: "暂无数据" })}
    if(result[0].SECONDARY_MENU){
        res.json({
            code:200,
            data:JSON.parse(result[0].SECONDARY_MENU)
        })
    }else{
        let productCode = result[0].PRODUCT_CODE
        let result1 = await commodityCetailsMy(storeCode, productCode)
        if (result1.data.code != 200) { return res.json({ code: 500, message: result1.data.message }) }
        if(!result1.data.data){
            res.json({
                code:500,
                message:`当前门店${storeCode}未找到该${productCode}商品,换家门店code试试`
            })
            return
        }
        let food = result1.data.data.product;//Cannot read property 'product' of undefined
        if(food.type ==7 || food.type == 2){
            if(food.comboItems){
                for(let i = 0;i<food.comboItems.length;i++){
                    for(let j = 0;j<food.comboItems[i].comboProducts.length;j++){
                        food.comboItems[i].comboProducts[j].isChecked = true
                    }
                }
            }
        }
        res.json({
            code:200,
            data:food
        })
    }
})
module.exports = router;