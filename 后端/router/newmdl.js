const express = require("express");
const router = express.Router();
const { pool } = require("../pool.js");
const utils = require("../utils/index.js");
const { getOrderDetail } = require("../config/request_api_config.js");

/**
 * 将麦当劳API的订单状态映射为前端需要的状态码
 * 前端状态码：
 * 1: 已支付
 * 2: 已出餐
 * 3: 已取餐
 * 4: 已退款
 **/
 function mapOrderStatus(mcdStatus) {
    // 处理数字状态码
    if (mcdStatus === null || mcdStatus === undefined) {
        return 1; // 默认为已支付状态
    }
    
    // 处理数字状态码 - 支持数字和字符串形式的数字
    if (typeof mcdStatus === 'number' || (typeof mcdStatus === 'string' && !isNaN(mcdStatus))) {
        const statusCode = parseInt(mcdStatus);
        switch(statusCode) {
            case 1:  // 已支付
            case 2:  // 支付确认
                return 1;
            case 3:  // 准备中
            case 4:  // 准备完成
            case 5:  // 待取餐
                return 2;
            case 6:  // 已完成
                return 3;
            case 7:  // 已取消
            case 8:  // 已退款
                return 4;
            default:
                return 1; // 默认为已支付状态
        }
    }
    
    // 处理文本状态（兼容旧版本）
    switch(mcdStatus) {
        case 'PAID':           // 已支付
        case 'PAYMENT_CONFIRMED':
            return 1;
        case 'PREPARING':      // 准备中/已出餐
        case 'READY_FOR_PICKUP':
            return 2;
        case 'COMPLETED':      // 已完成/已取餐
        case 'PICKED_UP':
            return 3;
        case 'CANCELLED':      // 已取消/已退款
        case 'REFUNDED':
            return 4;
        default:
            return 1; // 默认为已支付状态
    }
}

//获取订单详情
/**
 * @swagger
 * tags:
 *   name: Newmdl
 *   description: 新麦当劳相关接口
 */

/**
 * @swagger
 * /newmdl/realTimeOder:
 *   post:
 *     summary: 获取订单详情
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               oderId:
 *                 type: string
 *                 description: 订单ID
 *               sid:
 *                 type: string
 *                 description: SID
 *     responses:
 *       200:
 *         description: 成功获取订单详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *       400:
 *         description: 参数缺失
 */
router.post("/realTimeOder",async(req,res)=>{
    let {oderId,sid} = req.body
    if(!oderId || !sid){
        res.json({
            code:200,
            message:"参数缺失"
        })
        return
    }
    let result = await getOrderDetail(oderId,sid)
    res.json(result.data)
    
})
//获取订单详情
/**
 * @swagger
 * /newmdl/getOrderDetail:
 *   get:
 *     summary: 获取订单详情
 *     tags: [Newmdl]
 *     parameters:
 *       - in: query
 *         name: oderId
 *         schema:
 *           type: string
 *         required: true
 *         description: 订单ID
 *       - in: query
 *         name: sid
 *         schema:
 *           type: string
 *         required: true
 *         description: SID
 *     responses:
 *       200:
 *         description: 成功获取订单详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                 msg:
 *                   type: string
 *                   example: 获取订单详情成功
 *       400:
 *         description: 订单ID或SID参数缺失
 *       500:
 *         description: 获取订单详情出错
 */
router.get("/getOrderDetail", async (req, res) => {
    const { oderId, sid } = req.query;
    
    if (!oderId || !sid) {
        return res.send(utils.returnData({
            code: -1,
            msg: "订单ID或SID参数缺失"
        }));
    }
    
    try {
        // 调用API获取订单详情
        const result = await getOrderDetail(oderId, sid);
        
        if (result && result.data) {
            res.send(utils.returnData({
                code: 200,
                data: result.data,
                msg: "获取订单详情成功"
            }));
        } else {
            res.send(utils.returnData({
                code: -1,
                msg: "获取订单详情失败"
            }));
        }
    } catch (error) {
        console.error('获取订单详情出错:', error);
        res.send(utils.returnData({
            code: -1,
            msg: error.message || "获取订单详情出错"
        }));
    }
});

//删除日志
/**
 * @swagger
 * /newmdl/delJournal:
 *   post:
 *     summary: 删除日志
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 日志ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delJournal", async (req, res) => {
    let sql = "DELETE FROM mdl_log WHERE ID=?", obj = req.body;
    pool.query(sql, [obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    });
});
//批量删除日志
/**
 * @swagger
 * /newmdl/delJournalList:
 *   post:
 *     summary: 批量删除日志
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: string
 *               description: 日志ID数组
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delJournalList", async (req, res) => {
    let obj = req.body
    let str = ""
    for (let i = 0; i < obj.length; i++) {
        if (i === obj.length - 1) {
            str += "'" + obj[i] + "'"
        } else {
            str += "'" + obj[i] + "'" + ','
        }
    }

    let sql = `DELETE FROM mdl_log WHERE ID IN (${str})`;
    pool.query(sql, (err, result) => {
        console.log(err)
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//删除商品列表
/**
 * @swagger
 * /newmdl/delGoods:
 *   post:
 *     summary: 删除商品列表
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 商品ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delGoods", async (req, res) => {
    let sql = "DELETE FROM mdl_product WHERE ID=?", obj = req.body;
    console.log(obj.ID);
    pool.query(sql, [obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//批量删除商品列表
/**
 * @swagger
 * /newmdl/delGoodsList:
 *   post:
 *     summary: 批量删除商品列表
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: string
 *               description: 商品ID数组
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delGoodsList", async (req, res) => {
    let obj = req.body
    let str = ""
    for (let i = 0; i < obj.length; i++) {
        if (i === obj.length - 1) {
            str += "'" + obj[i] + "'"
        } else {
            str += "'" + obj[i] + "'" + ','
        }
    }
    let sql = `DELETE FROM mdl_product WHERE ID IN (${str})`;
    pool.query(sql, (err, result) => {
        console.log(err)
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//添加多code
/**
 * @swagger
 * /newmdl/addManyCode:
 *   post:
 *     summary: 添加多code
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 商品ID
 *               MANY_CODE:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 多个code的数组
 *     responses:
 *       200:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/addManyCode", async (req, res) => {
    let obj = req.body
    let str = ""
    for (let i = 0; i < obj.MANY_CODE.length; i++) {
        if (i === obj.MANY_CODE.length - 1) {
            str += obj.MANY_CODE[i]
        } else {
            str += obj.MANY_CODE[i] + ','
        }
    }
    let sql = "UPDATE mdl_product AS m SET MANY_CODE = ? WHERE ID = ?"
    pool.query(sql, [str, obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//删除方案配置
/**
 * @swagger
 * /newmdl/delPlanConfig:
 *   post:
 *     summary: 删除方案配置
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 方案配置ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delPlanConfig", async (req, res) => {
    let sql = "DELETE FROM mdl_combo_meal WHERE ID=?", obj = req.body;
    pool.query(sql, [obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//删除券码管理
/**
 * @swagger
 * /newmdl/delCoupon:
 *   post:
 *     summary: 删除券码管理
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 优惠券ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delCoupon", async (req, res) => {
    let sql = "DELETE FROM mdl_coupon WHERE ID=?", obj = req.body;
    pool.query(sql, [obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//删除账号
/**
 * @swagger
 * /newmdl/delAccount:
 *   post:
 *     summary: 删除账号
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 账号ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delAccount", async (req, res) => {
    let sql = "DELETE FROM classify WHERE ID=?", obj = req.body;
    pool.query(sql, [obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//删除普通账号
/**
 * @swagger
 * /newmdl/delNormalAccount:
 *   post:
 *     summary: 删除普通账号
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ID:
 *                 type: string
 *                 description: 普通账号ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *       500:
 *         description: 服务器错误
 */
router.post("/delNormalAccount", async (req, res) => {
    let sql = "DELETE FROM classify WHERE ID=?", obj = req.body;
    pool.query(sql, [obj.ID], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//获取订单页面
/**
 * @swagger
 * /newmdl/getOrderPage:
 *   post:
 *     summary: 获取订单页面
 *     tags: [Newmdl]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *               size:
 *                 type: integer
 *               name:
 *                 type: string
 *               date:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: 查询成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 1
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *       500:
 *         description: 服务器错误
 */
router.post("/getOrderPage", async (req, res) => {
    let obj = req.body
    let user = await utils.getUserRole(req, res);
    console.log(user.user.id)
    let { page, size } = utils.pageSize(obj.page, obj.size);
    if(obj.date){
//         let sql = `SELECT o.*, c.*
// FROM mdl_order o
// LEFT JOIN mdl_code c ON o.CODE = c.CODE
// WHERE o.CREATRD_TIME BETWEEN ? AND ? 
//   AND o.CODE LIKE "%${obj.name || ''}%"
// ORDER BY o.CREATRD_TIME DESC 
// LIMIT ?,?`
    let sql = `SELECT o.*, c.*
FROM mdl_order o
LEFT JOIN mdl_code c ON o.CODE = c.CODE
WHERE  o.CREATRD_TIME BETWEEN ? AND ? 
  AND (
    o.CODE LIKE "%${obj.name || ''}%" 
    OR o.MEAL_TITLE LIKE "%${obj.name || ''}%" 
    OR o.OPEN_ID LIKE "%${obj.name || ''}%"
    OR o.ODER_ID LIKE "%${obj.name || ''}%"
    OR o.PAY_ID LIKE "%${obj.name || ''}%"
    OR o.PROXY_ID LIKE "%${obj.name || ''}%"
    OR o.STORE_NAME LIKE "%${obj.name || ''}%"
    OR o.MEAL_ID LIKE "%${obj.name || ''}%"
    OR o.PICKUP_CODE LIKE "%${obj.name || ''}%"
    OR o.COUPON_ID LIKE "%${obj.name || ''}%"
    OR o.COUPON_CODE LIKE "%${obj.name || ''}%"
    OR c.tbOderId LIKE "%${obj.name || ''}%"
  ) AND o.PROXY_ID = ${user.user.id}
ORDER BY o.CREATRD_TIME DESC 
LIMIT ?,?`
        //  let sql = `SELECT * FROM mdl_order WHERE CREATRD_TIME BETWEEN ? AND ? AND CODE LIKE "%${obj.name || ''}%" ORDER BY CREATRD_TIME DESC LIMIT ?,?`
          pool.query(sql,[obj.date[0],obj.date[1],page,size], async (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        let totlalSql = `SELECT COUNT(1) as total FROM mdl_order WHERE PROXY_ID = ${user.user.id}  AND CREATRD_TIME BETWEEN ${obj.date[0]} AND ${obj.date[1]} AND CODE LIKE  "%${obj.name || ''}%"`
        let total = await utils.dbUse(totlalSql, [])
        res.send(utils.returnData({ data: { list: result, total: total } }));
    })
         
    }else{
        let sql = `SELECT o.*, c.*
FROM mdl_order o
LEFT JOIN mdl_code c ON o.CODE = c.CODE
WHERE  o.PROXY_ID = ${user.user.id} 
AND (
    o.CODE LIKE "%${obj.name || ''}%" 
    OR o.MEAL_TITLE LIKE "%${obj.name || ''}%" 
    OR o.OPEN_ID LIKE "%${obj.name || ''}%"
    OR o.ODER_ID LIKE "%${obj.name || ''}%"
    OR o.PAY_ID LIKE "%${obj.name || ''}%"
    OR o.PROXY_ID LIKE "%${obj.name || ''}%"
    OR o.STORE_NAME LIKE "%${obj.name || ''}%"
    OR o.MEAL_ID LIKE "%${obj.name || ''}%"
    OR o.PICKUP_CODE LIKE "%${obj.name || ''}%"
    OR o.COUPON_ID LIKE "%${obj.name || ''}%"
    OR o.COUPON_CODE LIKE "%${obj.name || ''}%"
    OR c.tbOderId LIKE "%${obj.name || ''}%"
  )
ORDER BY o.CREATRD_TIME DESC 
LIMIT ?,?`
//          let sql = `SELECT o.*, c.*
// FROM mdl_order o
// LEFT JOIN mdl_code c ON o.CODE = c.CODE
// WHERE o.CODE LIKE "%${obj.name || ''}%"
// ORDER BY o.CREATRD_TIME DESC 
// LIMIT ?,?`
        //  let sql = `SELECT * FROM mdl_order WHERE CODE LIKE "%${obj.name || ''}%" ORDER BY CREATRD_TIME DESC LIMIT ?,?`
    // let sql = `SELECT * FROM mdl_order ORDER BY CREATRD_TIMLIMITE ASC `, obj = req.body;LIMIT ?,? [obj.page, obj.size]

    pool.query(sql,[page,size], async (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        let totlalSql = `SELECT COUNT(1) as total FROM mdl_order WHERE PROXY_ID = ${user.user.id}  AND CODE LIKE  "%${obj.name || ''}%"`
        let total = await utils.dbUse(totlalSql, [])
        res.send(utils.returnData({ data: { list: result, total: total } }));
    })
    }
   
})

//删除订单
router.post("/delOrder", async (req, res) => {
    let sql = "DELETE FROM mdl_order WHERE ID=?", obj = req.body;
    console.log(obj);
    pool.query(sql, [obj.id], (err, result) => {
        if (err) return res.send(utils.returnData({ code: -1, err, req }));
        res.send(utils.returnData({ data: result }));
    })
})

//获取麦当劳账号订单列表（从Python后端API获取）
router.post("/getMcdOrder", async (req, res) => {
    try {
        let obj = req.body;
        
        // 权限验证 - getUserRole函数现在会在验证失败时自动发送响应并抛出异常
        let user = await utils.getUserRole(req, res);
        
        // 直接使用前端传入的账号作为SID
        let sid = obj.proxyId;
        if (!sid) {
            return res.send(utils.returnData({ code: -1, msg: '账号SID为空' }));
        }
        
        // 调用Python后端API获取订单
        const requestApi = require('../config/request_api_config');
        
        // 调用优化后的getMcdOrders函数，该函数已包含数据转换逻辑
        const result = await requestApi.getMcdOrders(
            sid,
            obj.orderCategoryId || 0,
            obj.page || 1,
            obj.size || 10
        );
        
        // 返回处理后的数据
        if (result && result.success) {
            res.send(utils.returnData({ 
                code: 200,
                data: {
                    list: result.data.list,
                    total: result.data.pages || 0
                }
            }));
        } else {
            res.send(utils.returnData({ 
                code: -1, 
                msg: result.message || '获取订单失败' 
            }));
        }
        
    } catch (error) {
        console.error('获取麦当劳订单失败:', error);
        
        // 根据错误类型返回不同的错误码
        let errorCode = -1;
        let errorMsg = '获取订单失败';
        
        if (error.response) {
            // HTTP响应错误
            errorCode = error.response.status === 401 ? 401 : -1;
            errorMsg = error.response.data?.message || '服务器响应错误';
        } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            // 网络连接错误
            errorMsg = '无法连接到订单服务器';
        } else {
            // 其他错误
            errorMsg = error.message || '网络错误';
        }
        
        res.send(utils.returnData({ 
            code: errorCode, 
            msg: errorMsg
        }));
    }
})





module.exports = router;