/**
 * API适配中间件
 * 将Vue前端的API调用适配到现有的后端路由
 */

const { ResponseFormatter } = require('../utils/common');

/**
 * 数据格式适配器
 */
class DataAdapter {
  /**
   * 适配分页参数
   */
  static adaptPaginationParams(req) {
    const { page = 1, limit = 20, pageNum, pageSize, ...otherParams } = req.query;
    
    // 统一分页参数格式
    req.query = {
      page: pageNum || page,
      limit: pageSize || limit,
      ...otherParams
    };
    
    return req;
  }

  /**
   * 适配分页响应
   */
  static adaptPaginationResponse(data, total = 0, page = 1, limit = 20) {
    return ResponseFormatter.success({
      list: data || [],
      total: total,
      pageNum: page,
      pageSize: limit,
      pages: Math.ceil(total / limit)
    });
  }

  /**
   * 适配标准响应格式
   */
  static adaptResponse(data, message = 'success') {
    return ResponseFormatter.success(data, message);
  }

  /**
   * 适配错误响应
   */
  static adaptErrorResponse(error, code = 500) {
    return ResponseFormatter.error(error.message || error, code);
  }
}

/**
 * API路由映射中间件
 */
const apiAdapter = (req, res, next) => {
  const originalUrl = req.originalUrl;
  const method = req.method.toLowerCase();

  // 记录API调用
  console.log(`[API Adapter] ${method.toUpperCase()} ${originalUrl}`);

  // 适配分页参数
  if (method === 'get') {
    DataAdapter.adaptPaginationParams(req);
  }

  // 重写响应方法以统一格式
  const originalJson = res.json;
  res.json = function(data) {
    // 如果数据已经是标准格式，直接返回
    if (data && typeof data === 'object' && ('code' in data || 'success' in data)) {
      return originalJson.call(this, data);
    }

    // 根据不同的路由适配响应格式
    let adaptedData;
    
    if (originalUrl.includes('/api/sids') && method === 'get') {
      // SID列表响应适配
      adaptedData = DataAdapter.adaptPaginationResponse(
        data.data || data,
        data.total || 0,
        parseInt(req.query.page) || 1,
        parseInt(req.query.limit) || 20
      );
    } else if (originalUrl.includes('/api/orders') && method === 'get') {
      // 订单列表响应适配
      adaptedData = DataAdapter.adaptPaginationResponse(
        data.data || data,
        data.total || 0,
        parseInt(req.query.page) || 1,
        parseInt(req.query.limit) || 20
      );
    } else if (originalUrl.includes('/mobile/product/list')) {
      // 商品列表响应适配
      adaptedData = DataAdapter.adaptPaginationResponse(
        data,
        data.length || 0,
        parseInt(req.query.page) || 1,
        parseInt(req.query.limit) || 20
      );
    } else if (originalUrl.includes('/api/auth/login')) {
      // 登录响应适配
      adaptedData = {
        code: 200,
        data: {
          accessToken: data.token || data.accessToken,
          refreshToken: data.refreshToken,
          tokenType: 'Bearer',
          expiresIn: data.expires || 7200,
          user: data.user || data.userInfo
        },
        msg: data.msg || '登录成功'
      };
    } else {
      // 默认响应适配
      adaptedData = DataAdapter.adaptResponse(data);
    }

    return originalJson.call(this, adaptedData);
  };

  next();
};

/**
 * 错误处理适配中间件
 */
const errorAdapter = (err, req, res, next) => {
  console.error(`[API Error] ${req.method} ${req.originalUrl}:`, err);
  
  const adaptedError = DataAdapter.adaptErrorResponse(err, err.status || 500);
  res.status(err.status || 500).json(adaptedError);
};

/**
 * 路由重写中间件
 * 将Vue前端的API路径重写为后端实际路径
 */
const routeRewriter = (req, res, next) => {
  const originalUrl = req.originalUrl;
  
  // API路径映射表
  const routeMapping = {
    // 认证相关
    '/api/auth/login': '/api/login',
    '/api/auth/logout': '/api/logout',
    '/api/auth/user': '/api/user/info',
    
    // 麦当劳业务相关
    '/api/sids': '/api/sids',
    '/api/combo-meals': '/api/combo-meals',
    '/api/cash-coupons': '/api/cash-coupons',
    '/api/orders': '/api/orders',
    '/api/products': '/api/products',
    '/api/account-categories': '/api/account-categories',
    '/api/class-tabs': '/api/class-tabs',
    '/api/points/update': '/api/points/update',
    '/api/logs': '/api/logs',

    // 麦当劳API代理相关 - 直接透传，不重写
    '/api/store/cities': '/api/store/cities',
    '/api/cities/group': '/api/cities/group',
    '/api/store/stores': '/api/store/stores',
    '/api/test': '/api/test',

    // 链接管理相关
    '/api/link-manage/generate': '/api/link-manage/generate',
    '/api/link-manage/codes': '/api/link-manage/codes',

    // 兑换码查询相关
    '/api/code-search': '/api/code-search',
    '/api/code-search/sold-out': '/api/code-search/sold-out',
    '/api/code-search/batch-sold-out': '/api/code-search/batch-sold-out',

    // 系统配置相关
    '/api/system-config/data': '/api/system-config/data',
    '/api/system-config/tasks': '/api/system-config/tasks',
    '/api/system-config/tasks/execute': '/api/system-config/tasks/execute',
    '/api/system-config/tasks/toggle': '/api/system-config/tasks/toggle',
    '/api/system-config/tasks/logs': '/api/system-config/tasks/logs',
    '/api/system-config/system-info': '/api/system-config/system-info'
  };

  // 检查是否需要重写路由
  for (const [frontendPath, backendPath] of Object.entries(routeMapping)) {
    if (originalUrl.startsWith(frontendPath)) {
      req.url = originalUrl.replace(frontendPath, backendPath);
      req.originalUrl = req.url;
      console.log(`[Route Rewrite] ${originalUrl} -> ${req.url}`);
      break;
    }
  }

  next();
};

/**
 * 请求体适配中间件
 */
const requestBodyAdapter = (req, res, next) => {
  // 适配登录请求
  if (req.originalUrl.includes('/api/auth/login') && req.method === 'POST') {
    // 如果是FormData格式，转换为JSON格式
    if (req.body && typeof req.body === 'object') {
      const { username, password, captchaKey, captchaCode } = req.body;
      req.body = {
        username,
        password,
        captchaKey,
        captchaCode
      };
    }
  }

  // 适配分页请求
  if (req.method === 'POST' && req.body && typeof req.body === 'object') {
    const { pageNum, pageSize, ...otherData } = req.body;
    if (pageNum || pageSize) {
      req.body = {
        page: pageNum || 1,
        limit: pageSize || 20,
        ...otherData
      };
    }
  }

  next();
};

module.exports = {
  apiAdapter,
  errorAdapter,
  routeRewriter,
  requestBodyAdapter,
  DataAdapter
};
