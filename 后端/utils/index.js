const jwt = require("jsonwebtoken");
const { pool } = require("../pool.js");
const { errLog } = require("../utils/err");//日志记录
const axios = require('axios')
const uuid = require('node-uuid')
const { findCash} = require('../config/request_api_config.js');
const crypto = require('crypto');
const config_option = require("../config/config_config.js");
const config = require("../config/env_config.js");

module.exports = {


    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },
    /**
     * 生成兑换码
     * */
   async generateUniqueCode(){
        let length = await config_option.CODE_LENGTH();
        let characters = await config_option.CHARACTERS();
        let code,end =true;
        let findSql = `select * from mdl_code where CODE = ?`
        do {
            // 使用crypto.randomBytes生成随机字节，然后将其转换为字符
            code = crypto.randomBytes(Math.ceil(length / 2))
                .toString('hex')
                .slice(0, length)
                .replace(/[\d]/g, match => characters[parseInt(match, 16) % characters.length])
                .toUpperCase(); // 如果需要所有字符都是大写

            // 检查生成的兑换码是否已经存在
            let isData = await dbUses(findSql,[code])
            if(isData.length == 0){
                end = false
            }
        } while (end);
        return code;
    },

    /**
     * 查询现金券存入数据库
     * @param {*} length 
     * @returns 
     */
    async getCashCouponList(code,log_id) {
       let data = await findCash(code)
        if(data.code == 200){
            let title_class = data.data.couponDetail.couponTitle
            let cash_couponList = parseInt(title_class.replace(/[^\d]/g, ''))
            let findSqlClass = `select * from mdl_cash_coupon_class where PRICE = ?`
            let classIs = await dbUses(findSqlClass,[cash_couponList])
            let createdClassId = uuid.v1()
            if(classIs.length == 0){
                let createdClasssql = `insert into mdl_cash_coupon_class(ID,TITLE,PRICE) values (?,?,?)`
                await dbUses(createdClasssql,[createdClassId,cash_couponList+'元现金券',cash_couponList])
            }else{
                createdClassId = classIs[0].ID
            }
            //添加数据库
            let sql = `insert into mdl_cash_coupon(AVAILABLE_QUANTITY,COUPON_CODE,COUPON_CODE_END_TIME,COUPON_CODE_START_TIME,COUPON_TITLE,TRADE_CHANNEL,ID,CREATED_CLASS_ID) values (?,?,?,?,?,?,?,?)`
            pool.query(sql,[data.data.couponDetail.availableQuantity,data.data.couponDetail.couponCode,data.data.couponDetail.couponCodeEndTime,data.data.couponDetail.couponCodeStartTime,data.data.couponDetail.couponTitle,data.data.couponDetail.tradeChannel,uuid.v1(),createdClassId],(err,result)=>{
                if(err) return console.log(err)
            })
        }else{
                await dbUses("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)",[uuid.v1(),log_id,`${code}抵用券返回异常:${data.message}`,code,0])
                return  `${code}抵用券返回异常:${data.message}`
        }
       
    },
    /**
     * 取出上传格式的sid
     * 返回为数组
     * */
    getSidReturnArr(str) {
        if (!str) {
            return [];
        }
        let arr
        try {
            arr = str.split("_");
            arr.pop();
            let newArr = []
            for (let i = 0; i < arr.length; i++) {
                newArr.push(arr[i].substr(-32) + '_')
            }
            return newArr;
        } catch (err) {
            return [];
        }

    },
    async setToken({ uid }) {
        let token = jwt.sign({ uid }, config.jwt.secret, {
            expiresIn: config.jwt.expiresIn,
            algorithm: config.jwt.algorithm // 授权时间
        });
        //修改sql
        let sql = "UPDATE user SET nowLogin=? WHERE id=?";
        await dbUses(sql,[1,uid])

        return token;
    },
    async verToken(token) {
        try {
            let res = jwt.verify(token, config.jwt.secret, {
                algorithms: [config.jwt.algorithm]
            });
            let uid = res.uid;
            //查询sql
            let sql = "SELECT nowLogin FROM user WHERE id=?";
            let user =  await dbUses(sql,[uid])
            if (!user[0].nowLogin) {
                return false;
            }
            return res;
        } catch (err) {
            return false;
        }
    },
    /**
     * 判断名称是否重复
     * @param string sql sql语句
     * @param string name sql查询参数name
     * @param string msg 提示语
     * @param object req 请求主体
     * @param object res 响应主体
     * */
    existName({ sql, name, msg = "名称已存在！", req, res }) {
        return new Promise((resolve, reject) => {
            if (!name) return resolve(true);
            pool.query(sql, [name], (err, result) => {
                if (err) return res.send(this.returnData({ code: -1, msg, err, req }))
                if (result.length > 0) return res.send(this.returnData({ code: -1, msg, err, req }))
                resolve(true);
            })
        })
    },
    /**
     * 判断修改的名称是否和修改前的一样
     * @param string sql sql语句
     * @param string sqlName 修改前的属性名
     * @param string name 修改后的值
     * @param number id sql条件参数
     * */
    judgeUserName({ sql, sqlName = "name", name, id }) {
        return new Promise((resolve, reject) => {
            // let sql = "SELECT name FROM user WHERE  id=?";
            pool.query(sql, [id], (err, result) => {
                if (err) return resolve(1);
                if (result[0][sqlName] == name) return resolve(-1);
                return resolve(1);
            })
        })
    },
    /**
     * 响应总函数
     * @param number code 状态码
     * @param string msg 提示文字
     * @param number total 查询总数量
     * @param object data 数据
     * @param object err 错误信息
     * @param object req 错误信息
     * @param object funName 错误信息记录名称
     * */
    returnData({ code = 1, msg, total = 0, data = {}, err, req = {}, funName } = {}) {
        if (code == 1 && !msg) msg = "请求成功！";
        if (code == -1 && !msg) msg = "服务器异常！";
        if (code == 203 && !msg) msg = "登陆失效，请重新登陆！";
        let res = { code, msg, data };
        if (total !== 0) res.total = total;
        if (err) res.err = err;
        //记录错误日志
        if (code != 1) errLog({ err, code, msg, req, funName });
        return res;
    },
    /**
     * 获取用户信息
     * @param object req 请求主体
     * @param object res 响应主体
     * */
    getUserInfo(req, res) {
        return new Promise(async resolve => {
            let token = req.headers.token;
            if (!token) return res.send(this.returnData({ code: 203, req }));
            let user = await this.verToken(token);
            if (!user) return res.send(this.returnData({ code: 203, req }));
            let sql = "SELECT id,name,status,roles_id AS rolesId,admin,more_id AS moreId FROM user WHERE id=?";
            pool.query(sql, [user.uid], (err, result) => {
                if (err) return res.send(this.returnData({ code: -1, msg: "服务端用户信息获取错误！", err, req }));
                if (result.length === 0) return res.send(this.returnData({ code: -1, msg: "用户不存在！", err, req }));
                resolve(result[0]);
            })
        })
    },
    /**
     * 分页页码处理
     * @param number page 页码
     * @param number size 最大数量
     * */
    pageSize(page, size) {
        if (!page) { page = 1 };
        if (!size) { size = 10 };
        page = (page - 1) * size;
        size = parseInt(size);
        return { page, size }
    },
    /**
     * 查询总数
     * @param string name 表名
     * @param string where 查询条件
     * @param object res 主体
    * */
    getSum({ name, where, res, req }) {
        return new Promise((resolve, reject) => {
            let sql = `SELECT count(1) FROM ${name} ${where}`;
            pool.query(sql, (err, result) => {
                if (err) return res.send(this.returnData({ code: -1, msg: "获取数据总数错误！", err, req }));
                resolve({ total: result[0]["count(1)"] });
            });
        })
    },
    /**
     * 获取用户权限
     * @param object req 请求主体
     * @param object res 响应主体
     * */
    getUserRole(req, res) {
        return new Promise(async (resolve, reject) => {
            let user = await this.getUserInfo(req, res);
            let userSql = "SELECT roles,role_key FROM roles WHERE FIND_IN_SET(id,?)";
            pool.query(userSql, [user.rolesId], (err, result) => {
                if (err || result.length == 0) return res.send(this.returnData({ code: -1, msg: "获取权限失败！", err, req }))
                let roles = result.map(t => t.roles);
                //权限字符
                let roleKey = result.map(t => t.role_key);
                //角色权限
                let roleAdmin = roleKey.some(t => t === "admin");
                resolve({ userRole: roles.join(","), roleKey, user, roleAdmin });
            });
        });
    },
    /**
     * 菜单字符权限拦截
     * @param object req 主体
     * @param object res 主体
     * @param array role 接口权限字符数组
     * @param boolean admin 是否管理员也要遵守（默认否）
     * */
    checkPermi({ req, res, role = [], admin = false }) {
        return new Promise(async (resolve, reject) => {
            let userRole = await this.getUserRole(req, res);
            if ((userRole.roleAdmin || userRole.user.admin === 1) && !admin) return resolve(true);
            let sql = "SELECT role_key AS roleKey FROM router_menu WHERE FIND_IN_SET(id,?)";
            pool.query(sql, [userRole.userRole], (err, result) => {
                try {
                    if (err) return res.send(this.returnData({ code: -1, msg: "菜单权限判断错误！", err, req }))
                    let roleKeyArr = result.map(t => t.roleKey).filter(t => t);
                    const hasPermission = role.some(permission => {
                        return roleKeyArr.includes(permission)
                    });
                    if (hasPermission) return resolve(hasPermission);
                    res.send(this.returnData({ code: -1, msg: "暂无对应菜单请求权限！", err, req }))
                } catch (e) {
                    res.send(this.returnData({ code: -1, msg: "菜单权限判断错误！！", err: e, req }))
                }
            })
        })
    },



    /**
     * 角色权限拦截
     * @param object req 主体
     * @param object res 主体
     * @param array role 角色权限数组
     * @param boolean admin 是否管理员也要遵守（默认否）
     * */
    async checkRole({ req, res, role = [], admin = false }) {
        try {
            let userRole = await this.getUserRole(req, res);
            if ((userRole.roleAdmin || userRole.user.admin === 1) && !admin) return true;
            let roleKeyArr = userRole.roleKey;
            const hasPermission = role.some(permission => {
                return roleKeyArr.includes(permission)
            });
            if (hasPermission) return true;
            res.send(this.returnData({ code: -1, msg: "暂无对应角色请求权限！", req }))
            return Promise.reject(false);
        } catch (e) {
            res.send(this.returnData({ code: -1, msg: "角色权限判断错误！", err: e, req }))
            return Promise.reject(false);
        }

    },
    /**
     * 是否操作的是用户总管理员
     * @param object req 请求主体
     * @param object res 响应主体
     * @param number id 查询条件id
     * */
    upAdmin({ req, res, id }) {
        return new Promise((resolve, reject) => {
            let sql = "SELECT admin FROM user WHERE id=?";
            pool.query(sql, [id], (err, result) => {
                if (err || result.length === 0) return res.send(this.returnData({ code: -1, msg: "管理信息判断错误！", err, req }));
                if (result[0].admin === 1) return res.send(this.returnData({ code: -1, msg: "无法对《总管理》执行此操作！", err, req }));
                resolve(result);
            })
        })

    },
    /**
     * 是否操作的是角色总管理员
     * @param object req 请求主体
     * @param object res 响应主体
     * @param number id 查询条件id
     * */
    upAdminRole({ req, res, id }) {
        return new Promise((resolve, reject) => {
            let sql = "SELECT role_key FROM roles WHERE id=?";
            pool.query(sql, [id], (err, result) => {
                if (err || result.length === 0) return res.send(this.returnData({ code: -1, msg: "管理信息判断错误！！", err, req }));
                if (result[0].role_key === "admin") return res.send(this.returnData({ code: -1, msg: "无法对《角色总管理》执行此操作！", err, req }));
                resolve(result);
            })
        })

    },
    /**
     * 通过id获取用户信息
     * @param object req 请求主体
     * @param object res 响应主体
     * @param number id 查询条件id
     * */
    getUserId({ req, res, id }) {
        return new Promise((resolve, reject) => {
            let sql = "SELECT admin FROM user WHERE id=?";
            pool.query(sql, [id], (err, result) => {
                if (err || result.length === 0) return res.send(this.returnData({ code: -1, msg: "用户信息错误！！", err, req }));
                resolve(result[0]);
            })
        })

    },
    /**
     * 生成时间戳作为数据id
     * */
    createId() {
        //取100-1000的随机整数
        // let abc = ['a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'];
        // // 随机取一个字母
        // let str = abc[Math.floor(Math.random()*abc.length)];
        // let str1 = abc[Math.floor(Math.random()*abc.length)];

        // let num =  Math.floor(Math.random()*(1000-100+1)+100);
        // let num1 =  Math.floor(Math.random()*(1000-100+1)+100);
        // let allNum = num+num1;
        // return str1+ str1+ new Date().getTime() +allNum;
        return uuid.v1()
    },
    /**
     * 返回当前时间戳
     * */
    createTime() {
        return +new Date();
    },
    //数据库操作 - 带重试机制
    dbUse(sql, arr, retries = 3) {
        return new Promise((resolve, reject) => {
            const executeQuery = (attempt) => {
                pool.query(sql, arr, (err, result) => {
                    if (err) {
                        console.error(`数据库查询错误 (尝试 ${attempt}/${retries}):`, err.message);
                        
                        // 如果是连接错误且还有重试次数，则重试
                        if ((err.code === 'ECONNRESET' || err.code === 'PROTOCOL_CONNECTION_LOST' || err.code === 'ETIMEDOUT') && attempt < retries) {
                            console.log(`${attempt + 1}秒后重试...`);
                            setTimeout(() => executeQuery(attempt + 1), (attempt + 1) * 1000);
                            return;
                        }
                        
                        reject(err);
                        return;
                    }
                    resolve(result);
                });
            };
            
            executeQuery(1);
        });
    },
    axiosQuery(method = "post", url, data, headers = {}) {
        return new Promise((resolve, reject) => {
            axios({
                url,
                method,
                data,
                headers
            }).then(res => {
                console.log(res.data);
                resolve(res.data);
            }).catch(err => {
                console.log(err)
                reject(err);
            })
        })
    },
    //数组分割
    splitArrayIntoChunks(originalArray, chunks) {
        const length = originalArray.length;
        const chunkSize = Math.ceil(length / chunks);
        return Array.from({ length: chunks }, (_, i) => originalArray.slice(i * chunkSize, (i + 1) * chunkSize));
    }
};


function dbUses(sql, arr) {
    return new Promise((resolve, reject) => {
        pool.query(sql, arr, (err, result) => {
            if (err) {
                reject(err);
            }
            resolve(result);
        })
    })
}