const { userCoupon, getUserInfos, bfindCouponFood, findVip, findUserRight, getRightCardDetail, getWalletBalance } = require("../config/request_api_config.js")
const config_item = require("../config/config_config.js")
const { pool } = require("../pool.js");
const utils = require("./index.js")

// API调用限流和缓存配置
const API_RATE_LIMIT = {
    delay: 100, // 每次API调用间隔100ms
    maxConcurrent: 5, // 最大并发数
    retryAttempts: 3, // 重试次数
    retryDelay: 1000 // 重试间隔
};

// 缓存对象
const cache = {
    userInfo: new Map(),
    couponProducts: new Map(),
    userCoupons: new Map(),
    ttl: 5 * 60 * 1000 // 5分钟缓存
};

// API调用队列
let apiQueue = [];
let activeRequests = 0;

// 延迟函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 带限流的API调用包装器
async function rateLimitedApiCall(apiFunction, ...args) {
    return new Promise((resolve, reject) => {
        apiQueue.push({ apiFunction, args, resolve, reject });
        processQueue();
    });
}

// 处理API调用队列
async function processQueue() {
    if (activeRequests >= API_RATE_LIMIT.maxConcurrent || apiQueue.length === 0) {
        return;
    }
    
    const { apiFunction, args, resolve, reject } = apiQueue.shift();
    activeRequests++;
    
    try {
        await delay(API_RATE_LIMIT.delay);
        const result = await executeWithRetry(apiFunction, args);
        resolve(result);
    } catch (error) {
        reject(error);
    } finally {
        activeRequests--;
        processQueue(); // 处理下一个请求
    }
}

// 带重试的API执行
async function executeWithRetry(apiFunction, args) {
    let lastError;
    
    for (let attempt = 1; attempt <= API_RATE_LIMIT.retryAttempts; attempt++) {
        try {
            return await apiFunction(...args);
        } catch (error) {
            lastError = error;
            console.log(`API调用失败，第${attempt}次重试:`, error.message);
            
            if (attempt < API_RATE_LIMIT.retryAttempts) {
                await delay(API_RATE_LIMIT.retryDelay * attempt); // 指数退避
            }
        }
    }
    
    throw lastError;
}

// 缓存工具函数
function getCacheKey(prefix, ...args) {
    return `${prefix}:${args.join(':')}`;
}

function getFromCache(cacheMap, key) {
    const item = cacheMap.get(key);
    if (item && Date.now() - item.timestamp < cache.ttl) {
        return item.data;
    }
    cacheMap.delete(key);
    return null;
}

function setCache(cacheMap, key, data) {
    cacheMap.set(key, {
        data,
        timestamp: Date.now()
    });
}

// 优化后的API调用函数
async function getCachedUserInfo(sid) {
    const cacheKey = getCacheKey('userInfo', sid);
    let cached = getFromCache(cache.userInfo, cacheKey);
    
    if (cached) {
        return cached;
    }
    
    const result = await rateLimitedApiCall(getUserInfos, sid);
    setCache(cache.userInfo, cacheKey, result);
    return result;
}

async function getCachedUserCoupons(channelCode, scene, storeCode, sid) {
    const cacheKey = getCacheKey('userCoupons', channelCode, scene, storeCode, sid);
    let cached = getFromCache(cache.userCoupons, cacheKey);
    
    if (cached) {
        return cached;
    }
    
    const result = await rateLimitedApiCall(userCoupon, channelCode, scene, storeCode, sid);
    setCache(cache.userCoupons, cacheKey, result);
    return result;
}

async function getCachedCouponProducts(couponCode, couponId, promotionId, storeCode, sid) {
    const cacheKey = getCacheKey('couponProducts', couponCode, couponId, promotionId, storeCode);
    let cached = getFromCache(cache.couponProducts, cacheKey);
    
    if (cached) {
        return cached;
    }
    
    const result = await rateLimitedApiCall(bfindCouponFood, couponCode, couponId, promotionId, storeCode, sid);
    setCache(cache.couponProducts, cacheKey, result);
    return result;
}
module.exports = {
     findCouponFoodk: async function (sid, userInfo) {

        //sid的券信息
        let sidCouponInfo = await userCoupon("03", '3', await config_item.storCode(), sid)
        //提取券列表
        let coupon = []//需要保存数据库
        let findFood = []//保存需要查商品的券信息
        let sidInfoArr = [{
            sid,
            userInfo
        }]
        if (sidCouponInfo.data) {
            let couponList = sidCouponInfo.data.productCouponType.coupons
            // 每个账号的券列表

            for (let i = 0; i < couponList.length; i++) {
                for (let j = 0; j < couponList[i].coupons.length; j++) {
                    if (j == 0 && couponList[i].coupons[j].title !== '满39元免外送费') {
                        couponList[i].coupons[j].sid = sid
                        findFood.push(couponList[i].coupons[j])
                    }
                    //检查数据库的coupon表中是否有couponList[i].coupons[j].code
                    let sql = "select * from mdl_coupon where COUPON_CODE = ?"
                    let arr = await utils.dbUse(sql, [couponList[i].coupons[j].code])
                    if (arr.length == 0 && couponList[i].coupons[j].title !== '满39元免外送费') {
                        couponList[i].coupons[j].sid = sid
                        coupon.push(couponList[i].coupons[j])
                    }
                }
            }

        }
        return { coupon, findFood, sidInfoArr }
    },
    getMdlUserInfoMethod: async function (sid, log_id, is) {
        //判断sid是否可用
        console.log(`开始验证账号: ${sid}`);
        
        try {
            let userInfo = await getCachedUserInfo(sid);
            // 检查userInfo是否有data属性
            if (!userInfo || !userInfo.data) {
                console.log('getUserInfos返回数据异常:', userInfo)
                // 根据错误码返回不同的错误信息
                let errorMsg = '';
                if (userInfo && userInfo.code === 401) {
                    errorMsg = `sid:${sid}未登录，请重新获取有效的登录凭证`;
                } else {
                    errorMsg = `sid:${sid}不可用,获取用户信息失败，返回数据结构异常`;
                }
                //记录当前sid不可用日志
                await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, errorMsg, sid, 0])
                return false
            }
            // 获取用户钱包余额并提取balance值（处理不存在情况）
            const walletBalance = await rateLimitedApiCall(getWalletBalance, sid);
            if (walletBalance.code === 200) {
                // 使用可选链和空值合并运算符提取balance，无值时设为'无余额信息'
                userInfo.data.walletBalance = walletBalance.data?.balance ?? '无余额信息'
            } else {
                userInfo.data.walletBalance = '获取失败'  // 接口异常处理
            }
            
            if (!is) {
                if (userInfo.code !== 200) {
                    console.log(userInfo)
                    //记录当前sid不可用日志
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `sid:${sid}不可用,获取用户信息失败，无法入库`, sid, 0])
                    return false
                }

                //判断当前sid是否在sid表中
                let sid_sql = "select * from mdl_sid where SID = ?"
                let sid_arr = await utils.dbUse(sid_sql, [sid])
                if (sid_arr.length > 0) {
                    //记录当前sid不可用日志
                    await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `sid:${sid}已入库,不可重复入库`, sid, 0])
                    return false
                }
                //不返回结果，直接进入查券
            }

            return findCouponFood(sid, userInfo.data);
        } catch (error) {
            console.log(`账号验证失败 ${sid}:`, error.message);
            await utils.dbUse("INSERT INTO mdl_log_data(ID,LOG_ID,MESSAGE,ACCOUNT,TYPE) VALUES (?,?,?,?,?)", [utils.createId(), log_id, `sid:${sid}验证失败: ${error.message}`, sid, 0]);
            return false;
        }
    },

    findCouponFoodMethod: async function (findFood, sid) {
        console.log(`查询券商品: ${findFood.title} - ${sid}`);
        try {
            let prouctArr = await addFood(findFood.code, findFood.id, findFood.promotionId, await config_item.storCode(), sid, findFood.title, findFood.totalCount);
            return prouctArr;
        } catch (error) {
            console.log(`券商品查询失败 ${findFood.title} - ${sid}:`, error.message);
            return [];
        }
    },
    //权益查询 
    async qxFind(sid) {//四件套,6折早餐,咖啡,福利金,免配送,新品尝鲜
        let codeList = ["2022063017364600008", "2022063017244000006", "2022063017422700009", "2022063017454900011", "2022063017434700010", "2022052622112600005"]
        for (let i = 0; i < codeList.length; i++) {
            //咖啡权益/早餐
            if(i == 1){
                // let rightRes = await findUserRight()
                //先查询 https://api.mcd.cn/bff/promotion/coupons/rightCards?scene=3
                
                //后查询 https://api.mcd.cn/bff/promotion/coupons/rightCard/detail/v2?cardId//=CRD7447070AB71EFCDEE336CF5A3C7354E1&cardNo=CRD603155720903453478&cardType=2&cityCode=610500
            }
            
            let result = await findVip(codeList[i], sid)
            if (result.code == 200 && result.data.useDetails) { //&& result.data.useDetails
                let sql = "update mdl_sid set LOGIN_CODE = ?"
                utils.dbUse(sql,[1])
                if (i == 0) {
                    //修改当前sid的权益信息
                    let sql = "update mdl_sid set MY_KA = ?,MY_KA_USE = ? where SID = ?"
                    utils.dbUse(sql, [result.data.useDetails[1].quantity, result.data.useDetails[0].quantity, sid])
                }
                if (i == 1) {
                    let sql = "update mdl_sid set MYKA_BREAKFAST = ?,MYKA_BREAKFAST_USE = ? where SID = ?"
                    utils.dbUse(sql, [result.data.useDetails[1].quantity, result.data.useDetails[0].quantity, sid])
                }
                if (i == 2) {
                    let sql = "update mdl_sid set COFFEE = ?,COFFEE_USE = ? where SID = ?"
                    utils.dbUse(sql, [result.data.useDetails[1].quantity, result.data.useDetails[0].quantity, sid])
                }
                if (i == 3) {
                    let sql = "update mdl_sid set BENEFITS = ?,BENEFITS_USE = ? where SID = ?"
                    utils.dbUse(sql, [result.data.useDetails[1].quantity, result.data.useDetails[0].quantity, sid])
                }
                if (i == 5) {
                    let sql = "update mdl_sid set XINPINCHANG = ?,XINPINCHANG_USE = ? where SID = ?"
                    utils.dbUse(sql, [result.data.useDetails[1].quantity, result.data.useDetails[0].quantity, sid])
                }
                if (i == 4) {
                    let sql = "update mdl_sid set FREE_DELIVERY = ?,FREE_DELIVERY_USE = ? where SID = ?"
                    utils.dbUse(sql, [result.data.useDetails[1].quantity, result.data.useDetails[0].quantity, sid])
                }
                // if(i==6){
                //     let sql = "update mdl_sid set FREE_DELIVERY = ?,FREE_DELIVERY_USE = ? where SID = ?"
                //     utils.dbUse(sql,[result.data.useDetails[0].quantity,result.data.useDetails[1].quantity,sid])
                // }
            }else{
                if(i ==0){
                    //修改当前sid的权益信息
                    let sql = "update mdl_sid set MY_KA = ?,MY_KA_USE = ? where SID = ?"
                    utils.dbUse(sql, [0, 0, sid])
                }
                if (i == 1) {
                    let sql = "update mdl_sid set MYKA_BREAKFAST = ?,MYKA_BREAKFAST_USE = ? where SID = ?"
                    utils.dbUse(sql, [0, 0, sid])
                }
                if (i == 2) {
                    let sql = "update mdl_sid set COFFEE = ?,COFFEE_USE = ? where SID = ?"
                    utils.dbUse(sql, [0, 0, sid])
                }
               if (i == 3) {
                    let sql = "update mdl_sid set BENEFITS = ?,BENEFITS_USE = ? where SID = ?"
                    utils.dbUse(sql, [0, 0, sid])
                }
                if (i == 4) {
                    let sql = "update mdl_sid set FREE_DELIVERY = ?,FREE_DELIVERY_USE = ? where SID = ?"
                    utils.dbUse(sql, [0, 0, sid])
                }
            }
        }
        let rightCard = await findUserRight(sid)
        if (rightCard.data.code == 200) {
            let cardId, cardNo, redeemEndTime
            for (let i = 0; i < rightCard.data.data.cards.length; i++) {
                if (rightCard.data.data.cards[i].title == "早餐双月卡19.8" || rightCard.data.data.cards[i].title == "早餐单月卡11.9" || rightCard.data.data.cards[i].title == "早餐月卡11.9") {
                    redeemEndTime = rightCard.data.data.cards[i].redeemEndTime
                    cardId = rightCard.data.data.cards[i].cardId
                    cardNo = rightCard.data.data.cards[i].cardNo
                }
            }
 
            if (cardId && cardNo) {
                //修改sid的早餐信息
                let sql = "update mdl_sid set BREAKFAST_CARD = ? ,BREAKFAST_END_TIME=?, BREAKFAST_CARD_ID =? ,BREAKFAST_CARD_NO =? where SID = ?"
                await utils.dbUse(sql, [1, redeemEndTime, cardId, cardNo, sid])
                let getRightCardDetailResult = await getRightCardDetail(sid, cardId, cardNo)
                if (getRightCardDetailResult.data.code == 200) {
                    let rightArr = getRightCardDetailResult.data.data.rightCard.coupons
                    for (let i = 0; i < rightArr.length; i++) {
                        if (rightArr[i].title == "早餐双月卡19.8" || rightArr[i].title == "早餐单月卡11.9" || rightArr[i].title == "早餐月卡11.9" || "早餐6折起（单月卡）") {
                            if (rightArr[i].currentDayAvailableCount) {
                                
                                //今日可用
                                let sql = "update mdl_sid set BREAKFAST_CARD = ?,  BREAKFAST_CARD_USE = ? where SID = ?"
                                await utils.dbUse(sql, [1,1, sid])
                            }else{
                                console.log("早餐今日不可用:"+sid)
                                let sql = "update mdl_sid set BREAKFAST_CARD = ?,  BREAKFAST_CARD_USE = ? where SID = ?"
                                await utils.dbUse(sql, [1,0, sid])
                            }
                        }
                        //增加咖啡权益更新
                        console.log("咖啡状态更新")
                        if(rightArr[i].title == "麦咖啡天天9.9" ){
                            if (rightArr[i].currentDayAvailableCount) {
                                let sql = "update mdl_sid set COFFEE = ?,COFFEE_USE = ? where SID = ?"
                                utils.dbUse(sql, [1, 1, sid])
                            }else{
                                let sql = "update mdl_sid set COFFEE = ?,COFFEE_USE = ? where SID = ?"
                                utils.dbUse(sql, [1, 0, sid])
                            }
                        }
                    }
                }else{
                    let sql = "update mdl_sid set BREAKFAST_CARD = ? ,BREAKFAST_END_TIME=?, BREAKFAST_CARD_ID =? ,BREAKFAST_CARD_NO =? where SID = ?"
                await utils.dbUse(sql, [0, '异常', "异常", "异常", sid])
                }
            }else{
                console.log("早餐已过期:"+sid)
                let sql = "update mdl_sid set BREAKFAST_CARD = ? ,BREAKFAST_END_TIME=?, BREAKFAST_CARD_ID =? ,BREAKFAST_CARD_NO =? where SID = ?"
                await utils.dbUse(sql, [0, '已过期', "已过期", "已过期", sid])
            }
        } else if (rightCard.code == 401) {
            //修改sid的登陆状态
            let sql = "update mdl_sid set LOGIN_CODE = ? where SID = ?"
            utils.dbUse(sql, [0, sid])
        }

    }
}


//查券
async function findCouponFood(sid, userInfo) {
    console.log(`开始查询券信息: ${sid}`);
    
    try {
        //sid的券信息 - 使用缓存
        let sidCouponInfo = await getCachedUserCoupons("03", '3', await config_item.storCode(), sid);
        //提取券列表
        let coupon = []//需要保存数据库
        let findFood = []//保存需要查商品的券信息
        let sidInfoArr = [{
            sid,
            userInfo
        }]
        
        if (sidCouponInfo.data) {
            let couponList = sidCouponInfo.data.productCouponType.coupons
            // 每个账号的券列表
            console.log(`账号 ${sid} 共有 ${couponList.length} 类券`);

            for (let i = 0; i < couponList.length; i++) {
                for (let j = 0; j < couponList[i].coupons.length; j++) {
                    if (j == 0 && couponList[i].coupons[j].title !== '满39元免外送费') {
                        couponList[i].coupons[j].sid = sid
                        findFood.push(couponList[i].coupons[j])
                    }
                    //检查数据库的coupon表中是否有couponList[i].coupons[j].code
                    let sql = "select * from mdl_coupon where COUPON_CODE = ?"
                    let arr = await utils.dbUse(sql, [couponList[i].coupons[j].code])
                    if (arr.length == 0 && couponList[i].coupons[j].title !== '满39元免外送费') {
                        couponList[i].coupons[j].sid = sid
                        coupon.push(couponList[i].coupons[j])
                    }
                }
            }
            console.log(`账号 ${sid} 需要查询商品的券数量: ${findFood.length}`);
        }
        return { coupon, findFood, sidInfoArr };
    } catch (error) {
        console.log(`查询券信息失败 ${sid}:`, error.message);
        return { coupon: [], findFood: [], sidInfoArr: [{ sid, userInfo }] };
    }


}

async function addFood(code, id, promotionId, storCode, sid, titltC, totalCount) {
    console.log(`查询券商品详情: ${titltC} - ${sid}`);
    
    try {
        // 使用缓存的券商品查询
        let food = await getCachedCouponProducts(code, id, promotionId, storCode, sid);
        let prouctArr = [];
        
        if (food.code == 200 && food.data.productList) {
            console.log(`券 ${titltC} 找到 ${food.data.productList.length} 个商品`);
            
            for (let i = 0; i < food.data.productList.length; i++) {
                let sql = "select * from mdl_product where PRODUCT_CODE = ?";
                let arr = await utils.dbUse(sql, [food.data.productList[i].code]);
                if (arr.length == 0) {
                    // 保持原始产品名称，不添加重复的券信息
                    // food.data.productList[i].productName = food.data.productList[i].productName + '_' + titltC + totalCount + '次' + '_' + id;
                    food.data.productList[i].couponID = id;
                    prouctArr.push(food.data.productList[i]);
                }
            }
            console.log(`券 ${titltC} 新增 ${prouctArr.length} 个商品到数据库`);
        } else {
            console.log(`券 ${titltC} 未找到可用商品`);
        }
        
        return prouctArr;
    } catch (error) {
        console.log(`查询券商品失败 ${titltC} - ${sid}:`, error.message);
        return [];
    }





}


