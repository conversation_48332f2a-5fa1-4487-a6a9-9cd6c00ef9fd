console.log('🚀 开始加载app.js');
const express = require('express');
console.log('✅ Express加载完成');
const bodyparser = require('body-parser'); //body中间件
console.log('✅ body-parser加载完成');
const testsRouter=require("./router/tests.js");//测试信息路由
console.log('✅ tests路由加载完成');
const web = require("./router/web.js")
console.log('✅ web路由加载完成');
const weijia = require('./router/weijia.js'); //微加路由
console.log('✅ weijia路由加载完成');
const newmdl = require("./router/newmdl.js")
console.log('✅ newmdl路由加载完成');
const cors = require('cors'); //解决跨域的中间件
console.log('✅ CORS加载完成');
const server = express();
console.log('✅ Express服务器实例创建完成');
console.log('正在加载配置文件');
const config = require('./config/env_config');
console.log('✅ 配置文件加载完成');
console.log('正在加载 node-schedule');
const schedule = require('node-schedule')
console.log('✅ node-schedule加载完成');
console.log('正在加载 pool.js');
const pool = require("./pool.js");
console.log('✅ pool.js加载完成');
console.log('正在加载 utils/err');
const {errLog}= require("./utils/err");
console.log('✅ utils/err加载完成');
console.log('正在加载 utils/index.js');
const utils = require("./utils/index.js");
console.log('✅ utils/index.js加载完成');
console.log('正在加载 middleware/performance');
const { performanceMonitor, monitorConnectionPool } = require('./middleware/performance');
console.log('✅ middleware/performance加载完成');
console.log('正在加载 middleware/api-adapter');
const { apiAdapter, errorAdapter, routeRewriter, requestBodyAdapter } = require('./middleware/api-adapter');
console.log('✅ middleware/api-adapter加载完成');
const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Swagger-UI
const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: '后端 API',
      version: '1.0.0',
      description: '后端项目 API 文档. Code-Gen by Gemini.',
    },
    servers: [
      {
        url: config.server.apiDocsUrl,
        description: '开发服务器',
      },
    ],
  },
  apis: ['./router/*.js'],
};

const swaggerDocs = swaggerJsDoc(swaggerOptions);
server.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// 启动性能监控
monitorConnectionPool(pool);

// 配置CORS以支持Vue管理后台
server.use(cors({
  origin: config.cors.origins,
  credentials: config.cors.credentials,
  methods: config.cors.methods,
  allowedHeaders: config.cors.allowedHeaders,
  exposedHeaders: config.cors.exposedHeaders
}));

// 最简单的测试路由 - 在所有中间件之前
console.log('正在注册 /super-simple-test 路由');
server.get('/super-simple-test', (req, res) => {
    console.log('超级简单测试路由被访问');
    res.send('Hello World!');
});
console.log('/super-simple-test 路由注册完成');

// 最早的测试路由
console.log('正在注册 /early-test 路由');
server.get('/early-test', (req, res) => {
    console.log('最早的测试路由被访问');
    res.json({ message: '最早的测试路由工作正常', timestamp: new Date().toISOString() });
});
console.log('/early-test 路由注册完成');

server.use(performanceMonitor); // 添加性能监控中间件

// 添加API适配中间件
server.use(routeRewriter); // 路由重写
server.use(requestBodyAdapter); // 请求体适配
server.use(apiAdapter); // API响应适配

server.use(express.static(config.app.staticPath)); //用户的静态资源
server.use(bodyparser.json());
const {qxFind} = require("./utils/findCouponFood.js")
const {manyFindCoupons} = require("./router/findCoupon.js")
const {getZaoCaoCanList,getCouponXq} = require("./config/request_api_config.js")




server.get("/mobile/product/list/:tid", (req, res) => {
    console.log(req.params.tid)
    let sql = "SELECT * FROM mdl_combo_meal WHERE TAB = ?";
    pool.query(sql, [req.params.tid], (err, result) => {
        if (err) {
            console.error('查询套餐数据失败:', err.message);
            return res.send(utils.returnData({ code: -1, msg: "查询失败", data: [] }));
        }
        res.send(utils.returnData({ data: result, total: 0 }));
    })
})

// server.use(bodyparser.urlencoded({//body中间件
// 	extended:false
// }));
server.use(async function (req, res, next)  {
	if(req.headers.token){
		let user=await utils.getUserInfo(req,res);
		if(user.status===0) return res.send(utils.returnData({code: 203, msg: "你账号已被禁用，请联系管理员！！",req}));
	}
	next();
})
process.on('unhandledRejection', (err, test) => {
	errLog({err,code:500,msg:"后端系统错误！",funName:"fatal"});
	}).on('uncaughtException', err => {
	errLog({err,code:500,msg:"后端系统错误！！",funName:"fatal"});
	});
// 添加简单测试路由
server.get('/admin/simple-test', (req, res) => {
    console.log('简单测试路由被访问');
    res.json({ message: '简单测试路由工作正常', timestamp: new Date().toISOString() });
});

// 添加API测试路由
server.get('/api/test', (req, res) => {
    console.log('API测试路由被访问');
    res.json({ message: 'API测试路由工作正常', timestamp: new Date().toISOString() });
});

const adminRouter = require('./router/admin.js'); //管理菜单等路由
const agiso = require("./router/agiso.js")//阿奇索路由
const apiRouter = require('./config/request_api_config.js'); //API路由
console.log('Admin路由已加载，类型:', typeof adminRouter);
server.use('/admin', adminRouter); //挂载用户信息路由
console.log('Admin路由已挂载到 /admin');
server.use("/tests",testsRouter);//挂载测试信息路由
server.use("/mdl",weijia);//挂载微加路由
server.use("/web",web);
server.use("/newmdl",newmdl)
server.use("/agiso",agiso)
server.use("/api",apiRouter);//挂载API路由

//重置会员sid的当日权益可用次数
schedule.scheduleJob(config.schedule.resetMemberRights, async function () {
    console.log('开始重置会员sid的当日可用次数')
	let allsid = `SELECT * FROM mdl_sid WHERE QY=?`
	let allSidList = await utils.dbUse(allsid,[1])
	for(let i = 0 ; i<allSidList.length;i++){
		qxFind(allSidList[i].SID)
	}
})
//每天03:00进行查券操作
schedule.scheduleJob(process.env.SCHEDULE_UPDATE_COUPONS || '1 26 0 * * *', async function () {
    console.log('开始更新券')
	let allsid = `SELECT * FROM mdl_sid`
	let allSidList = await utils.dbUse(allsid,[])
	for(let i = 0 ; i<allSidList.length;i++){
		manyFindCoupons(allSidList[i].SID,allSidList[i].PARENT_MENU)
	}
})

//定时15分钟将未支付的订单转支付超时
//超时的订单删除
schedule.scheduleJob(process.env.SCHEDULE_CLEANUP_ORDERS || '* * * * * *', async function () {
    //获取当前时间戳 ，删除oders表中timestamp字段大于当前时间戳加15分钟的数据
    //生成当前时间戳
    let now = new Date().getTime();
    //当前时间戳减去15分钟的时间戳
    let time = now - 15 * 60 * 1000;
    //删除oders表中小于当前time的
    let sql = 'SELECT * FROM  mdl_order WHERE CREATRD_TIME < ? and ODER_STATUS = 0 '
    pool.query(sql,[time],function(err,res){
        if (err) {
            console.error('查询过期订单失败:', err.message);
            return;
        }
        if (res && res.length > 0) {
            for(let i = 0 ; i < res.length;i++){
                //解锁sid
                unlockSidOne(res[i].OPEN_ID)
            }
        }
    })
    pool.query("UPDATE mdl_order SET ODER_STATUS = 3 WHERE CREATRD_TIME < ? and ODER_STATUS = 0;", [time], function (err, res) {
        if (err) {
            console.error('更新过期订单状态失败:', err.message);
            return;
        }
        if (res && res.affectedRows > 0) {
            console.log("修改成功")
        }
    });
    
})

//解锁sid
async function unlockSidOne(sid) {
    //修改sid的状态
    let sql = `UPDATE mdl_sid SET START = ? WHERE SID = ?`
    await utils.dbUse(sql, [0, sid])
}

//每天清理过期的优惠券 '1 2 0 * * *'2 2 0
schedule.scheduleJob(process.env.SCHEDULE_DELETE_EXPIRED_COUPONS || '1 27 0 * * *',function () {
    deleteCoupon()
     refreshNumber()
})
deleteCoupon()
async function deleteCoupon(){
  let date = new Date().valueOf() - 86400000
    console.log("删除过期")
    let sql = 'DELETE FROM mdl_coupon WHERE LOGTIME < ?'
    await utils.dbUse(sql,[date])
}
//刷新每日的次卡数量
async function refreshNumber(){
    //查询总次数大于1的券
    let findsql = 'SELECT * FROM `mdl_coupon` WHERE `TOTAL_COUNT` > 1'
    let result =  await utils.dbUse(findsql, [])
    for(let i = 0 ; i < result.length ;i++){
        let coupon = await getCouponXq(result[i].COUPON_CODE,result[i].COUPON_SID_ID,result[i].COUPON_ID)
        if(coupon.code == 200){
            //修改券的今日可用次数
            let updateDayNumber = 'UPDATE mdl_coupon SET CURRENT_DAY_AVAILABLE_COUNT = ? WHERE ID = ?'
            await utils.dbUse(updateDayNumber, [coupon.data.coupon.currentDayAvailableCount,result[i].ID])
        }
    }
}

// 添加错误处理中间件（必须在所有路由之后）
server.use(errorAdapter);

// 启动Express服务器（必须在所有路由和中间件之后）
server.listen(config.server.port, config.server.host, () => {
    console.log(`后端接口启动成功，监听地址 ${config.server.host}:${config.server.port}`);
});
