version: '3.8'

services:
  admin-dev:
    build:
      context: ./admin-backend-backup
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    volumes:
      - ./admin-backend-backup:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    command: npm run serve
    networks:
      - mcd-network

  backend:
    build: ./后端
    ports:
      - "3000:3000"
    volumes:
      - ./后端:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    command: nodemon app.js
    networks:
      - mcd-network

  mcd-api-server:
    build: ./mcd-api-server
    ports:
      - "3001:3001"
    volumes:
      - ./mcd-api-server:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    command: nodemon server.js
    networks:
      - mcd-network

networks:
  mcd-network:
    driver: bridge
