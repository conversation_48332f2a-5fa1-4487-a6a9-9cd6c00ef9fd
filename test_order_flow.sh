#!/bin/bash

echo "🔍 兑换码到下订单完整流程测试"
echo "=================================="

# 测试变量
BASE_URL="http://localhost:3000/web"
TEST_CODE="EDIAGCCG"
STORE_CODE="1960113"
STORE_NAME="武汉光谷步行街餐厅"
PHONE="18888888888"
TAKE_MEAL="1"

echo
echo "📋 测试环境:"
echo "- API地址: $BASE_URL"
echo "- 兑换码: $TEST_CODE"
echo "- 门店: $STORE_CODE ($STORE_NAME)"
echo "- 手机号: $PHONE"
echo "- 取餐方式: $TAKE_MEAL"

echo
echo "🔍 步骤1: 验证兑换码..."
VFT_RESULT=$(curl -s "$BASE_URL/vft" -X POST -H "Content-Type: application/json" -d "{\"code\":\"$TEST_CODE\"}")
VFT_CODE=$(echo "$VFT_RESULT" | jq -r '.code')

if [ "$VFT_CODE" = "200" ] || [ "$VFT_CODE" = "201" ]; then
    echo "✅ 兑换码验证成功"
    echo "   状态: $(echo "$VFT_RESULT" | jq -r '.message')"
else
    echo "❌ 兑换码验证失败: $VFT_CODE"
    echo "   错误: $(echo "$VFT_RESULT" | jq -r '.message')"
    exit 1
fi

echo
echo "🔍 步骤2: 获取套餐信息..."
PACKAGE_RESULT=$(curl -s "$BASE_URL/getCashCouponList" -X POST -H "Content-Type: application/json" -d "{\"code\":\"$TEST_CODE\"}")
PACKAGE_CODE=$(echo "$PACKAGE_RESULT" | jq -r '.code')

if [ "$PACKAGE_CODE" = "200" ]; then
    echo "✅ 套餐信息获取成功"
    MEAL_TITLE=$(echo "$PACKAGE_RESULT" | jq -r '.data[0].TITLE')
    MEAL_ID=$(echo "$PACKAGE_RESULT" | jq -r '.data[0].ID')
    MEAL_COUNT=$(echo "$PACKAGE_RESULT" | jq -r '.anum')
    MEAL_PLAN=$(echo "$PACKAGE_RESULT" | jq -r '.data[0].PLAN // "未知"')
    echo "   套餐名称: $MEAL_TITLE"
    echo "   套餐ID: $MEAL_ID"
    echo "   可选数量: $MEAL_COUNT"
    echo "   方案类型: $MEAL_PLAN"
    
    # 保存完整的套餐信息用于后续分析
    echo "$PACKAGE_RESULT" > /tmp/package_info.json
    echo "   套餐详情已保存到 /tmp/package_info.json"
else
    echo "❌ 套餐信息获取失败: $PACKAGE_CODE"
    exit 1
fi

echo
echo "🔍 步骤3: 获取商品详情..."
DETAIL_RESULT=$(curl -s "$BASE_URL/getCashCouponListById" -X POST -H "Content-Type: application/json" -d "{\"id\":[{\"id\":\"$MEAL_ID\"}],\"storeCode\":\"$STORE_CODE\"}")
DETAIL_CODE=$(echo "$DETAIL_RESULT" | jq -r '.code')

echo "   商品详情API响应码: $DETAIL_CODE"
if [ "$DETAIL_CODE" = "200" ]; then
    echo "✅ 商品详情获取成功"
    echo "$DETAIL_RESULT" > /tmp/product_detail.json
    echo "   商品详情已保存到 /tmp/product_detail.json"
    
    # 分析商品结构
    POSITIVE_COUNT=$(echo "$DETAIL_RESULT" | jq -r '.data[0].POSITIVE | length')
    TICKET_COUNT=$(echo "$DETAIL_RESULT" | jq -r '.data[0].TICKET | length')
    echo "   正价商品数量: $POSITIVE_COUNT"
    echo "   有券商品数量: $TICKET_COUNT"
    
    # 构建foodData
    FOOD_DATA=$(echo "$DETAIL_RESULT" | jq -r '.data[0]')
    
elif [ "$DETAIL_CODE" = "500" ]; then
    echo "⚠️  商品详情返回业务错误: $(echo "$DETAIL_RESULT" | jq -r '.message')"
    echo "   这可能是正常的业务逻辑，继续测试其他功能..."
    
    # 构建模拟的foodData用于测试
    FOOD_DATA='{"ID":"'$MEAL_ID'","POSITIVE":[],"TICKET":[]}'
else
    echo "❌ 商品详情获取失败: $DETAIL_CODE"
    exit 1
fi

echo
echo "🔍 步骤4: 获取定制信息..."
CUSTOM_RESULT=$(curl -s "$BASE_URL/getCustomization" -X POST -H "Content-Type: application/json" -d "{\"storeCode\":\"$STORE_CODE\",\"productCode\":\"1000\"}")
CUSTOM_CODE=$(echo "$CUSTOM_RESULT" | jq -r '.code')
echo "   定制信息API响应码: $CUSTOM_CODE"

echo
echo "🔍 步骤5: 获取取餐方式..."
TAKEAWAY_RESULT=$(curl -s "$BASE_URL/getTakeA" -X POST -H "Content-Type: application/json" -d "{\"storeCode\":\"$STORE_CODE\"}")
TAKEAWAY_CODE=$(echo "$TAKEAWAY_RESULT" | jq -r '.code')
echo "   取餐方式API响应码: $TAKEAWAY_CODE"

if [ "$TAKEAWAY_CODE" = "200" ]; then
    echo "✅ 取餐方式获取成功"
    echo "$TAKEAWAY_RESULT" > /tmp/takeaway_info.json
    TAKEAWAY_OPTIONS=$(echo "$TAKEAWAY_RESULT" | jq -r '.data | length')
    echo "   可用取餐方式数量: $TAKEAWAY_OPTIONS"
fi

echo
echo "🔍 步骤6: 测试下单接口..."
echo "⚠️  注意: 这是真实下单测试，可能会消耗兑换码！"

# 构建下单请求数据
ORDER_DATA=$(cat << EOF
{
    "code": "$TEST_CODE",
    "foodData": [$FOOD_DATA],
    "storeCode": "$STORE_CODE",
    "iphone": "$PHONE",
    "TakeAMeal": "$TAKE_MEAL",
    "storeName": "$STORE_NAME"
}
EOF
)

echo "   下单请求数据:"
echo "$ORDER_DATA" | jq .

# 保存下单数据用于分析
echo "$ORDER_DATA" > /tmp/order_request.json
echo "   下单请求已保存到 /tmp/order_request.json"

echo
echo "📊 数据结构分析:"
echo "================"

if [ -f "/tmp/package_info.json" ]; then
    echo "套餐信息结构:"
    jq '.data[0] | {ID, TITLE, PLAN, ACCOUNT, POSITIVE: (.POSITIVE | length), TICKET: (.TICKET | length)}' /tmp/package_info.json
fi

if [ -f "/tmp/product_detail.json" ]; then
    echo
    echo "商品详情结构:"
    jq '.data[0] | {ID, POSITIVE: (.POSITIVE | length), TICKET: (.TICKET | length)}' /tmp/product_detail.json 2>/dev/null || echo "商品详情解析失败"
fi

echo
echo "🔍 潜在问题分析:"
echo "================"

# 检查数据一致性
if [ -f "/tmp/package_info.json" ] && [ -f "/tmp/product_detail.json" ]; then
    PACKAGE_MEAL_COUNT=$(jq -r '.data | length' /tmp/package_info.json)
    DETAIL_MEAL_COUNT=$(jq -r '.data | length' /tmp/product_detail.json 2>/dev/null || echo "0")
    
    echo "1. 数据数量检查:"
    echo "   套餐信息中的商品数量: $PACKAGE_MEAL_COUNT"
    echo "   商品详情中的商品数量: $DETAIL_MEAL_COUNT"
    
    if [ "$PACKAGE_MEAL_COUNT" != "$DETAIL_MEAL_COUNT" ] && [ "$DETAIL_MEAL_COUNT" != "0" ]; then
        echo "   ⚠️  数量不匹配，可能导致下单失败"
    fi
fi

echo
echo "2. 常见下单失败原因:"
echo "   - 商品已售罄（getCashCouponListById返回500）"
echo "   - foodData数量与套餐配置不匹配"
echo "   - 门店不支持该商品"
echo "   - 账号余额不足"
echo "   - 取餐方式不正确"
echo "   - 兑换码已被使用"

echo
echo "📝 建议检查项目:"
echo "================"
echo "1. 检查后端日志: docker-compose logs backend"
echo "2. 检查数据库中的套餐配置"
echo "3. 验证前端传递的foodData格式"
echo "4. 确认门店是否支持该套餐"
echo "5. 检查账号池是否有可用账号"

echo
echo "🎯 测试完成！"
echo "如需进行实际下单测试，请手动执行:"
echo "curl -s '$BASE_URL/addCashCouponOrder' -X POST -H 'Content-Type: application/json' -d @/tmp/order_request.json"
