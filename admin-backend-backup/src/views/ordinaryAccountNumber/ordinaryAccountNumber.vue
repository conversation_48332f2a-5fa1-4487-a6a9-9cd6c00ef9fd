<template>
  <div class="boxs">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">普通账号管理</h2>
      <p class="page-description">管理和监控所有普通账号的状态、余额和使用情况</p>
    </div>

    <!-- 核心统计卡片 -->
    <div class="core-statistics">
      <el-row :gutter="12">
        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
          <div class="stat-card total-card">
            <div class="stat-content">
              <div class="stat-number">{{ statisticsData.totalCount }}</div>
              <div class="stat-label">总账号数</div>
            </div>
            <div class="stat-icon">
              <i class="el-icon-user"></i>
            </div>
          </div>
        </el-col>
        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
          <div class="stat-card active-card">
            <div class="stat-content">
              <div class="stat-number">{{ statisticsData.enabledCount }}</div>
              <div class="stat-label">启用账号</div>
            </div>
            <div class="stat-icon">
              <i class="el-icon-check"></i>
            </div>
          </div>
        </el-col>
        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
          <div class="stat-card inactive-card">
            <div class="stat-content">
              <div class="stat-number">{{ statisticsData.disabledCount }}</div>
              <div class="stat-label">禁用账号</div>
            </div>
            <div class="stat-icon">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </el-col>
        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
          <div class="stat-card using-card">
            <div class="stat-content">
              <div class="stat-number">{{ statisticsData.inUseCount }}</div>
              <div class="stat-label">使用中</div>
            </div>
            <div class="stat-icon">
              <i class="el-icon-loading"></i>
            </div>
          </div>
        </el-col>
        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
          <div class="stat-card idle-card">
            <div class="stat-content">
              <div class="stat-number">{{ statisticsData.idleCount }}</div>
              <div class="stat-label">空闲</div>
            </div>
            <div class="stat-icon">
              <i class="el-icon-time"></i>
            </div>
          </div>
        </el-col>
        <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
          <div class="stat-card today-card">
            <div class="stat-content">
              <div class="stat-number">{{ statisticsData.todayCount }}</div>
              <div class="stat-label">今日新增</div>
            </div>
            <div class="stat-icon">
              <i class="el-icon-plus"></i>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="账号搜索" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入账号" clearable size="small"
            prefix-icon="el-icon-search" />
        </el-form-item>
        <el-form-item label="启用状态" prop="enabled">
          <el-select v-model="queryParams.enabled" placeholder="请选择状态" clearable size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态" prop="start">
          <el-select v-model="queryParams.start" placeholder="请选择使用状态" clearable size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="使用中" value="1"></el-option>
            <el-option label="空闲" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button icon="el-icon-plus" type="primary" size="small" @click="add">
          <span>新增账号</span>
        </el-button>
        <el-button type="success" plain size="small" icon="el-icon-download" @click="handleExport">
          <span>导出数据</span>
        </el-button>
      </div>
      <div class="action-right">
        <el-button type="success" @click="batchEnable" :disabled="multipleSelection.length === 0" size="small">
          <i class="el-icon-check"></i>
          <span>批量启用 ({{ multipleSelection.length }})</span>
        </el-button>
        <el-button type="danger" @click="batchDisable" :disabled="multipleSelection.length === 0" size="small">
          <i class="el-icon-close"></i>
          <span>批量禁用 ({{ multipleSelection.length }})</span>
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      width="100%"
      max-height="500"
      :data="list"
      class="modern-table"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      border
      :header-cell-style="{
        background: '#f8f9fa',
        color: '#606266',
        fontWeight: '600'
      }">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="编号" align="center" width="120" prop="ID">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">{{ scope.row.ID }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180" prop="CREATE_TIME" :formatter="formatterCreateTime">
        <template slot-scope="scope">
          <span class="date-text">{{ formatterCreateTime(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库分类" align="center" width="150" prop="PARENT_MENU">
        <template slot-scope="scope">
          <el-tag type="success" class="category-tag">{{ returndatak(scope.row.PARENT_MENU) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="使用账号" align="center" width="300" prop="SID">
        <template slot-scope="scope">
          <div class="account-info">
            <div class="sid-container">
              <span class="account-text" :title="scope.row.SID">
                {{ showFullSid[scope.$index] ? scope.row.SID : maskSid(scope.row.SID) }}
              </span>
              <div class="sid-actions">
                <el-button type="text" size="mini" @click="toggleSidDisplay(scope.$index)"
                  :title="showFullSid[scope.$index] ? '隐藏SID' : '显示完整SID'">
                  <i :class="showFullSid[scope.$index] ? 'el-icon-view' : 'el-icon-hide'"></i>
                </el-button>
                <el-button type="text" size="mini" @click="copySid(scope.row.SID)" title="复制SID">
                  <i class="el-icon-copy-document"></i>
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="登录状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.LOGIN_CODE ? 'success' : 'danger'" size="small" class="login-status-tag">
            <i :class="scope.row.LOGIN_CODE ? 'el-icon-success' : 'el-icon-error'"></i>
            {{ scope.row.LOGIN_CODE ? '登录成功' : '登录失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" width="150" prop="remark">
        <template slot-scope="scope">
          <el-tag type="warning" size="small" v-if="scope.row.remark">{{ scope.row.remark }}</el-tag>
          <span v-else class="empty-text">-</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" width="120" prop="PHONE">
        <template slot-scope="scope">
          <span class="phone-text">{{ scope.row.PHONE || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="MeddyID" align="center" width="180" prop="MEDDYID">
        <template slot-scope="scope">
          <span class="meddy-id">{{ scope.row.MEDDYID || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="可用点数" align="center" width="100" prop="AVAILABLE_POINTS">
        <template slot-scope="scope">
          <span class="points-badge">{{ scope.row.AVAILABLE_POINTS || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center" width="120" prop="WALLET_BALANCE">
        <template slot-scope="scope">
          <span class="balance-badge">¥{{ formatBalance(scope.row.WALLET_BALANCE) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用状态" align="center" width="120">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.START" :active-value="1" :inactive-value="0" active-text="使用中"
            inactive-text="空闲" active-color="#409EFF" inactive-color="#909399"
            @change="handleAccountStartChange(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="账号启用" align="center" width="120">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.ENABLED" :active-value="1" :inactive-value="0" active-text="启用"
            inactive-text="禁用" active-color="#13ce66" inactive-color="#ff4949"
            @change="handleAccountEnabledChange(scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="500">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button size="mini" type="warning" plain @click="handleEditAccount(scope.row)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button size="mini" type="success" plain @click="viewAccountOrders(scope.row)">
              <i class="el-icon-document"></i> 查看订单
            </el-button>
            <el-dropdown trigger="click">
              <el-button size="mini" type="primary" plain>
                更多操作<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="queryBalance(scope.row)">
                  <i class="el-icon-wallet"></i> 查询余额
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleEdit(scope.row)">
                  <i class="el-icon-tickets"></i> 查券入库
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleEdits(scope.row)">
                  <i class="el-icon-delete"></i> 清除外部券
                </el-dropdown-item>
                <el-dropdown-item @click.native="updateMake(scope.row)">
                  <i class="el-icon-edit-outline"></i> 添加备注
                </el-dropdown-item>
                <el-dropdown-item divided @click.native="handleDelete(scope.row)">
                  <i class="el-icon-delete" style="color: #f56c6c;"></i>
                  <span style="color: #f56c6c;">删除账号</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
      @pagination="getAccount" />

    <el-dialog title="新增账号" :visible.sync="open" width="40%">
      <el-form class="demo-form-inline" label-width="100px" :model="form" :rules="rules" ref="form">
        <el-form-item label="sid分类" prop="name">
          <el-select v-model="form.classify" clearable filterable allow-create default-first-option placeholder="请选择分类">
            <el-option v-for="item in options" :key="item.ID" :label="item.CLASSIFY_TITLE" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="sid" prop="name">
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="form.sid_list">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" v-loading="addLoading">
        <el-button @click="open = false">取 消</el-button>
        <el-button :type="form.id ? 'warning' : 'primary'" @click="affirm">{{ form.id ? "确认修改" : "确认添加" }}</el-button>
      </div>
    </el-dialog>

    <el-dialog title="编辑账号信息" :visible.sync="editOpen" width="40%">
      <el-form class="demo-form-inline" label-width="120px" :model="editForm" :rules="editRules" ref="editForm">
        <el-form-item label="账号SID" prop="SID">
          <el-input v-model="editForm.SID" placeholder="请输入SID" disabled></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="PHONE">
          <el-input v-model="editForm.PHONE" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="MeddyID" prop="MEDDYID">
          <el-input v-model="editForm.MEDDYID" placeholder="请输入MeddyID"></el-input>
        </el-form-item>
        <el-form-item label="可用点数" prop="AVAILABLE_POINTS">
          <el-input-number v-model="editForm.AVAILABLE_POINTS" :min="0" placeholder="请输入可用点数"></el-input-number>
        </el-form-item>
        <el-form-item label="账号分类" prop="PARENT_MENU">
          <el-select v-model="editForm.PARENT_MENU" clearable filterable placeholder="请选择分类">
            <el-option v-for="item in options" :key="item.ID" :label="item.CLASSIFY_TITLE" :value="item.ID">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="登录状态" prop="LOGIN_CODE">
          <el-select v-model="editForm.LOGIN_CODE" placeholder="请选择登录状态">
            <el-option label="登录成功" :value="1"></el-option>
            <el-option label="登录失败" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态" prop="START">
          <el-select v-model="editForm.START" placeholder="请选择使用状态">
            <el-option label="空闲" :value="0"></el-option>
            <el-option label="使用中" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号启用" prop="ENABLED">
          <el-switch v-model="editForm.ENABLED" :active-value="1" :inactive-value="0" active-text="启用"
            inactive-text="禁用" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" :rows="3" v-model="editForm.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" v-loading="editLoading">
        <el-button @click="editOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmEditAccount">确认修改</el-button>
      </div>
    </el-dialog>

    <!-- 订单列表对话框 -->
    <el-dialog title="账号订单列表" :visible.sync="orderDialogVisible" width="90%" top="5vh">
      <div class="order-dialog-header">
        <div class="account-info-header">
          <el-tag type="primary" size="medium">
            <i class="el-icon-user"></i> 账号：{{ currentAccountInfo.SID }}
          </el-tag>
          <el-tag type="success" size="medium" v-if="currentAccountInfo.PHONE">
            <i class="el-icon-phone"></i> 手机：{{ currentAccountInfo.PHONE }}
          </el-tag>
        </div>

        <!-- 订单搜索 -->
        <el-form :inline="true" :model="orderQueryParams" class="demo-form-inline">
          <el-form-item label="订单号">
            <el-input v-model="orderQueryParams.name" placeholder="请输入订单号" size="small" style="width: 200px;" />
          </el-form-item>
          <el-form-item label="支付方式">
            <el-select v-model="orderQueryParams.paymentMethod" placeholder="请选择支付方式" size="small" style="width: 150px;" clearable>
              <el-option label="全部" value="" />
              <el-option label="支付宝" value="alipay" />
              <el-option label="微信支付" value="wechat" />
              <el-option label="余额支付" value="balance" />
            </el-select>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="orderQueryParams.orderStatus" placeholder="请选择订单状态" size="small" style="width: 150px;" clearable>
              <el-option label="全部" value="" />
              <el-option label="已支付" value="1" />
              <el-option label="已出餐" value="2" />
              <el-option label="已取餐" value="3" />
              <el-option label="已退款" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="门店">
            <el-input v-model="orderQueryParams.storeName" placeholder="请输入门店名称" size="small" style="width: 150px;" />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="orderQueryParams.date" type="datetimerange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" size="small" style="width: 350px;"
              value-format="timestamp" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="searchOrders">搜索</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetOrderSearch">重置</el-button>
            <el-button type="success" icon="el-icon-download" size="small" @click="exportOrders">导出</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 订单统计信息 -->
      <div class="order-statistics" v-if="orderStatistics">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ orderStatistics.total }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ orderStatistics.todayCount }}</div>
              <div class="stat-label">今日订单</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">¥{{ orderStatistics.totalAmount }}</div>
              <div class="stat-label">总金额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">¥{{ orderStatistics.todayAmount }}</div>
              <div class="stat-label">今日金额</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 订单列表表格 -->
      <el-table width="100%" :data="orderList" v-loading="orderLoading" stripe border max-height="400"
        :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: '600' }" element-loading-text="正在加载订单数据...">
        <!-- 空状态插槽 -->
        <template slot="empty">
          <div class="empty-state" style="padding: 40px 0; text-align: center;">
            <i class="el-icon-document" style="font-size: 48px; color: #ddd; margin-bottom: 16px;"></i>
            <p style="margin: 0 0 16px 0; color: #999; font-size: 14px;">暂无订单数据</p>
            <el-button size="small" type="primary" @click="getOrderList">
              <i class="el-icon-refresh"></i> 重新加载
            </el-button>
          </div>
        </template>
        <el-table-column label="订单号" align="center" width="180" prop="ORDER_ID">
          <template slot-scope="scope">
            <el-tag size="mini" type="info">{{ scope.row.ORDER_ID || scope.row.ODER_ID }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="商品名称" align="center" width="200" prop="MEAL_TITLE">
          <template slot-scope="scope">
            <span class="meal-title" :title="scope.row.MEAL_TITLE">{{ scope.row.MEAL_TITLE }}</span>
          </template>
        </el-table-column>
        <el-table-column label="门店" align="center" width="150" prop="STORE_NAME">
          <template slot-scope="scope">
            <span class="store-name">{{ scope.row.STORE_NAME || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单金额" align="center" width="120" prop="PRICE_NUM">
          <template slot-scope="scope">
            <span class="amount-text">¥{{ scope.row.PRICE_NUM || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付方式" align="center" width="120" prop="PAY_TEXT">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getPaymentMethodType(scope.row.PAY_TEXT)">
              <i :class="getPaymentMethodIcon(scope.row.PAY_TEXT)" style="margin-right: 4px;"></i>
              {{ getPaymentMethodText(scope.row.PAY_TEXT) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" width="120" prop="ORDER_STATUS">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getOrderStatusType(scope.row.ORDER_STATUS || scope.row.ODER_STATUS)">
              {{ getOrderStatusText(scope.row.ORDER_STATUS || scope.row.ODER_STATUS) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="取餐码" align="center" width="120" prop="PICKUP_CODE">
          <template slot-scope="scope">
            <span class="pickup-code">
              {{ (scope.row.ORDER_STATUS === 4 || scope.row.ODER_STATUS === 4) ? '已退款' : (scope.row.PICKUP_CODE || '-') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" width="180" prop="CREATED_TIME">
          <template slot-scope="scope">
            <span class="date-text">{{ formatOrderTime(scope.row.CREATED_TIME || scope.row.CREATRD_TIME) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewOrderDetail(scope.row)">
              <i class="el-icon-view"></i> 详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 订单分页 -->
      <div class="order-pagination">
        <el-pagination @size-change="handleOrderSizeChange" @current-change="handleOrderCurrentChange"
          :current-page="orderQueryParams.page" :page-sizes="[10, 20, 50, 100]" :page-size="orderQueryParams.size"
          layout="total, sizes, prev, pager, next, jumper" :total="orderTotal" />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      title="订单详情"
      :visible.sync="orderDetailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-loading="orderDetailLoading" element-loading-text="正在加载订单详情...">
        <div class="order-detail-content">
          <!-- 订单基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">订单信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <span class="label">订单号：</span>
                  <span class="value">{{ orderDetail.ORDER_ID || orderDetail.ODER_ID }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="label">订单状态：</span>
                  <el-tag :type="getOrderStatusType(orderDetail.ORDER_STATUS || orderDetail.ODER_STATUS)">
                    {{ getOrderStatusText(orderDetail.ORDER_STATUS || orderDetail.ODER_STATUS) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ formatOrderTime(orderDetail.CREATED_TIME || orderDetail.CREATRD_TIME) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="label">取餐码：</span>
                  <span class="value pickup-code">{{ orderDetail.PICKUP_CODE || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 商品信息 -->
          <div class="detail-section">
            <h3 class="section-title">商品信息</h3>
            <div class="detail-item">
              <span class="label">商品名称：</span>
              <span class="value">{{ orderDetail.MEAL_TITLE }}</span>
            </div>
          </div>

          <!-- 门店信息 -->
          <div class="detail-section">
            <h3 class="section-title">门店信息</h3>
            <div class="detail-item">
              <span class="label">门店名称：</span>
              <span class="value">{{ orderDetail.STORE_NAME || '-' }}</span>
            </div>
          </div>

          <!-- 支付信息 -->
          <div class="detail-section">
            <h3 class="section-title">支付信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <span class="label">支付方式：</span>
                  <el-tag :type="getPaymentMethodType(orderDetail.PAY_TEXT)" :icon="getPaymentMethodIcon(orderDetail.PAY_TEXT)">
                    {{ getPaymentMethodText(orderDetail.PAY_TEXT) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="label">订单金额：</span>
                  <span class="value amount">¥{{ orderDetail.PRICE_NUM || '0.00' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="retryGetOrderDetail" v-if="orderDetailLoading === false">
          <i class="el-icon-refresh"></i> 重新加载
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addDict, addSid, getAccount, getAccountStatistics, getClass, upDict, delsid, sidCouponlist, updateCouponsk, updataMakeData, getWalletBalance, getOrderPage, getMcdOrder } from "@/api/admin";
import '@/styles/order-detail.css'; // 引入订单详情样式
import axios from '@/utils/axios';
import { formatDate } from "@/utils";

export default {
  name: "Dict",
  data() {
    return {
      options: [],
      queryParams: {
        page: 1,
        size: 10,
        name: '',
        enabled: '',
        start: ''
      },
      total: 0,
      loading: false,
      addLoading: false,
      editLoading: false,
      list: [],
      open: false,
      editOpen: false,
      multipleSelection: [], // 多选数据
      showFullSid: {}, // 控制每行SID的显示状态
      // 添加统计数据对象
      statisticsData: {
        totalCount: 0,
        enabledCount: 0,
        disabledCount: 0,
        inUseCount: 0,
        idleCount: 0,
        todayCount: 0
      },
      form: {
        classify: "",
        sid_list: "",
        qy: 0
      },
      editForm: {
        ID: "",
        SID: "",
        PHONE: "",
        MEDDYID: "",
        AVAILABLE_POINTS: 0,
        PARENT_MENU: "",
        LOGIN_CODE: 1,
        START: 0,
        ENABLED: 1,
        remark: ""
      },
      rules: {
        classify: [
          { required: true, message: '请选择分类', trigger: 'blur' },
        ],
        sid_list: [
          { required: true, message: '请输入sid', trigger: 'blur' }
        ]
      },
      editRules: {
        AVAILABLE_POINTS: [
          { required: true, message: '请输入可用点数', trigger: 'blur' }
        ]
      },
      // 订单相关数据
      orderDialogVisible: false,
      orderLoading: false,
      orderList: [],
      orderTotal: 0,
      currentAccountInfo: {},
      orderQueryParams: {
        page: 1,
        size: 20,
        name: '',
        paymentMethod: '',
        orderStatus: '',
        storeName: '',
        date: null
      },
      orderStatistics: {
        total: 0,
        todayCount: 0,
        totalAmount: '0.00',
        todayAmount: '0.00'
      },
      // 订单详情相关数据
      orderDetailDialogVisible: false,
      orderDetailLoading: false,
      orderDetail: {}
    }
  },
  created() {
    this.getAccount();
    this.getClass();
  },
  computed: {
    dictType() {
      return this.$store.state.dictType;
    }
  },
  watch: {
    dictType() {
      this.getAccount();
    }
  },
  filters: {
    formatDate
  },
  methods: {
    returndatak(id) {
      let tit = ''
      for (let i = 0; i < this.options.length; i++) {
        if (this.options[i].ID == id) {
          tit = this.options[i].CLASSIFY_TITLE
        }
      }
      return tit
    },
    updateMake(row) {
      let that = this
      this.$prompt('更新备注', '备注', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async ({ value }) => {
        await updataMakeData({ id: row.ID, make: value })
        that.getAccount()
        this.$message({
          type: 'success',
          message: '你的备注是: ' + value
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    async handleEdits(row) {
      let result = await updateCouponsk({sid:row.SID})
      console.log(result)
      if (result.code == 200) {
        this.$message.success(result.message);
      } else {
        this.$message.error("失败");
      }
    },
    timestampToTime(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hour = date.getHours().toString().padStart(2, '0');
      const minute = date.getMinutes().toString().padStart(2, '0');
      const second = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    handleExport() {
      let str = `编号,创建时间,使用账号,手机号,meddyid,可用点数,余额\n`
      let newList = this.list.map(item => {
        return {
          ID: item.ID,
          CREATE_TIME: this.timestampToTime(Number(item.CREATE_TIME)),
          SID: item.SID,
          PHONE: item.PHONE,
          MEDDYID: item.MEDDYID,
          AVAILABLE_POINTS: item.AVAILABLE_POINTS,
          WALLET_BALANCE: item.WALLET_BALANCE
        }
      })
      for (let i = 0; i < newList.length; i++) {
        for (const key in newList[i]) {
          str += `${newList[i][key] + '\t'},`
        }
        str += '\n'
      }
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(str)
      const link = document.createElement('a')
      link.href = uri
      link.download = '普通账号数据表.csv'
      link.click()
    },
    getClass() {
      getClass().then(res => {
        this.options = res.data;
      })
    },
    affirm() {
      this.$refs.form.validate(async (validate) => {
        if (!this.form.classify) {
          this.$message.error("请选择分类");
          return
        }
        if (!this.form.sid_list) {
          this.$message.error("请填写sid");
          return
        }
        this.addLoading = true
        try {
          const res = await addSid(this.form);
          if (res.code === 1) {
            this.$message.success(res.msg || "正在上传，请稍后刷新");
            this.open = false;
          } else {
            this.$message.error(res.msg || "上传失败");
          }
        } catch (err) {
          console.error('请求失败:', err);
          this.$message.error('网络请求失败');
        } finally {
          this.addLoading = false;
        }
      })
      this.open = false;
      this.addLoading = false;
    },
    add() {
      this.form = {
        classify: "",
        sid_list: "",
        qy: 0
      };
      this.open = true;
    },

    handleQuery() {
      this.queryParams.page = 1;
      this.getAccount();
    },
    handleEdit(row) {
      this.form = { ...row };
      this.sidCouponlist({ sid: this.form.SID, classify: this.form.PARENT_MENU })
    },
    handleDelete(row) {
      this.$confirm(`是否删除改账号？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await delsid({ sid: row.SID });
        this.getAccount();
        this.$message.success("删除成功！");
      })
    },
    // 编辑账号
    handleEditAccount(row) {
      this.editForm = {
        ID: row.ID,
        SID: row.SID,
        PHONE: row.PHONE || '',
        MEDDYID: row.MEDDYID || '',
        AVAILABLE_POINTS: row.AVAILABLE_POINTS || 0,
        PARENT_MENU: row.PARENT_MENU || '',
        LOGIN_CODE: row.LOGIN_CODE || 1,
        START: row.START || 0,
        ENABLED: row.ENABLED !== undefined ? row.ENABLED : 1,
        remark: row.remark || ''
      }
      this.editOpen = true
    },
    // 确认编辑账号
    confirmEditAccount() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          this.editLoading = true
          this.updataSid()
        }
      })
    },
    // 禁用账号
    handleDisableAccount(row) {
      this.$confirm('确定要禁用该账号吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.updateAccountStatus(row.ID, 0)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    // 启用账号
    handleEnableAccount(row) {
      this.$confirm('确定要启用该账号吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }).then(() => {
        this.updateAccountStatus(row.ID, 1)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    // 更新账号状态
    updateAccountStatus(id, status) {
      this.$http.post('/admin/updateAccountEnabled', {
        ID: id,
        ENABLED: status
      }).then(res => {
        if (res.data.code === 200) {
          this.$message({
            type: 'success',
            message: status === 1 ? '账号启用成功' : '账号禁用成功'
          })
          this.getAccount()
        } else {
          this.$message({
            type: 'error',
            message: res.data.message || '操作失败'
          })
        }
      }).catch(err => {
        this.$message({
          type: 'error',
          message: '操作失败，请重试'
        })
      })
    },
    goDictList(row) {
      this.$router.push({ path: "/menus/dict/dictItem", query: { id: row.id } })
    },
    formatterCreateTime(row) {
      return formatDate(row.CREATE_TIME);
    },
    // 查券入库
    async sidCouponlist(params) {
      try {
        this.$message.success("开始查券");
        let res = await sidCouponlist(params)
        if (res && res.code == 200) {
          this.$message({
            message: res.data || '查券成功',
            type: 'success'
          });
        } else {
          this.$message.error((res && res.data) || '查券失败');
        }
      } catch (error) {
        console.error('查券错误:', error);
        this.$message.error('查券操作失败');
      }
    },
    // 格式化余额显示
    formatBalance(balance) {
      // 处理空值或未定义
      if (balance === null || balance === undefined || balance === '') {
        return '0.00';
      }

      // 处理特殊字符串
      if (balance === '无余额信息' || balance === '获取失败') {
        return '--';
      }

      // 尝试转换为数字
      const numericBalance = parseFloat(balance);
      if (isNaN(numericBalance)) {
        return '--';
      }

      // 返回格式化的数字
      return numericBalance.toFixed(2);
    },
    async queryBalance(row) {
      try {
        this.$message.info('正在查询余额...');
        const res = await getWalletBalance({ sid: row.SID });
        if (res && res.code === 200 && res.data && res.data.balance !== undefined) {
          // 修复数据处理逻辑，处理非数字返回值
          let balance = res.data.balance;

          // 检查是否为有效数字
          if (balance === '无余额信息' || balance === '获取失败' || balance === null || balance === undefined) {
            // 对于无效数据，显示友好提示并保持原值
            this.$message.warning('余额信息暂时无法获取，请稍后重试');
            return;
          }

          // 尝试转换为数字
          const numericBalance = parseFloat(balance);
          if (isNaN(numericBalance)) {
            // 如果转换失败，显示错误信息并保持原值
            this.$message.warning('余额数据格式异常，请联系管理员');
            return;
          }

          // 格式化为两位小数
          const formattedBalance = numericBalance.toFixed(2);
          this.$set(row, 'WALLET_BALANCE', formattedBalance);
          this.$message.success('余额更新成功');
        } else {
          this.$message.error((res && res.message) || '查询失败');
        }
      } catch (error) {
        console.error('查询余额错误:', error);
        this.$message.error('查询余额失败');
      }
    },
    updataSid() {
      const formData = this.editOpen ? this.editForm : this.form
      axios({
        path: '/admin/updataSid',
        method: 'POST',
        data: formData
      }).then(res => {
        if (res && res.code === 200) {
          this.$message.success('修改成功')
          this.editOpen = false
          this.getAccount() // 修复：调用正确的方法名
        } else {
          this.$message.error((res && res.message) || '修改失败')
        }
      }).catch(err => {
        console.error('API错误:', err)
        this.$message.error('请求失败')
      })
    },
    
    // 新增的状态管理方法
    // 切换单个SID状态
    async toggleSidStatus(row) {
      try {
        const newStatus = row.START == 1 ? 0 : 1
        // 使用现有的 updataSid 方法来更新状态
        const result = await axios({
          path: '/admin/updataSid',
          method: 'POST',
          data: {
            ID: row.ID,
            START: newStatus
          }
        })
        
        if (result && result.code === 200) {
          this.$message.success('状态更新成功')
          row.START = newStatus // 更新本地数据
        } else {
          this.$message.error((result && result.message) || '状态更新失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      }
    },
    
    // 批量启用
    async batchEnable() {
      const ids = this.multipleSelection.map(item => item.ID)
      await this.batchUpdateStatus(ids, 1)
    },
    
    // 批量禁用
    async batchDisable() {
      const ids = this.multipleSelection.map(item => item.ID)
      await this.batchUpdateStatus(ids, 0)
    },
    
    // 批量更新状态
    async batchUpdateStatus(ids, status) {
      try {
        // 逐个更新每个账号的启用状态
      for (const id of ids) {
          const result = await axios({
            path: '/admin/updateAccountEnabled',
            method: 'POST',
            data: {
              ID: id,
              ENABLED: status
            }
          })
          // 检查每个请求的结果
          if (!result || result.code !== 200) {
            throw new Error(`更新账号 ${id} 失败: ${(result && result.message) || '未知错误'}`)
          }
        }
        this.$message.success(`批量${status === 1 ? '启用' : '禁用'}成功`)
        this.getAccount() // 刷新列表
        } catch (error) {
        this.$message.error(error.message || '批量操作失败')
        console.error('批量操作错误:', error)
      }
    },
    
    // 表格多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 添加更新统计数据的方法
    updateStatistics() {
      // 调用统计API获取全量统计数据
      getAccountStatistics({
          name: this.queryParams.name || ''
      }).then(res => {
        if (res.code === 1 && res.data) {
          this.statisticsData = {
            totalCount: res.data.totalCount || 0,
            enabledCount: res.data.enabledCount || 0,
            disabledCount: res.data.disabledCount || 0,
            inUseCount: res.data.inUseCount || 0,
            idleCount: res.data.idleCount || 0,
            todayCount: res.data.todayCount || 0
          };
        }
      }).catch(err => {
        console.error('获取统计数据失败:', err);
      });
    },
    
    // 修改 getAccount 方法，在获取数据后更新统计
    getAccount() {
      this.loading = true;
      getAccount(this.queryParams).then(res => {
        this.loading = false;
        
        console.log('完整API响应:', res);
        
        // 根据实际API响应结构解析数据
        if (res.code === 1 && res.msg === '请求成功！') {
          // API返回成功，数据在 res.data 中，总数在 res.total 中
          this.list = res.data || [];
          this.total = res.total || 0;
          
          console.log('成功解析数据:', {
            list: this.list,
            total: this.total
          });
          
          // 更新统计数据
          this.updateStatistics();
        } else {
          console.error('API返回错误:', res.msg || '未知错误');
          this.$message.error(res.msg || '获取账号数据失败');
          this.list = [];
          this.total = 0;
        }
      }).catch(err => {
        this.loading = false;
        console.error('获取账号数据失败:', err);
        this.$message.error('获取账号数据失败');
        this.list = [];
        this.total = 0;
      });
    },
    
    // 添加重置查询方法
    resetQuery() {
      this.queryParams = {
        page: 1,
        size: 10,
        name: '',
        enabled: '',
        start: ''
      };
      this.getAccount();
    },
    
    // 脱敏显示SID
    maskSid(sid) {
      if (!sid || sid.length <= 8) {
        return sid;
      }
      const start = sid.substring(0, 4);
      const end = sid.substring(sid.length - 4);
      const middle = '*'.repeat(Math.min(sid.length - 8, 8));
      return `${start}${middle}${end}`;
    },
    
    // 切换SID显示状态
    toggleSidDisplay(index) {
      this.$set(this.showFullSid, index, !this.showFullSid[index]);
    },
    
    // 复制SID到剪贴板
    async copySid(sid) {
      try {
        if (navigator.clipboard && window.isSecureContext) {
          // 使用现代 Clipboard API
          await navigator.clipboard.writeText(sid);
        } else {
          // 降级方案：使用传统方法
          const textArea = document.createElement('textarea');
          textArea.value = sid;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          document.execCommand('copy');
          textArea.remove();
        }
        this.$message.success('SID已复制到剪贴板');
      } catch (err) {
        console.error('复制失败:', err);
        this.$message.error('复制失败，请手动复制');
      }
    },
    
    // 处理账号启用/禁用状态变化
    async handleAccountEnabledChange(row) {
      try {
        const status = row.ENABLED;
        const statusText = status === 1 ? '启用' : '禁用';
        
        this.$confirm(`确定要${statusText}该账号吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: status === 1 ? 'success' : 'warning'
        }).then(async () => {
          // 调用后端API更新账号启用状态
          const result = await axios({
            path: '/admin/updateAccountEnabled',
            method: 'POST',
            data: {
              ID: row.ID,
              ENABLED: status
            }
          });
          
          if (result && result.code === 200) {
            this.$message.success(`账号${statusText}成功`);
            // 更新本地数据
            this.$set(row, 'ENABLED', status);
          } else {
            this.$message.error((result && result.message) || `账号${statusText}失败`);
            // 恢复原状态
            this.$set(row, 'ENABLED', status === 1 ? 0 : 1);
          }
        }).catch(() => {
          // 用户取消，恢复原状态
          this.$set(row, 'ENABLED', status === 1 ? 0 : 1);
          this.$message.info('已取消操作');
        });
      } catch (error) {
        console.error('更新账号启用状态失败:', error);
        this.$message.error('操作失败，请重试');
        // 恢复原状态
        this.$set(row, 'ENABLED', row.ENABLED === 1 ? 0 : 1);
      }
    },
    
    // 处理账号使用状态变化
    async handleAccountStartChange(row) {
      try {
        const status = row.START;
        const statusText = status === 1 ? '设为使用中' : '设为空闲';
        
        this.$confirm(`确定要${statusText}该账号吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }).then(async () => {
          // 调用后端API更新账号使用状态
          const result = await axios({
            path: '/admin/updataSid',
            method: 'POST',
            data: {
              ID: row.ID,
              START: status
            }
          });
          
          if (result && result.code === 200) {
            this.$message.success(`账号${statusText}成功`);
            // 更新本地数据
            this.$set(row, 'START', status);
          } else {
            this.$message.error((result && result.message) || `账号${statusText}失败`);
            // 恢复原状态
            this.$set(row, 'START', status === 1 ? 0 : 1);
          }
        }).catch(() => {
          // 用户取消，恢复原状态
          this.$set(row, 'START', status === 1 ? 0 : 1);
          this.$message.info('已取消操作');
        });
      } catch (error) {
        console.error('更新账号使用状态失败:', error);
        this.$message.error('操作失败，请重试');
        // 恢复原状态
        this.$set(row, 'START', row.START === 1 ? 0 : 1);
      }
    },
    
    // 查看账号订单
    viewAccountOrders(row) {
      this.currentAccountInfo = row
      this.orderDialogVisible = true
      this.orderQueryParams.page = 1
      this.orderQueryParams.name = ''
      this.orderQueryParams.date = null
      this.getOrderList()
    },
    // 获取订单列表
    async getOrderList() {
      try {
        // 参数验证
        if (!this.currentAccountInfo || !this.currentAccountInfo.SID) {
          this.$message.error('账号SID不能为空')
          return
        }
        
        this.orderLoading = true
        const params = {
          page: this.orderQueryParams.page,
          size: this.orderQueryParams.size,
          name: this.orderQueryParams.name || '',
          paymentMethod: this.orderQueryParams.paymentMethod || '',
          orderStatus: this.orderQueryParams.orderStatus || '',
          storeName: this.orderQueryParams.storeName || '',
          startTime: this.orderQueryParams.date && this.orderQueryParams.date[0] ? this.orderQueryParams.date[0] : '',
          endTime: this.orderQueryParams.date && this.orderQueryParams.date[1] ? this.orderQueryParams.date[1] : '',
          proxyId: this.currentAccountInfo.SID // 使用账号的SID作为代理ID
        }
        
        // 调用订单API
        const response = await getMcdOrder(params)
        
        if (response && response.code === 200) {
          this.orderList = response.data.list || []
          this.orderTotal = response.data.total || 0
          
          // 计算统计数据
          this.calculateOrderStatistics()
          
          // 如果订单列表为空，显示友好提示
          if (this.orderList.length === 0) {
            this.$message.info('该账号暂无订单记录')
          }
        } else {
          this.$message.error(response.message || '获取订单列表失败')
          this.orderList = []
          this.orderTotal = 0
        }
      } catch (error) {
        console.error('获取订单列表失败:', error)
        
        // 根据不同错误类型显示不同消息
        if (error.code === 203) {
          this.$message.error('登录已过期，请重新登录')
        } else if (error.code === 401) {
          this.$message.error('无权限访问该账号订单')
        } else {
          this.$message.error('获取订单列表失败，请稍后重试')
        }
        
        this.orderList = []
        this.orderTotal = 0
        // 重置统计数据
        this.calculateOrderStatistics()
      } finally {
        this.orderLoading = false
      }
    },
    // 计算订单统计
    calculateOrderStatistics() {
      const today = new Date().toISOString().split('T')[0]
      let todayCount = 0
      let totalAmount = 0
      let todayAmount = 0
      let validOrderCount = 0 // 有效订单数（排除退款）
      
      // 如果有订单数据，计算统计信息
      if (this.orderList && this.orderList.length > 0) {
        this.orderList.forEach(order => {
          // 获取订单状态
          const orderStatus = order.ORDER_STATUS || order.ODER_STATUS
          
          // 排除退款订单（状态为4）
          if (orderStatus === 4) {
            return
          }
          
          validOrderCount++
          
          // 处理不同的时间字段名
          const createTime = order.CREATRD_TIME || order.CREATED_TIME || order.CREATE_TIME
          const orderDate = createTime ? new Date(createTime).toISOString().split('T')[0] : ''
          const amount = parseFloat(order.PRICE_NUM || order.AMOUNT || 0)
          
          totalAmount += amount
          
          if (orderDate === today) {
            todayCount++
            todayAmount += amount
          }
        })
      }
      
      this.orderStatistics = {
        total: validOrderCount, // 使用有效订单数而不是总数
        todayCount,
        totalAmount: totalAmount.toFixed(2),
        todayAmount: todayAmount.toFixed(2)
      }
    },
    // 订单搜索
    searchOrders() {
      this.orderQueryParams.page = 1
      this.getOrderList()
    },
    // 重置订单搜索
    resetOrderSearch() {
      this.orderQueryParams.name = ''
      this.orderQueryParams.paymentMethod = ''
      this.orderQueryParams.orderStatus = ''
      this.orderQueryParams.storeName = ''
      this.orderQueryParams.date = null
      this.orderQueryParams.page = 1
      this.getOrderList()
    },
    // 订单分页处理
    handleOrderSizeChange(size) {
      this.orderQueryParams.size = size
      this.orderQueryParams.page = 1
      this.getOrderList()
    },
    handleOrderCurrentChange(page) {
      this.orderQueryParams.page = page
      this.getOrderList()
    },
    // 查看订单详情
    // 查看订单详情
    async viewOrderDetail(row) {
      this.orderDetailDialogVisible = true
      this.orderDetailLoading = true
      this.orderDetail = { ...row } // 先显示基本信息
      
      try {
        // 调用后端API获取详细信息
        const response = await axios.get(`/newmdl/getOrderDetail?oderId=${row.ORDER_ID || row.ODER_ID}&sid=${this.currentAccountInfo.SID}`)
        
        if (response.data && response.data.code === 200) {
          // 合并详细信息到当前订单对象
          this.orderDetail = { ...this.orderDetail, ...response.data.data }
        } else {
          this.$message.warning('获取订单详情失败：' + (response.data?.msg || '未知错误'))
        }
      } catch (error) {
        console.error('获取订单详情出错:', error)
        this.$message.error('获取订单详情出错：' + (error.message || '未知错误'))
      } finally {
        this.orderDetailLoading = false
      }
    },
    // 格式化订单时间
    formatOrderTime(timestamp) {
      if (!timestamp) return '-'
      return formatDate(timestamp)
    },
    // 获取订单状态类型
    getOrderStatusType(status) {
      const statusMap = {
        1: 'success', // 已支付
        2: 'warning', // 已出餐
        3: 'success', // 已取餐
        4: 'danger'   // 已退款
      }
      return statusMap[status] || 'info'
    },
    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        1: '已支付',
        2: '已出餐',
        3: '已取餐',
        4: '已退款'
      }
      return statusMap[status] || '未知状态'
    },
    
    // 导出订单数据
    exportOrders() {
      this.$confirm('确定要导出当前筛选条件下的订单数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.doExportOrders()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        })
      })
    },
    
    // 执行导出
    doExportOrders() {
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      try {
        this.exportToCSV(this.orderList)
        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      } finally {
        loading.close()
      }
    },
    
    // 导出为CSV格式
    exportToCSV(data) {
      const headers = [
        '订单号',
        '商品名称', 
        '门店名称',
        '金额',
        '支付方式',
        '订单状态',
        '取餐码',
        '创建时间'
      ]
      
      let csvContent = '\uFEFF' + headers.join(',') + '\n' // 添加BOM以支持中文
      
      data.forEach(order => {
        const row = [
          order.ORDER_ID || order.ODER_ID || '',
          order.MEAL_TITLE || '',
          order.STORE_NAME || '',
          order.PRICE_NUM || '',
          order.PAY_TEXT || '',
          this.getOrderStatusText(order.ORDER_STATUS || order.ODER_STATUS),
          order.PICKUP_CODE || '',
          this.formatOrderTime(order.CREATED_TIME || order.CREATRD_TIME)
        ]
        csvContent += row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',') + '\n'
      })
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `订单数据_${this.currentAccountInfo.SID}_${new Date().toISOString().slice(0, 10)}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    },
    // 获取支付方式类型（用于标签颜色）
    getPaymentMethodType(payText) {
      const typeMap = {
        'alipay': '',        // 支付宝 - 蓝色
        'wechat': 'success', // 微信 - 绿色
        'balance': 'warning' // 余额 - 橙色
      }
      return typeMap[payText] || 'info'
    },
    // 获取支付方式图标
    getPaymentMethodIcon(payText) {
      const iconMap = {
        'alipay': 'el-icon-wallet',
        'wechat': 'el-icon-chat-dot-round',
        'balance': 'el-icon-coin'
      }
      return iconMap[payText] || 'el-icon-money'
    },
    // 获取支付方式文本
    getPaymentMethodText(payText) {
      const textMap = {
        'alipay': '支付宝',
        'wechat': '微信支付',
        'balance': '余额支付'
      }
      return textMap[payText] || '其他支付'
    },
    // 重试获取订单详情
    async retryGetOrderDetail() {
      if (!this.orderDetail.ORDER_ID && !this.orderDetail.ODER_ID) {
        this.$message.warning('订单信息不完整，无法重新加载')
        return
      }
      
      this.orderDetailLoading = true
      
      try {
        const response = await axios.get(`/newmdl/getOrderDetail?oderId=${this.orderDetail.ORDER_ID || this.orderDetail.ODER_ID}&sid=${this.currentAccountInfo.SID}`)
        
        if (response.data && response.data.code === 200) {
          this.orderDetail = { ...this.orderDetail, ...response.data.data }
          this.$message.success('订单详情刷新成功')
        } else {
          this.$message.warning('获取订单详情失败：' + (response.data?.msg || '未知错误'))
        }
      } catch (error) {
        console.error('重新获取订单详情出错:', error)
        this.$message.error('获取订单详情出错：' + (error.message || '未知错误'))
      } finally {
        this.orderDetailLoading = false
      }
    }
  }
}
</script>

<style scoped>
.boxs {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题样式 - 参考卡券页面设计 */
.page-header {
  margin-bottom: 24px;
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* 核心统计卡片样式 - 参考卡券页面现代化设计 */
.core-statistics {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.stat-content {
  flex: 1;
  text-align: left;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
  color: #1e293b;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #fff;
  opacity: 0.9;
}

/* 表格容器样式 */
.modern-table {
  width: 100% !important;
  overflow-x: auto;
}

.modern-table .el-table__body-wrapper {
  overflow-x: auto;
}

/* 表格响应式处理 */
@media (max-width: 1200px) {
  .modern-table {
    font-size: 12px;
  }
  
  .modern-table .el-table__cell {
    padding: 8px 4px;
  }
}

/* 不同卡片类型的现代化配色 */
.total-card::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.total-card .stat-number {
  color: #3b82f6;
}

.total-card .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.active-card::before {
  background: linear-gradient(90deg, #10b981, #047857);
}

.active-card .stat-number {
  color: #10b981;
}

.active-card .stat-icon {
  background: linear-gradient(135deg, #10b981, #047857);
}

.inactive-card::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.inactive-card .stat-number {
  color: #ef4444;
}

.inactive-card .stat-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.using-card::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.using-card .stat-number {
  color: #f59e0b;
}

.using-card .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.idle-card::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.idle-card .stat-number {
  color: #8b5cf6;
}

.idle-card .stat-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.today-card::before {
  background: linear-gradient(90deg, #06b6d4, #0891b2);
}

.today-card .stat-number {
  color: #06b6d4;
}

.today-card .stat-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.search-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.action-left, .action-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 现代化表格样式 */
.modern-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

/* 账号信息样式 */
.account-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sid-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.account-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #374151;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sid-actions {
  display: flex;
  gap: 4px;
}

/* 登录状态标签 */
.login-status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

/* 分类标签 */
.category-tag {
  font-weight: 500;
  border-radius: 6px;
}

/* 日期文本 */
.date-text {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 手机号文本 */
.phone-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #374151;
}

/* MeddyID */
.meddy-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 点数和余额徽章 */
.points-badge, .balance-badge {
  font-weight: 600;
  color: #059669;
  background: #d1fae5;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid #a7f3d0;
}

.balance-badge {
  color: #dc2626;
  background: #fee2e2;
  border-color: #fca5a5;
}

/* 空文本 */
.empty-text {
  color: #9ca3af;
  font-style: italic;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

/* 订单对话框样式 */
.order-dialog-header {
  margin-bottom: 20px;
}

.account-info-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

/* 订单统计样式 */
.order-statistics {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.stat-item .stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state i {
  font-size: 64px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-state p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .core-statistics .el-col {
    margin-bottom: 8px;
  }
  
  .stat-card {
    padding: 15px;
    min-height: 100px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .boxs {
    padding: 12px;
  }
  
  .core-statistics {
    margin-bottom: 12px;
  }
  
  .core-statistics .el-col {
    margin-bottom: 10px;
  }
  
  .stat-card {
    padding: 12px 16px;
    min-height: 90px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-icon {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }
  
  .action-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .action-left, .action-right {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .core-statistics .el-col {
    margin-bottom: 8px;
  }
  
  .stat-card {
    padding: 8px;
    min-height: 70px;
  }
  
  .stat-number {
    font-size: 16px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .stat-icon {
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
}
</style>

