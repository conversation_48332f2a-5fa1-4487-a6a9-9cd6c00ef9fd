import {
	httpRequest
} from '../utils/request.js'
// 兑换码
export function checkCode(params) {
	return httpRequest({
		url: '/vft',
		method: 'POST',
		data:params
	})
}
// 获取城市信息
export function getNowCity(params) {
	return httpRequest({
		url: `/GetCitiesBasedOnLocation`,
		method: 'POST',
		data: params
	})
}
//搜索门店 storeIdGetShopsApi
export function storeIdGetShopsApi(params) {
	return httpRequest({
		url: `/storeIdGetShops`,
		method: 'POST',
		data: params
	})
}
// 附近餐厅信息
export function nearByRestaurant(params) {
	return httpRequest({
		url: `/getNearbyStoresBasedOnCoordinates`,
		method: 'POST',
		data: params
	})
}
// 根据城市code获取门店列表
export function allRestaturant(params) {
	return httpRequest({
		url: `/getCityByLocation`,
		method: 'POST',
		data: params
	})
}
// 获取套餐信息
export function packageDetail(params) {
	return httpRequest({
		url: `/getCashCouponList`,
		method: 'POST',
		data:params
	})
}
// 获取套餐商品详情
export function productDetail(params) {
	return httpRequest({
		url: `/getCashCouponListById`,
		method: 'POST',
		data: params
	})
}
// 获取定制信息
export function customerInfo(params) {
	return httpRequest({
		url: `/getCustomization`,
		method: 'POST',
		data:params
	})
}
// 立即兑换
export function exchangeGoods(params) {
	return httpRequest({
		url: `/addCashCouponOrder`,
		method: 'POST',
		data:params
	})
}
// 获取取餐方式
export function getOrderWay(params) {
	return httpRequest({
		url: `/getTakeA`,
		method: 'POST',
		data:params
	})
}
// 卡券下架接口
export function soldOutCoupon(params) {
	return httpRequest({
		url: `/soldOut`,
		method: 'POST',
		data: params
	})
}
// 如果存在这个文件，需要更新为：
const baseURL = process.env.VUE_APP_BASE_API || 'http://localhost:3000';
const frontendURL = process.env.VUE_APP_FRONTEND_URL || 'http://localhost:8080';
