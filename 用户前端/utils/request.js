// 尝试从配置文件加载，如果不存在则使用默认值
let baseUrl;
try {
    const config = require('./config.js');
    baseUrl = config.baseUrl;
} catch (error) {
    // 如果配置文件不存在，使用默认值
    baseUrl = 'http://localhost:3000/web';
    console.warn('配置文件不存在，使用默认API地址:', baseUrl);
}

// 日志记录函数
const logRequest = (url, method, data, type = 'REQUEST') => {
	const timestamp = new Date().toISOString()
	const logData = {
		timestamp,
		type,
		url,
		method,
		data: JSON.parse(JSON.stringify(data || {}))
	}
	console.log(`[${type}] ${timestamp} ${method} ${url}`, logData)
	// 存储到本地存储用于调试
	try {
		const logs = uni.getStorageSync('api_logs') || []
		logs.push(logData)
		// 只保留最近100条日志
		if (logs.length > 100) {
			logs.splice(0, logs.length - 100)
		}
		uni.setStorageSync('api_logs', logs)
	} catch (e) {
		console.error('保存日志失败:', e)
	}
}

const httpRequest=(options)=>{
	return new Promise((reslove,reject)=>{
		// 记录请求日志
		logRequest(options.url, options.method, options.data, 'REQUEST')
		
		uni.request({
			url:baseUrl+options.url,
			method: options.method,
			data:options.data||{},
			success: (res) => {
				// 记录响应日志
				logRequest(options.url, options.method, res.data, 'RESPONSE')
				// uni.hideLoading()
					reslove(res.data)
			},
			fail: (err) => {
				// 记录错误日志
				logRequest(options.url, options.method, err, 'ERROR')
				// uni.hideLoading()
				uni.showToast({
					title: "请求接口失败！",
					icon: 'none',
					duration: 2000
				})
				reject(err)
			}
		})
	})
}

export {httpRequest}