"""RSA Util"""
import base64
from binascii import hexlify

import Crypto.Random
from Crypto.Cipher import AES, PKCS1_v1_5 as pk_cipher
from Crypto.Hash import SHA1
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5 as pk_sign
from loguru import logger

PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAivhhvEb8lf3jlYybhvGBSGg9ASkxZvsTVRh2y+/9SSY7gsun75ZX2mJLKTBosRz7EukXSLgA/X2G7NkoyPhtBhcfSAnMwS7/J2VCl06SDm2wFZYmzrj8/2yo2LnaYe1UmQYFsh08QYFtYCn7x+Nr9l0zOHTtQj8qXn71swcPoazDD2i3GH1vdrZf0KZiNA95G6HypVMK6X/AI05dx0p6GAk+AU4O75n+l7+AGg5RBNaHGxnRMuBEwQ2Gnc8AtXdCIRZb40GP/3oTeMq3l3fuVdd/hT5+cocVuMeDKpB6mczB2IL6qLqTm1peU1g/GeriZrl3v07C0+LYM9iCGDKxcwIDAQAB"
PRIVATE_KEY = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDTCzHlY4sjWtczd+QK3PmWKQ59KuYedtx95XF4qtxukrIfHhXtFYeQEHSwoZLRYSxVsg6/wSQICZ5b67M+blyfACjEs8aD9N14kL6QFcF/5hiSvT5vcpIGFHMX20hP9Xs4309XP1oJXlzOy+xDHqRmayH0TwzHB/lbf51ooA+EfsNPR5mxdiptI7Nw0nQOkXjmgqz/x2jZ7ZVcoFfY9LxHEvkYlgrz9U8WXBM0N+ssT4V9wtQu1CEpMRCLjkuoQq3a87Fr4nCiEtBCYUFEbqrvcKEztRXaenZsHfuBy+z0hEOc1xvl4S/OXXbZATLLq4cPgp9sQ1CpEW3nn5o7h8nZAgMBAAECggEAAvMhG2esxi263ElMsqosAuFIBo/W744RvuC6GUmqExGR08WD4PBwGJ9iQiW/+5U0HW6DrkJw0nvv7rD4rGK2gLJr33PzzlxZzBmZ0EG9lqAMr+BdQwmC6PrYMPkrpZJ0ynK0lS+ZfpAHFAzdnIKNIbFVpCiGsTF3VsQN2yyBiwSEDee636zq4MH6b7ceEldKgDqdURgH1LVD0Z00Rg/W+LQ3xXz9mkE3FX7dnzxm7/SCAC9FbE/9wUIHpk26EtcCHyl6lnOMpH/eq70W+WL0WsDU9deFsNUSRCi1ij6JK0XHyQOidZXTPLsvMyyNWcnhhLrQfH68bGXJ3kL7jbmAAQKBgQDwzkLNuShUndBFrRJrMRTWyc9ke5iE3kzq84E9fEZ+VrYVD5X9XKt15Jklp1AwrAfrmOIRmFtKGbhP5ifHGLlYuNupYlY1bU9+dtRGgS8kD/HCpolYiiM/u1Fr2mJ45XrVAeNIBM6UnXfNXTTqdiA1v7RkAUqoX7+ovbfgnVQd2QKBgQDgXDAtZMyQCBmF4p3/g6x3xBL9ssIyB55IWS9YDUcbIcb1cd7FI1tcWXYKB08WEcMkUhO7/yjkIEzmj8aNWHTxCH5juZRPUJE+5nKCdHSzHKYTdq2MBues2DZJhLxXmxGFTNx/sgqND80WYQme4GEH6xno4s3rmk7mr9DcE0mMAQKBgEaO+W4UOegJUdSIsOPtvsBs47L6sCcGiB0z+oOMnY85UyL6rSVO+QKTV/h7fPi1/6Ad7Ofg76bXwHTQ6IjjCrQffmW0ZbRD8k4I/YwnICv97AXwDECAQXHULjhfBgmMnCA77F/W4L0VItxu2il1MVzubin7YufRHnSQGofbMaGJAoGAKjlvX7uuvBSqKQZjUelIFioyoMh5v8yrP1Z5kIq/LR4IQb0yfcuPsCuuCWkiuzaQkU9ZI3J8EaL6lhHM/mD9cTd2AfK1NNAm5b2dI2wjYmor31Hgi4/pHiB65w8yGxK2E25JuUaasb2djW6pztrPqBvY/nlg48xguQ2xsxl2KAECgYADQdhHLmY+KRuxKPgSOzpEt9/9/iww0VnqrKg4C6Q8lyLV+aRPPMB2CVl2blJHetx6/nlC2MUnyUxTpXe3CG/XGqVeo/mvlCvsPe695wWvFoFc+8N/BahmuJ9OTHkmdP3OyygTfBENJtLULMixQCsCTI6FEkls9nr76i2lo6AX8g=="

# deprecated
WEBANK_PUBLIC_KEY_N = "B5FF4B7F7B9435B1945B65F8B6AABCE0AF236B35B5106D570876621ED2B5F3D7ACED06D0695E3127E8EA27509922E5A9320B7D7A3A19C3C84048DD3C62A40233C521F302FB4578298DE7E382D4741505760E708AF36BB7F66BBB42532DBF4147389620CF07F34BC985F1342585B7683AFD3F0F846D5A0F82818BE4B2F90C449C2D488A32C8837994E7B0DF40077C9F90B12023799C8F29300FB3FEC89672A41D5B0500EA4B03A1857E1FFAA9FCCA86827EA6BCBF5D859A8B703B6AEBAE045800AEEF8E60DABB8F485A74E8BA2F0024802869BF5F0716ABB3C810E86AEA56CB11BE2F845C928776AF971766413CE30D65DFB0B478481F6F2A0CFA59B5670A6979"
# new key
WEBANK_PUBLIC_KEY_N = "C7F3A36CF69EC48E036D7D80A4EC3488FAA0EE17AE5E3DD9B2E6AC2042220C1BC0C5F5B0374BF84F33B75814E6B1A64D8F9537E1EFBCF296D6A29616BC8A6CF852743E38522C9634E6B4610FDAAA55B20AB1D27611EF9E615CDB9D28762F45680DA152C600357B0E5E9B9B786CCD76BB96EB8A7EE212C2BE4584A54331BDEC3F626FD2A728615CBC50E2E3D9A89101FEBFEEDFD9CDB135DEAAD51DF81375C886A729FB3C81FD13010A99AFEDA80A6A68E4D140C4E781F661865CC2FE1A56F70AD74DED61710E24AC1DC0EB506582A797995936991E97AC0A1BF8EC3CE2626947CC6D999EF81A0E02D0492DADA7F07A86751225EC25A92520493C84030E0AC429"

def generate_random_key(key_len: int = 16) -> bytes:
    """
    Generate random key for RSA encryption/decryption
    :param key_len: key length, default to 16
    :return: random bytes
    """
    return Crypto.Random.get_random_bytes(key_len)


def get_rsa_key(b64ed_key: str) -> RSA.RsaKey:
    """
    Get RsaKey from base64ed string
    :param b64ed_key: base64ed string
    :return: RsaKey
    """
    return RSA.importKey(base64.b64decode(b64ed_key))


def rsa_sign(content: str) -> str:
    """
    Sign the content
    :param content: content to be signed
    :return: signature with base64 format
    """
    content = content.encode('utf-8')
    content_hash = SHA1.new(content)
    pri_key = get_rsa_key(PRIVATE_KEY)
    signer = pk_sign.new(pri_key)
    signature = signer.sign(content_hash)

    return base64.b64encode(signature).decode()


def verify_rsa_sign(sign: str) -> bool:
    """
    Verify the signature of the content
    :param sign: signature
    :return: if the signature matches
    """
    pub_key = get_rsa_key(PUBLIC_KEY)
    verifier = pk_sign.new(pub_key)
    try:
        verifier.verify(SHA1.new(), base64.b64decode(sign.encode('utf-8')))
        logger.info("signature matches")
        return True
    except Exception:
        logger.error("signature doesn't match")

    return False


def rsa_encrypt(content: bytes) -> str:
    """
    RSA encryption with public key
    :param content: content to be encrypted
    :return: encrypted with base64 format
    """
    pub_key = get_rsa_key(PUBLIC_KEY)
    cipher = pk_cipher.new(pub_key)
    logger.info(f"origin: {hexlify(content)}")
    encrypted = cipher.encrypt(content)
    logger.info(f"encrypt: {hexlify(encrypted)}")
    return base64.b64encode(encrypted).decode()


def rsa_encrypt_webank(content: bytes) -> bytes:
    """
    RSA encryption with webank public key
    :param content: content to be encrypted
    :return: encrypted with bytes format
    :return:
    """
    pub_key = RSA.construct(rsa_components=(int(WEBANK_PUBLIC_KEY_N, 16), int('********', 16)))
    cipher = pk_cipher.new(pub_key)
    logger.info(f"origin: {content}")
    return cipher.encrypt(content)


def rsa_decrypt(encrypted: str) -> bytes:
    """
    RSA decryption with private key
    :param encrypted: encrypted content
    :return: decrypted
    """
    pri_key = get_rsa_key(PRIVATE_KEY)
    cipher = pk_cipher.new(pri_key)
    plaintext = cipher.decrypt(base64.b64decode(encrypted.encode('utf-8')), "Error while decrypting")
    return plaintext


def padding(text: str, key_len: int) -> str:
    """
    PKCS5Padding
    :param text: text
    :param key_len: key length
    :return: padded text
    """
    return text + (key_len - len(text) % key_len) * chr(key_len - len(text) % key_len)


def unpadding(text: bytes) -> bytes:
    """
    PKCSPadding unpadding
    :param text: text
    :return: unpadded text
    """
    return text[0:-ord(text[-1:])]


def aes_encrypt(key: bytes, content: str) -> str:
    """
    AES encryption
    :param key: encryption key
    :param content: content to be encrypted
    :return: encrypted with base64 format
    """
    cipher = AES.new(key, AES.MODE_ECB)
    ciphertext = cipher.encrypt(padding(content, len(key)).encode('utf-8'))
    return base64.b64encode(ciphertext).decode()


def aes_decrypt(key: bytes, b64_encrypted: str) -> str:
    """
    AES decryption
    :param key: decryption key
    :param b64_encrypted: encrypted with base64 format
    :return: decyprted
    """
    cipher = AES.new(key, AES.MODE_ECB)
    return unpadding(cipher.decrypt(base64.b64decode(b64_encrypted.encode('utf-8')))).decode()


if __name__ == '__main__':
    req = {
        "funcode": "A1.AC004",
        "cooperator": "C8888",
        "version": "1.0.0",
        "type": "app",
        "extend": {
            "userId": "MEDDY178821351473147131",
            "token": "aff4306d661259747dce0121062b36f5_",
            "pwdVersion": 1,
            "sdkVersion": 10
        },
        "encryptData": "T0f30F8HqNe5YE2W5Ozys82aKCwKomGpBQXPxvS2vAASLbKd1aSTGgtuhDraBItSz94Z1cbEeQRFKSXy3LeGoIn5NrAfh3CMWbgyhQ8ZhLBZa+EJ7C4p9umvA6T+X914f+Q65UjIqQmREV0pHi26EBE+znNFJe8qb77VZ6zpPdL5q4+Xn4XsoIdtYGaRkyP01e3ZcqoybV4fIdkHNm65ikbxoRzEY5wrsSYnM+z+w79Fm9ZPqiDJc7XVgAW9+32kVYGp1y0xXtTWIAMMsItXlvy6AQOV5qYbHG8x+BOYY2dNGnaQPugxyu+9wYBw80BuMk7612UdTJknAa3aBD1rgASf2GyyWRMoHJK3EHTbVagLrPpDzg5dQ9101mT4\/ZkA1WK4J7HdgFJLvslOGF28y5nnsY+KQMW5xpzOSXXJtdfBn44B+kIFsm3q8Qmx1Lm9mVqK0+MUsoXfnwY9ZjoZ8b5AB1nPb0a+HY\/ai+eATsPWjWEQUGiJd9YjIgooAqmuPHSwrmTJ0z9qPwMAloj\/NMTZai8Wf0U29Qnt3ung2QNuRB8rJlbzXgKtnLVRgCk2xW16sF4zy3LVUw0SnzVQm3SvJcD0sDRAoHJmtioFNkvGfCxZZDTf13dDIX0jZgiURJdP7tXfJayA1G93Ik+aBBu+Wp5OXXQn9Pgnf\/eB\/9EaBmRO+Jpt1tAUEEP5w7P4zHN2lZOeyB76uQbrpNFTpWTUX0dBCicwxtnXOTSrO4ByG5JsGFgewPg8AiOytrLP36tMoHttulAf3VypgDEVys6LkIJvZt4F\/yRARY82LEsSzEEizL002SCcUufre6Y1M8PF9TfXHZYKmwxNE5GnBrZHpmtOocEPnmXsgGd3p5DLKVHtogh4q5o\/unthTLQ2BWjPNxIK4muRDWizrD1ylu0s4fdSzcM+yMVuknTlj5Kzctwl+6LsZMpmCN41byRPTkdsT9jA0MEPjbXDa1K+MjVG5hiNKFZpN9ej\/C5hHVm1bpZiLvAQt2Rguxx72Lu6sp0GLW0E6YQFx\/LeMxsDUt0QZY4ipKBcJYPMTEUCfTOAqfQcnGPHXkTDSFc4k0ygNhXwNUzTZbgmG54OwnTE+9SBda3Y8cUvOAya+5Gva2hm1gjVy4FYRJZWCK483N+fJg9ZjYaLtabU4yel1aB2blLDEiVvtWz7zDqb9c\/5z1IqYGb3xaD0JyRHDMck0xAF7pXWItp+BojyrO4Rst+qQm\/zgs2AiSV\/X5cCSbcNhRX4LmpnMj2ad9eoIzLbP4MzOJle5EbUFY+knSSrMPyqyrXXKomOAM\/xghpZEsVL5xs3U7cyYDAD6atKGsTwDUvrS67amFZr9ZrqG8eo5xazBZfUgiKsW4dq3ZAPaAA526V5oZqCXONQxOzSs1yv7cZCgauBIjIPw4pDqu2CnP8uoaRgX\/uYxlKd5suk6lo4Jv1i7pykvLkeGuj+H91U+1AUQvRoNq9uOew3phW+60CpcKfh6WuQYHEDSAP4njCuZVI=",
        "encryptKey": "GToU13s96rFOYlyBG6BETOIlLvM2xO9alvYTidikAlEXLmaI4tD+B1v+nmCZFLVbOs2+fIVaJFzA0sYuOdR\/\/i\/8prTrvDC4Kx1HXHIsoO1flf\/a4o\/XZl6r6Tdrd00zYCC47uq\/TZZ\/xfbbPMa\/ehnCY4vKbMfGbvPwoVe0tGqo7lNHSfs7n2\/5zuTGl4TjGOTICX0Tw3HB8gPNy55VDRGjT9gWCaxYhr+vEIWmfOgfZoNmKKhnM\/mYBW5E\/TaO1T+MjphfwgNNsTZGVDKGZq6S3O+pxqZ7IsgN7tuzQmtyWVP9vW4+fyUP\/ObxHSyz33J4PWQK\/A\/ORB4oGbYQmQ==",
        "signature": "IybqhYcVSzr0KQGDSr5CqRj1F\/0wVl7qy8aR+06IL6GrjjCiBsx4Rrkt\/P7ju3O1FAfWEsNzDVBUpCBplITvF3Nabxyre+xWHoV63xz9U87hDDbIhr4ZXYpgLMynDtLeTjIzlVQkrDlNgzHPy7eWtAT45veGty6r6rWnqgPC72V4jtzS7p5d0GihNujy0e5YYB7U0hLH9Ss7eHObKUodHoI2Fs\/ml5HBHu9mwRoInfWn3Xqqb5KmsZuFuieGEje+48WVxpbem35fQ8pjXErs4NVYnOOGUDSR8wZpQjsL8eiqxpSahRGhifD2aq8TOCeHx4RRYKH3fZwQ2Y9hTEQZyg=="
    }

    resp = {
        "funcode": "A1.AC002",
        "signature": "EerGb7sRHRR4tvbCtKvRrFhLplRRaqAAJRc1AJWP2FmRYXOZCW9u2aJKY+zaYxVk8wZnNeudJFjtGhKgJRsnWVilWjat7YkZtLfqU5ATT++4luCWqYnizxLcs4/F6KlYC0O6ABN3WNyzrNJvjfjjNjr3b0UBRq9dXXVu5WOJb0jHzRoyEvrXywWNz04FucmaS8bjlGqnoUqQes/rmcEN4bjk0MeCIVafKZvDvsntgxNVa7m8AVCj3kX9gUljlYcVxLdDcfdcDHpnXgcRARRzXsQG1DgERHzOGevs1FCkauANO9bpWfTz7nipYS3sR/eKXsJfqS6F+udZF4RnM5ZdAg==",
        "encryptData": "bXeAMgv7H/Z3QrYHJgkp6idnGQIc39rNDkSLHxZNJL+hEN+WiTAYOdAmo5uZOJhHc8wsfIV2LBNPNdFNYBExi0oyTSlk72X9NBQ59JqwruP9FFS9rX9ckbsC04pjZBw35oKeZ28wyEQzdxIcJQs+ez4oQDU6PCb3aCSwqW09XhPAoO5JcGKGgJ+zeRHaDO+EokvksMZYvg3umPX/dPnvaCI4hBnh0uWOKN8sNRZtReZl561p9u9sx3ybLQFvZSW7j3YJy3UdKnEbxE6DdI2TcfNZuL8dZoh2X2kn0QJSZUE=",
        "encryptKey": "JM5mzFpXIGn7qytlwrM//VWArx4+xI4y1gMD7+uthHIvF1eXU6P5zRX7oTlz02kG+Ih9p7GBIODrtkgpt2kqs/D0ErMYbWXygC2z6ksOoq0YKfiWtxk6rpooT/8HxlJZ68OGStc8DIe9qyASonylaPGwL6U3F103rXV+yy5fKUpFaoEfT12Cz4mzR4Cp5LCLM8VvTkyOIChzvQLltrl2M+U9ZDH7yeLZ7fRej0CGHOJqkz0coFziE/0g9A1RkuzKfYXlRO3e5Ycyulvl2IcJalv3ASF2RdcVdHt7P9uekkZe7pZnUzfXewTWwy1Te5TD5SYQ7ZHSHIR80Qc61yLR3Q==",
        "cooperator": "C8888",
        "version": "1.0.0"
    }

    resp = {
        "funcode": "A2.YEC008",
        "signature": "HDXSPSNPaZ/El34mkg1BKkHPP0ohpGWWw7bc++uKw+qMFYlDAmN/tj32Ydmpgub0eglYyW/SVyZrdcJO/482QQvA0yeK1mwgjqIa0z525PIDe+B+bEY3QBLvKHg0VyfVnuHNdsVHXNdxs7REH9nv0tp45qOyh54FVqxNcMm79hOYT/6+vKfopEcSoFIYFLbTn3t59tqCRZoVpY2VB+h74Ah4gJjImmJjGlA//iio2kc2r8r4hY+EhMFDVAWFCMygpncEscYocTyeuKU0eFsPdZcqyZ3JDD1a7ZI1vj50V91XBmJt31MUSYM7FcJuBuWPuLnHmHknYUm8DRPXy0QZsg==",
        "encryptData": "WCO9mkgGh3pmAwVcXRQG8gooIOqVTvLsBwQga4ulRxsVaEHLYSkRo01aLmICEEageRPMBcXigWWsGdi0mqL5qltmcz3YH2OY5tHBltN3fWUyN1dPRJG8EDA4WOGdk2g51Bna7c3ULHm8tGvyhOaidnsX903GTAViuGWK2AXvpZoeclI0If4a/4BitiS38acXQXlBTl4zbA413FlrtP4WpeXBorkry0NOcd48pb8bWFXBvaFm5OchlP5tL3zzLBSNk87BjykR7B+RB4yb8stW08EKakHucEV20Nnhwl0FhMw=",
        "encryptKey": "H3iJhYe8R/XUCfHYulN0os22KM7YkGHddhORhksqXvjD4PP+/liN6dBVbcso3bi4MrZ2YFG09i5ezzHUq9tCFcrEySmZsKXRqkRiUMztVzLYURekSVq2I+wOS3oSPTTPjpY3bJz4v8N8YONTbdBcnOB6nHfZDhJdTZcvDjZBYSE3JZERmYWu4WgGKplr97S00A72I5Wo9A3/d0scjNt4bSnnjJ8bo781261Lls/ZVxZe2BoUgcWXLhIePBRZgWzWFF1KIRKdRmSsX5cNDI1WtcwSc0MZ1nDCfFNyr4MzkgAhgOB8MxG9ABNVQZaqkYZdgu50aqfSGHXz2MxiMraZXA==",
        "cooperator": "C8888",
        "version": "1.0.0"
    }

    resp = {
        "signature": "KPSC/S/twcAAE9oYM8eQU08jNXkXtR+/yCd+svpigWWqGMUxl59BYIRd099C4NZcF7fE5XDEhNc1WYG17ZMZVRGXsDTSbajSVX/MXQfBXHulZyuOnLEheaqzn6stdDrj04h06Ncvlpq4VeSC8iW118SJ85DRHJ5lV8C718jttv1mtwSPVbr7kpIfW7rI1rP1DeeiOY+3Z8kpPqS58vKEqRZc88M3AQ4WMKAMPtcDV0S0S7gkcfZKRIEBWG9HmgGyrsXYjmUvAfnFmxkUMYigotybcadfjKHMgYGxYOQzI0O7ZTfzX8HLEi4SOU5oNyC2cGKb4W3t6PfT46GWBcUr5w==",
        "encryptData": "zWAF215URq1kK83IiiXVGb3LDKOqlqJXY9ttYp+ugRkM/jHgCJEREOln8XWtnwYVTLd6KCCd3T32Vlvq32iCGLtZikU+jmng2LKto+3AOTAsPOThH2gcGXlbr/1gCJzZhEyCO3kZTP677gOI6MttJg==",
        "encryptKey": "hlJfRjORPm5psVf4DffuXHo1HX4faiaWIUmsr+RJfFwzOxHlxD/dnYp6DDuMUmEKMKvRbTFMkjKeljSo4qCD2zpXx05mvmdipfngeMKJiMSTUJonPLv4iNjo/73ClYlcfzPr3t6W5bQId7xuKWmIRYVVWdZVeOY/9tqL6Lv2Eub1DFu97Igu3qwycXLsidD7qo1VVdWkbnPJi8qhokzrqeXkfPXascVM59bbo1WZbgPTtC/0GB/mSP0QnyioDi7lL3gv9KzZ0ab9rGDp3RVbREjRZ3l/1Grs/QGGZg+aAq8W1wKoGy5qLB1zAUTCGa/0vUZUd/PCfBLJnCMptUoZAw=="
    }

    resp = {
        "funcode": "A2.YEC011",
        "signature": "SDOAoedtqMlk17N+eAC+BCEv0R98f7HHA9mhRlPVqF0gzzh18ZxJXnw6v6t6jYj86j1oMQa9klP9FmNdcJIm4idqECmbvzvvuzSRp8n8DK9wftDHdnm5uvRfdR5LIJFWakdFhuCy+cUfn65UHQya2MV1kRGt+nJnpCRWamR/NSCTeycSHZHMnOHcrkdIZ4QwfA1mnIo4mg+AZpg6iEBs5Wg+eqTYmS6kQlCrutGlNK0uZG85CR9HfJ6GXNBPcoVEBifwYyawFBJXb3/2sT3b828XjFvDOxb+gmmmpNoCJw+qKbNFcFWwkwzIPRCz5IFzLRV7UDwLYG9Bf81pKitTmA==",
        "encryptData": "EMlvOc3OwoxagtsUMIu8ELuvQXHvPt939smZnHs2fQ6HqZjyMC/ehN/+0kNN376eUUQ+veDxtBJtK+oJ5D6Doyhmt0SRtILrXAfqm7C/R4qJbdl3kBo03cFc68T2GhMbgyKTdMdTMKt/Gox7vOzbZJ+KxTgswZqIDsyjk187z8DegkMnK5p1wnXGg/oKH3vcSCBaR7MRpMprKcfZruZlGg==",
        "encryptKey": "FXebFtdZREJAeMHeXvrInayWUkWRLS7LaP4EwCripR9a64x4BUWf4biHvOuQwnusYZ6RsMPQ/7D4jVPXKcuzgElx8c4IGcRjc7gROhQFlR6HEUvd8S6HZ38TH8BvOFQqIBAFwEXFceYaYBIFFe9fbnXcl2qsUs2CUnkUfG0VBeN9sYMRySvza/Ea9SEVXc9CN6ajtpHcyyP1eRfLENYpMcEKm3R02BUcUBPOL9IdlT5AaBsLkg+oCsN0qFpsCtxhpxW4r+ElkMJHKfG7N+1qd7f518QI8uAY+gYrOs86SFVvkZFZo3jZjOuiJMlk653m5hbfA4LBl+QiiMNyF0kXJg==",
        "cooperator": "C8888",
        "version": "1.0.0"
    }

    resp = {
        "funcode": "A2.YEC008",
        "signature": "g/vL4foz+0zJz/G4Z8zUTKjV40rvkQPnYMN5BLbFs75lFz6PdRhrBKP6moBy2S9IC1+dfCAModFbOOZMN8p03uwDxmTEtn0BgWpajZaSLl8fOdnq0eb13msVhe/BNDG08rdM3yMRBzk9ZkGLavWl2f32NzsFw26ds9q8gQiI3fE5IF3VbzWLO8VymXwQfQIU6KEDtxgqcYw9eWMQBvnbinsU0EGv2zwPALrXUIna0t1V3NU3b84x7fXaCZOEaXJlMOVz7cFdSH0znp8fLemc9/YYuUXs4mCQQqd1KgyOQkdtYKH37en4xH/OnERJ+O+v7aGQ/Qb7sRKsIVZ7/Zu0qQ==",
        "encryptData": "Ex+zIvBahwf8bqnA+OFUDp3/S1eyb/sIWDdcOu2KCR6cYkZJi8tnheoJJaYzMyHoGxl1CH8bHEtjyOENKG+zY1ar1qQkgo+VJn5gklI8nszXC/dwf4cwMbnWoDryDO0ObCfq7JKzYe1hJXtULGH9S2Ydz/vQNhS+XqzYpDhxER/o5fkqyk2qJ27ujVDXtSUdrh5GwDrmaWRXzlJyGgMNuOnZFj5QSHh8tPbw2qUJiYlNKd5vi3DOTdcPDL0bbvAxqe1fnVI8A1bAW9fxCQYfpepUmMrhcRTzk2+rbbHa2AE=",
        "encryptKey": "rpfrR+NnEa0yCR659WsHE9uyhxH/AOfreWAC7Xa5vjCt+B4f6jLXcmVmUpjej2Dby5Cnt63z7ZowQPP3I9mBo2H1y1969OzXZMwNEhfyyXKYIpkB8wI3ulXFta3MYLaSWOo/oi68hHtL/Pt4iWhVV4RlEiiXesc3+euogsLhponcIfG6wkO+4Sk5ClGX3CIGbHPLt7WQ3yKG5ybD2zwDL1fXDaYq1h9o1rF3wcFSYvTxW/rD+7v++D29Q8GD0RX5RmPQrwiRDLoQwmWojItozia020pIS6/WWZVvzjxdgybfKX0bF3yRJeA8zaUZdkkIHq6bsdpMGjFZBV5PZJFKuA==",
        "cooperator": "C8888",
        "version": "1.0.0"
    }

    PAYLOAD = '{"userId":"MEDDY920168127384188971","memberId":"MEDDY920168127384188971","token":"f647a76861e45ca94f30ebcf63ce4b60_","lang":"zh"}'
    recovered_key = generate_random_key()
    logger.info(f"key: {hexlify(recovered_key)}")

    # encrypt_key = rsa_encrypt(recovered_key)
    # logger.info(f"encrypt_key: {encrypt_key}")
    #
    # signature = rsa_sign(PAYLOAD)
    # logger.info(f"signature: {signature}")
    # verify_rsa_sign(signature)
    #
    # encrypt_data = aes_encrypt(recovered_key, PAYLOAD)
    # logger.info(f"encrypt_data: {encrypt_data}")
    # plain_data = aes_decrypt(recovered_key, encrypt_data)
    # logger.info(f"plain_data: {plain_data}")

    recovered_key = rsa_decrypt(resp['encryptKey'])
    logger.info(f"recovered_key: {len(recovered_key)} {hexlify(recovered_key)}")

    plain_data = aes_decrypt(recovered_key, resp['encryptData'])
    logger.info(f"plain_data: {plain_data}")

    verify_rsa_sign(resp['signature'])

    # password = '111222'
    # res = rsa_encrypt_webank(password.encode('utf-8'))
    # logger.info(f"password encrypted: {hexlify(res)}")

    pub_key = RSA.construct(rsa_components=(int(WEBANK_PUBLIC_KEY_N, 16), int('********', 16)))
    cipher = pk_cipher.new(pub_key)
    logger.info(hex(pub_key.n))
    logger.info(hex(pub_key.e))
