import asyncio
import json
import time
from hashlib import md5

import aiohttp
from loguru import logger


def generate_sign(json_data):
    secret_key = '7Wf019BZQPLH5EVM9tmqU0adxmZ5mBou'
    params = []
    for (k, v) in sorted(json_data.items()):
        params.append(f"{k}={v}")
    param_str = '&'.join(params)
    raw = f'{param_str}{secret_key}'
    return md5(raw.encode('utf-8')).hexdigest()


class iCoke:
    """
    iCode class encapsulates async APIs for accessing https://coke-iums.icoke.cn.
    """
    def __init__(self):
        self.session = None
        self.verify_ssl = False
        self.session = aiohttp.ClientSession(raise_for_status=True)
        self.req_timeout = 30

    async def close(self):
        """
        Close session
        :return:
        """
        await self.session.close()


    async def __base_get_request(self, url, params):
        """
        Base get request
        :param url: url
        :param params: params
        :return:
        """
        raise Exception("unimplemented __base_get_request")

    async def __base_post_request(self, url, json_data):
        """
        Base post request
        :param url: url
        :param json_data: post body in json format
        :return:
        """
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7', 'Cache-Control': 'no-cache',
            'Connection': 'keep-alive', 'Content-Type': 'application/json',
            'Origin': 'https://coke-iums.icoke.cn', 'Pragma': 'no-cache',
            'Referer': 'https://coke-iums.icoke.cn/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Linux; U; Android 8.1.0; zh-cn; ALP-AL00 Build/HUAWEIALP-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/8.8 Mobile Safari/537.36',
            'sec-ch-ua': '""', 'sec-ch-ua-mobile': '?1', 'sec-ch-ua-platform': '""',
        }

        json_data['sign'] = generate_sign(json_data)
        logger.debug(json.dumps(json_data, indent=4, ensure_ascii=False))
        post_content = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
        try:
            async with self.session.post(url, headers=headers, data=post_content, ssl=self.verify_ssl,
                                         timeout=self.req_timeout) as resp:
                res = await resp.json()
                logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def send_sms(self, mobile):
        """
        Send SMS
        :param mobile:
        :return:
        """
        url = 'https://coke-iu.icoke.cn/zex-cola-iu/external/sms/vf'
        json_data = {
            'openId': mobile,
            'mobile': mobile,
            'timestamp': int(time.time()*1000),
        }

        return await self.__base_post_request(url, json_data)

    async def login_with_sms(self, mobile, sms_code):
        """
        Login with SMS code
        :param mobile: mobile
        :param sms_code: sms code
        :return:
        """
        url = 'https://coke-iu.icoke.cn/zex-cola-iu/mc/coupon/login'
        json_data = {
            'code': sms_code,
            'mobile': mobile,
            'timestamp': int(time.time()*1000),
        }

        return await self.__base_post_request(url, json_data)

    async def receive_coupon(self, token):
        """
        Receive coupon
        :param token: user token (userValue)
        :return:
        """
        url = 'https://coke-iu.icoke.cn/zex-cola-iu/mc/coupon/receive'
        json_data = {
            'userValue': token,
            'timestamp': int(time.time()*1000),
        }

        return await self.__base_post_request(url, json_data)


async def main():
    mobile = '18610288148'
    client = iCoke()
    try:
        await client.send_sms(mobile)
        sms_code = input("input SMS code:")
        res = await client.login_with_sms(mobile, sms_code)
        token = res['data']['userValue']
        await client.receive_coupon(token)
    finally:
        await client.close()


if __name__ == '__main__':
    asyncio.run(main())