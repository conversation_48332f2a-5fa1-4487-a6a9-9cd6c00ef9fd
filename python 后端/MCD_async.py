"""
Macdonald module
"""
import json
import random
import re
import time
import urllib.parse
import uuid
from binascii import hexlify

import aiohttp
import requests
from dateutil import parser
from loguru import logger

import mcd_exceptions
from rsa_util import generate_random_key, aes_encrypt, aes_decrypt, rsa_encrypt, rsa_encrypt_webank, rsa_decrypt, \
    rsa_sign, verify_rsa_sign
from util import Util

LOG_RESP_URL_BLACKLIST = [
    'https://api.mcd.cn/bff/store/stores/vicinity',
    'https://api.mcd.cn/bff/store/stores',
    'https://api.mcd.cn/bff/store/cities/group',
    'https://api.mcd.cn/bff/spc/menu',
]

class MCDAsync:
    """
    MCDAsync class encapsulates async APIs for accessing MCD.
    """
    def __init__(self):
        self.session = None
        self.verify_ssl = False
        self.req_timeout = 30

    def __del__(self):
        pass

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.req_timeout))
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()

    @staticmethod
    def _get_common_headers(app_version='********'):
        """
        Get common headers for request from app
        :return: app request headers
        """
        ts = int(time.time() * 1000)
        st = Util.make_st(ts)
        nonce = Util.make_nonce(ts)

        return {
            'User-Agent': f'mcdonald_Android/{app_version} (Android)',
            'ct': '102',
            'v': app_version,
            'p': '102',
            'language': 'cn',
            'traceid': str(uuid.uuid4()),
            'st': st,
            'nonce': nonce,
            'sv': 'v3',
            'tid': '00003TuN',
            'device-id': 'BQuLoOFbMfVK30is464W4mep0iXmkEqlh0b7zcnqGLuxpellGcpLVVvEHvDnUxWEQst3PoCbGho3B3FxlLfk2tg==',
            'Content-Type': 'application/json; charset=UTF-8',
            'Host': 'api.mcd.cn',
        }

    @staticmethod
    def _get_web_common_headers():
        """
        Get common headers for request from web
        :return: web common headers
        """
        ts = int(time.time() * 1000)
        st = Util.make_st(ts)
        nonce = Util.make_nonce(ts)

        return {
            'Host': 'api.mcd.cn',
            'sv': 'v3',
            'nonce': nonce,
            'User-Agent': 'Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P Build/OPM7.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.91 Mobile Safari/537.36mcd-android',
            'st': st,
            'ct': '102',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://m.mcd.cn',
            'X-Requested-With': 'com.mcdonalds.gma.cn',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://m.mcd.cn/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/json; charset=UTF-8',
        }

    @staticmethod
    def _get_wbapi_headers():
        """
        Get wbapi headers
        :return: wbapi headers
        """
        return {
            'Accept-Charset': 'UTF-8',
            'Content-Type': 'application/json',
            'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 8.1.0; Nexus 6P Build/OPM7.181205.001)',
            'Host': 'wbapi.archcard.mcdonalds.com.cn',
        }

    async def _base_get_request(self, url, extra_headers, params, path='', app_version='********'):
        """
        Base get request
        :param url: URL that the request being sent to
        :param extra_headers: extra headers
        :param params: get params
        :param path: request path, used for generating sign
        :param app_version: app version
        :return: response in json format
        """
        headers = MCDAsync._get_common_headers(app_version)
        headers.update(extra_headers)
        sign = Util.generate_adaptive_sign(headers, params, None, path)
        headers['sign'] = sign

        logger.debug(url)
        if 'sid' in extra_headers:
            logger.debug(f'sid: {extra_headers["sid"]}')
        logger.debug(json.dumps(params, indent=4, ensure_ascii=False))
        try:
            async with self.session.get(url, headers=headers, params=params, ssl=self.verify_ssl, timeout=self.req_timeout) as resp:
                resp.raise_for_status()
                res = await resp.json()
                if url not in LOG_RESP_URL_BLACKLIST:
                    logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def _base_web_get_request(self, url, extra_headers, params, path=''):
        """
        Base web get request
        :param url: URL that the request being sent to
        :param extra_headers: extra headers
        :param params: get params
        :param path: request path, used for generating sign
        :return: response in json format
        """
        headers = MCDAsync._get_web_common_headers()
        headers.update(extra_headers)
        sign = Util.generate_adaptive_sign(headers, params, None, path)
        headers['sign'] = sign

        logger.debug(url)
        if 'sid' in extra_headers:
            logger.debug(f'sid: {extra_headers["sid"]}')
        logger.debug(json.dumps(params, indent=4, ensure_ascii=False))
        try:
            async with self.session.get(url, headers=headers, params=params, ssl=self.verify_ssl) as resp:
                resp.raise_for_status()
                res = await resp.json()
                logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def _base_post_request(self, url, extra_headers, json_data, path='', app_version='********'):
        """
        Base post request
        :param url: URL that the request being sent to
        :param extra_headers: extra headers
        :param json_data: post body in json format
        :param path: request path, used for generating sign
        :param app_version: app version
        :return: response in json format
        """
        headers = MCDAsync._get_common_headers(app_version)
        headers.update(extra_headers)
        sign = Util.generate_adaptive_sign(headers, None, json_data, path)
        headers['sign'] = sign

        logger.debug(url)
        if 'sid' in extra_headers:
            logger.debug(f'sid: {extra_headers["sid"]}')

        post_content = ''
        if json_data:
            logger.debug(json.dumps(json_data, indent=4, ensure_ascii=False))
            post_content = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
        try:
            async with self.session.post(url, headers=headers, data=post_content, ssl=self.verify_ssl) as resp:
                resp.raise_for_status()
                res = await resp.json()
                if url not in LOG_RESP_URL_BLACKLIST:
                    logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def _base_web_post_request(self, url, extra_headers, json_data, path=''):
        """
        Base web post request
        :param url: URL that the request being sent to
        :param extra_headers: extra headers
        :param json_data: post body in json format
        :param path: request path, used for generating sign
        :return: response in json format
        """
        headers = MCDAsync._get_web_common_headers()
        headers.update(extra_headers)
        sign = Util.generate_adaptive_sign(headers, None, json_data, path)
        headers['sign'] = sign

        logger.debug(url)
        if 'sid' in extra_headers:
            logger.debug(f'sid: {extra_headers["sid"]}')

        post_content = ''
        if json_data:
            logger.debug(json.dumps(json_data, indent=4, ensure_ascii=False))
            post_content = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
        try:
            async with self.session.post(url, headers=headers, data=post_content, ssl=self.verify_ssl) as resp:
                resp.raise_for_status()
                res = await resp.json()
                if url not in LOG_RESP_URL_BLACKLIST:
                    logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def _base_put_request(self, url, extra_headers, json_data, path='', app_version='********'):
        """
        Base put request
        :param url: URL that the request being sent to
        :param extra_headers: extra headers
        :param json_data: post body in json format
        :param path: request path, used for generating sign
        :return: response in json format
        """
        headers = MCDAsync._get_common_headers(app_version)
        headers.update(extra_headers)
        sign = Util.generate_adaptive_sign(headers, None, json_data, path)
        headers['sign'] = sign

        logger.debug(url)
        if 'sid' in extra_headers:
            logger.debug(f'sid: {extra_headers["sid"]}')

        post_content = ''
        if json_data:
            logger.debug(json.dumps(json_data, indent=4, ensure_ascii=False))
            post_content = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
        try:
            async with self.session.put(url, headers=headers, data=post_content, ssl=self.verify_ssl) as resp:
                resp.raise_for_status()
                res = await resp.json()
                if url not in LOG_RESP_URL_BLACKLIST:
                    logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def _base_wbapi_post_request(self, url, json_data):
        """
        Base wbapi post request
        :param url: URL that the request being sent to
        :param json_data: post body in json format
        :return: response in json format
        """
        headers = MCDAsync._get_wbapi_headers()
        logger.debug(url)
        logger.debug(json.dumps(json_data, indent=4, ensure_ascii=False))
        post_content = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
        logger.info(f"post body: {post_content}")
        try:
            async with self.session.post(url, headers=headers, data=post_content, ssl=self.verify_ssl) as resp:
                resp.raise_for_status()
                text = await resp.text()
                logger.debug(text)
                res = json.loads(text)
                if url not in LOG_RESP_URL_BLACKLIST:
                    logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                return res
        except Exception as e:
            logger.exception(getattr(e, "__dict__", {}))
            return None

    async def send_verify_code(self, mobile, verify_code_type=1, region_code=''):
        """'''
        Send verify code
        :param mobile: the mobile number the code sent to
        :param verify_code_type: 1 for login
        :param region_code: mobile region code
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/passport/verifyCode/send'

        json_data = {
            'tel': Util.aes_ecb(mobile),
            'type': verify_code_type,
        }

        if region_code != '':
            json_data.update({
                'regionCode': region_code
            })

        extra_headers = {
            'biz_scenario': '500',
            'biz_from': '1011'
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def login(self, mobile, sms_code, region_code):
        """
        Login with sms code
        :param mobile: the mobile to login
        :param sms_code: sms code
        :param region_code: region code
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/passport/login/mobile'

        json_data = {
            'citicRegister': True,
            'code': Util.aes_ecb(sms_code),
            'deviceInfoId': Util.generate_random_imei(),
            'tel': Util.aes_ecb(mobile),
        }

        if region_code != '':
            json_data.update({
                'citicRegister': False,
                'regionCode': region_code
            })

        extra_headers = {
            'biz_scenario': '500',
            'biz_from': '1004'
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def get_access_token(self, sid):
        """
        Get access token from sid.
        Actually the returned access token is the sid passed in.
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/login/token'

        extra_headers = {
            'sid': sid
        }

        return await self._base_get_request(url, extra_headers, None)

    async def login_refresh(self, sid):
        """
        Login refresh
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/passport/login/refresh'

        extra_headers = {
            'biz_from': '1020',
            'biz_scenario': '500',
            'sid': sid
        }

        return await self._base_post_request(url, extra_headers, {})

    async def logout(self, sid):
        """
        Logout
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/passport/login/logout'

        extra_headers = {
            'biz_from': '1008',
            'biz_scenario': '500',
            'sid': sid
        }

        return await self._base_post_request(url, extra_headers, {})

    async def change_mobile(self, sid, old_mobile, sms_code, new_mobile):
        """
        Change mobile number
        :param sid: sid
        :param old_mobile: the old mobile number
        :param sms_code: sms code
        :param new_mobile: the new mobile number
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/user/mobile/change'

        json_data = {
            'code': Util.aes_ecb(sms_code),
            'oldTel': Util.aes_ecb(old_mobile),
            'newTel': Util.aes_ecb(new_mobile),
        }

        extra_headers = {
            'biz_scenario': '201',
            'biz_from': '1018',
            'sid': sid,
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def edit_profile(self, sid, birthday):
        """
        Edit user profile
        :param sid: sid
        :param birthday: birthday
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/user/edit'

        json_data = {
            'birthday': birthday,
        }

        extra_headers = {
            'biz_scenario': '201',
            'biz_from': '1030',
            'sid': sid,
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def get_user_portal(self, sid):
        """
        Get user portal
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/user/portal'

        extra_headers = {
            'sid': sid
        }

        return await self._base_get_request(url, extra_headers, None)

    async def get_user_info(self, sid):
        """
        Get user info
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/user/portal/info'

        extra_headers = {
            'sid': sid
        }

        return await self._base_get_request(url, extra_headers, None)

    async def get_user_coupons(self, sid):
        """
        Get user coupons
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/promotion/coupons/rightCards'

        extra_headers = {
            'biz_from': '1026',
            'biz_scenario': '201',
            'sid': sid
        }

        params = {
            'scene': '3',
        }

        return await self._base_get_request(url, extra_headers, params)

    async def get_available_coupons(self, sid):
        """
        Get available coupons
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/promotion/coupons/v2'

        extra_headers = {
            'sid': sid,
        }

        params = {}

        return await self._base_get_request(url, extra_headers, params, app_version='6.0.47.0')

    async def get_coupon_card(self, sid, store_code, be_type=1, be_code='', order_type=1):
        """
        Get coupon card
        :param sid: sid
        :param store_code: store code
        :param be_type: business type? 到店或外卖
        :param be_code: business code
        :param order_type: order type
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/promotion/coupons/card'

        extra_headers = {
            'sid': sid
        }

        params = {
            'beCode': be_code,
            'beType': be_type,
            'cityCode': '',
            'orderType': order_type,
            'storeCode': store_code,
            # 'daypartCode': '5',
            'themeId': '',
            'uniCode': '',
            'scene': '2',
            'date': '',
            'time': '',
        }

        return await self._base_get_request(url, extra_headers, params)

    async def get_all_cities(self):
        """
        Get all cities
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/store/cities/group'

        extra_headers = {
            'biz_from': '1007',
            'biz_scenario': '103',
        }

        return await self._base_get_request(url, extra_headers, None)

    async def get_city_by_coordinate(self, lat, lng):
        """
        Get current city according to the coordinate
        :param lat: latitude
        :param lng: longitude
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/store/cities'

        extra_headers = {
            'biz_from': '1002',
            'biz_scenario': '100'
        }

        params = {
            'latitude': lat,
            'longitude': lng,
        }

        return await self._base_get_request(url, extra_headers, params)

    async def get_nearby_stores(self, lat, lng, show_type, be_type, order_type):
        """
        Get nearby stores according to the coordinate the other params
        :param lat: latitude
        :param lng: longitude
        :param show_type: show type
        :param be_type: business type?
        :param order_type: order type
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/store/stores/vicinity'

        extra_headers = {
            'biz_from': '1012',
            'biz_scenario': '103',
        }

        params = {
            'showType': show_type,
            'beType': be_type,
            'orderType': order_type,
            'latitude': lat,
            'longitude': lng,
            'keyword': '',
        }

        return await self._base_get_request(url, extra_headers, params)
    async def cityAcquisitionStore(self, city_code, page_no=1, page_size=10):
        """
        Search stores by keyword in a city
        :param city_code: city code
        :param keyword: search keyword
        :param page_no: page number
        :param page_size: page size
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/store/stores'

        extra_headers = {
            'biz_from': '1013',
            'biz_scenario': '103',
        }

        params = {
            'pageNo': page_no,
            'pageSize': page_size,
            'cityCode': city_code
        }

        return await self._base_get_request(url, extra_headers, params)
    async def search_stores_by_keyword(self, city_code, keyword, page_no=1, page_size=10):
        """
        Search stores by keyword in a city
        :param city_code: city code
        :param keyword: search keyword
        :param page_no: page number
        :param page_size: page size
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/store/stores'

        extra_headers = {
            'biz_from': '1013',
            'biz_scenario': '103',
        }

        params = {
            'pageNo': page_no,
            'pageSize': page_size,
            'cityCode': city_code,
            'keyword': keyword,
            'hotTagCode': '',
            'beType': '1',
        }

        return await self._base_get_request(url, extra_headers, params)

    async def get_store_info(self, store_code):
        """
        Get store detail information
        :param store_code: store code
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/store/stores/{store_code}'

        extra_headers = {
            'biz_scenario': '101',
            'biz_from': '1004',
        }

        return await self._base_get_request(url, extra_headers, None, store_code)

    async def get_store_menu(self, store_code, order_type, be_type, be_code, day_part_code):
        """
        Get store menu
        :param store_code: store code
        :param order_type: order type
        :param be_type: business type
        :param be_code: business code
        :param day_part_code: day part code
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/spc/menu'

        extra_headers = {
            'biz_scenario': '101',
            'biz_from': '1006',
        }

        params = {
            'storeCode': store_code,
            'orderType': str(order_type),
            'beCode': be_code,
            'beType': str(be_type),
            'dayPartCode': str(day_part_code),
        }

        return await self._base_get_request(url, extra_headers, params, app_version='********')

    async def get_products_detail(self, store_code, product_code, channel_code, order_type, be_code, day_part_code):
        """
        Get product detail
        :param store_code: store code
        :param product_code: product code
        :param channel_code: channel code
        :param order_type: order type
        :param be_code: business code
        :param day_part_code: day part code
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/spc/products/detail/{product_code}'

        extra_headers = {
            'biz_scenario': '101',
            'biz_from': '1001',
        }

        json_data = {
            'beCode': be_code,
            'cardId': '',
            'cartType': '1',
            'channelCode': channel_code,
            'couponCode': '',
            'couponId': '',
            'daypartCode': str(day_part_code),
            'hasCard': False,
            'orderType': order_type,
            'pageSource': 1,
            'productCode': product_code,
            'productPromotions': [],
            'storeCode': store_code,
            'abtestIds': [
                '562d8ad1-848b-4297-a1ed-38f0ee038433',
                '01a8ee73-95e2-417e-a87e-b19379a19005',
            ],
        }

        return await self._base_post_request(url, extra_headers, json_data, product_code)

    async def get_coupon_product(self, sid, coupon_id, coupon_code, promotion_id, store_code, order_type=1, be_code='', daypartCodes=None, date=None, time=None):
        """
        Get product the coupon can be used on
        :param sid: sid
        :param coupon_id: coupon id
        :param coupon_code: coupon code
        :param promotion_id: promotion id
        :param store_code: store code
        :param order_type: order type
        :param be_code: business code
        :param daypartCodes: day part codes
        :param date: date
        :param time: time
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/promotion/coupons/products'

        extra_headers = {
            'biz_scenario': '101',
            'biz_from': '1012',
            'sid': sid,
        }

        params = {
            'storeCode': store_code,
            'channelCode': '03',
            'orderType': order_type,
            'beCode': be_code,
            'couponCode': coupon_code,
            'couponId': coupon_id,
            'promotionId': promotion_id,
        }
        
        # 添加可选参数
        if daypartCodes:
            params['daypartCodes'] = daypartCodes
        if date:
            params['date'] = date
        if time:
            params['time'] = time

        return await self._base_get_request(url, extra_headers, params)

    async def get_coupon_detail(self, sid, coupon_id, coupon_code):
        """
        Get coupon detail
        :param sid: sid
        :param coupon_id: coupon id
        :param coupon_code: coupon code
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/promotion/coupons/{coupon_code}'

        extra_headers = {
            'biz_scenario': '201',
            'biz_from': '1012',
            'sid': sid,
        }

        params = {
            'couponId': coupon_id,
        }

        return await self._base_get_request(url, extra_headers, params, path=coupon_code)

    async def add_to_cart(self, sid, store_code, product_code, cart_type, channel_code, be_type, be_code, day_part_code,
                    coupon_id='', coupon_code='', promotion_id='', order_type=1, quantity=1, customization=None, combo_items=None, sub_product_code='', membership_code='', product_type=1, card_type=0, card_id=''):
        """
        Add goods to cart
        :param sid: sid
        :param store_code: store code
        :param product_code: product code
        :param cart_type: cart type
        :param channel_code: channel code
        :param be_type: business type
        :param be_code: business code
        :param day_part_code: day part code
        :param coupon_id: coupon id
        :param coupon_code: coupon code
        :param promotion_id: promotion id
        :param order_type: order type
        :param quantity: quantity
        :param combo_items: comboItems indicates that the product can be customized
        :param sub_product_code: sub product code
        :param membership_code: membership code
        :param product_type: product type 1: normal, 0: membership/card
        :param card_type: card type 0: normal, 2: breakfast card
        :param card_id: card id like breakfast card id CRD09698D2C951AB3B30C57B48EE0FB1F30
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/cart/carts'

        extra_headers = {
            'biz_scenario': '101',
            'biz_from': '1007',
            'sid': sid,
        }

        json_data = {
            'abTest': '562d8ad1-848b-4297-a1ed-38f0ee038433,01a8ee73-95e2-417e-a87e-b19379a19005',
            'beCode': be_code,
            'beType': be_type,
            'cartType': str(cart_type),
            'channelCode': channel_code,
            # TODO: 代表什么意思，有时值会为3? 是指从优惠券页跳过来吗？
            'dataSource': 1,
            'daypartCode': str(day_part_code),
            'maxPurchaseQuantity': 999,
            'orderType': order_type,
            'products': [
                {
                    'addonInvalidTime': 0,
                    # TODO: 套餐时才会有这个字段，单品没有
                    # 'alcPrice': 0,  # TODO: if update the real price here
                    'bu': 0,  # TODO: figure out what does it mean, it's set to 1 sometimes
                    # 如早餐卡
                    'cardId': card_id,
                    # 正常商品0, 早餐卡2
                    'cardType': card_type,
                    'code': sub_product_code if sub_product_code != '' else product_code,
                    'couponCode': coupon_code,
                    'couponId': coupon_id,
                    'customizationMode': 0,
                    'dayOfWeek': '',
                    'desc': '',
                    'failType': 0,
                    'image': '',
                    'isCouponEnable': 0,
                    'isPromotion': 0,
                    'isSellOut': False,
                    'likeCount': 0,
                    # TODO: 套餐时才会有这个字段，单品没有
                    # 'links': [],
                    'longName': '',
                    # TODO: 可订制化时有此字段？
                    # 'matchedSpecsCode': '3-1',
                    'name': '',
                    'operationType': 0,
                    'packingFeePrice': 0,
                    'promotionId': promotion_id,
                    # TODO: if price related fields are necessary here
                    # 'price': 0,
                    # 'priceText': '',
                    # 'promotionId': '',
                    'quantity': quantity,
                    'realPackingFeeTotalPrice': 0,
                    'saleStatus': 1,
                    'sequence': -1,
                    'showSelectBtn': False,
                    # product type? 1: normal, 0: membership/card
                    'type': product_type,  # TODO: figure out what the type is, it's set to 7 sometimes, 0 other times
                },
            ],
            'storeCode': store_code,
            'storeName': '',
        }

        if membership_code != '':
            json_data['products'][0]['membershipCode'] = membership_code

        if customization:
            '''
            # 单选
            {
                "items": [{
                    "code": "200002",
                    "values": [{
                        # 单选，默认checked=1
                        "code": "2",
                    }]
                }],
                "options": []
            }
    
            # 多选
            {
                "items": [],
                "options": [{
                    # 多选，因为默认是checked=1，所以需要把checked=0的都传过来
                    "checked": 0,
                    "code": "100136",
                }]
            }
            '''
            json_data['products'][0].update({
                'customization': customization
            })

        # [
        #     {
        #         "code": "503700",
        #         "round": 1,
        #         "hasCustomization": 1,
        #         "options": [
        #             {
        #                 "code": "100141",
        #                 "checked": 0
        #             },
        #             {
        #                 "code": "100202",
        #                 "checked": 0
        #             }
        #         ]
        #     },
        #     {
        #         "code": "504495",
        #         "round": 2,
        #         "hasCustomization": 0,
        #     },
        #     {
        #         "code": "509303",
        #         "round": 3,
        #         "hasCustomization": 1,
        #         "items": [{
        #             "code": "200002",
        #             "values": [{
        #                 "code": "2",
        #                 "checked": 0
        #             }]
        #         }]
        #     },
        # ]
        if combo_items:
            req_combo_items = []

            for idx, item in enumerate(combo_items):
                combo_item = {}
                if item['hasCustomization']:
                    customization = {}
                    if 'options' in item:
                        customization.update({
                            'options': item['options']
                        })
                    elif 'items' in item:
                        customization.update({
                            'items': item['items']
                        })
                    combo_item.update({
                        'comboProducts': [
                            {
                                "code": item['code'],
                                'customization': customization,
                                'quantity': 1,
                            }
                        ],
                        # 麦满分早餐的饮品round值不是连续的，会跳一个数
                        'round': item['round'] if 'round' in item else idx + 1,
                    })
                else:
                    combo_item.update({
                        'comboProducts': [
                            {
                                'code': item['code'],
                                'quantity': 1,
                            }
                        ],

                        "round": item['round'] if 'round' in item else idx + 1,
                    })
                req_combo_items.append(combo_item)
            json_data['products'][0].update({
                'comboItems': req_combo_items
            })

        return await self._base_put_request(url, extra_headers, json_data, app_version='********')

    async def empty_cart(self, sid, store_code, cart_type, channel_code, be_type, be_code, day_part_code, order_type=1):
        """
        Empty cart
        :param sid: sid
        :param store_code: store code
        :param cart_type: cart type
        :param channel_code: channel code
        :param be_type: business type
        :param be_code: business code
        :param day_part_code: day part code
        :param order_type: order type
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/cart/carts/empty'

        extra_headers = {
            'biz_scenario': '200',
            'biz_from': '1003',
            'sid': sid,
        }

        json_data = {
            'abTest': '562d8ad1-848b-4297-a1ed-38f0ee038433,01a8ee73-95e2-417e-a87e-b19379a19005',
            'beCode': be_code,
            'beType': be_type,
            'cartType': str(cart_type),
            'changeAddress': 1,
            'channelCode': channel_code,
            'daypartCode': str(day_part_code),
            'orderType': order_type,
            'storeCode': store_code,
            'storeName': '',
        }

        return await self._base_put_request(url, extra_headers, json_data)

    async def get_cart_validation_info(self, sid, store_code, channel_code, order_type, cart_type, day_part_code, be_code='', address_id=''):
        """
        Get validation info of cart
        :param sid: sid
        :param store_code: store code
        :param channel_code: channel code
        :param order_type: order type
        :param cart_type: cart type
        :param day_part_code: day part code
        :param be_code: business code
        :param address_id: address id which is only required in delivery
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/order/confirmation/validationinfo'

        extra_headers = {
            'biz_from': '1004',
            'biz_scenario': '200',
            'sid': sid,
        }

        params = {
            'storeCode': store_code,
            'channelCode': channel_code,
            'orderType': str(order_type),
            'cartType': str(cart_type),
            'dayPartCode': str(day_part_code),
            'beCode': be_code,
        }

        # 外卖单需要提供外送地址
        if int(order_type) == 2:
            params['addressId'] = address_id

        return await self._base_get_request(url, extra_headers, params, app_version='********')

    async def make_order(self, sid, valid_info, store_code, order_type, day_part_code, be_type, be_code, eat_type, cash_coupon_code='', cash_coupon_id='', address_id=''):
        """
        Make order
        :param sid: sid
        :param valid_info: valid info for cart
        :param store_code: store code
        :param order_type: order type
        :param day_part_code: day part code
        :param be_type: business type
        :param be_code: business code
        :param eat_type: eat type
        :param cash_coupon_code: cash coupon code if used
        :param cash_coupon_id: cash coupon id if used
        :param address_id: address id which is only required in delivery
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/order/orders'

        extra_headers = {
            'biz_scenario': '202',
            'biz_from': '1012',
            'sid': sid,
        }

        json_data = {
            'beType': be_type,
            'beCode': be_code,
            'customerConfirm': False,
            'dayPartCode': str(day_part_code),
            'driveDuration': -1,
            'orderType': str(order_type),
            'selectCashCouponCode': '',
            'source': 0,
            'storeCode': store_code,
            'tablewareCode': 'no',
        }

        if cash_coupon_code != '' and cash_coupon_id != '':
            json_data.update({
                "cashCoupon": {
                    "couponCode": cash_coupon_code,
                    "couponId": cash_coupon_id
                },
            })

        card_list = []
        if 'rightCardInfo' in valid_info['confirmInfo']['productPriceInfo']:
            for card in valid_info['confirmInfo']['productPriceInfo']['rightCardInfo']['cardList']:
                card['menuCardType'] = 0
                card_list.append(card)

        cart_items = []
        for cart_item in valid_info['confirmInfo']['productPriceInfo']['cartProductList']:
            cart_item.update({
                'changeQuantity': 0,
                'pmtPrdReplace': False
            })
            cart_item.pop('saleStatus')
            cart_items.append(cart_item)

        json_data.update({
            'cartItems': cart_items,
            'realTotalAmount': valid_info['confirmInfo']['productPriceInfo']['realTotalAmount']
        })
        if len(card_list) > 0:
            json_data.update({
                'menuCardList': card_list,
            })

        json_data.update({
            'promotionList': valid_info['confirmInfo']['productPriceInfo'].get('promotionList', [])
        })

        if int(order_type) == 1:
            pick_up_time_options = []
            selected_pick_up_time_option = None
            for pick_up_time_option in valid_info['confirmInfo']['storePickUpInfo']['pickUpTimeOptions']:
                selected = False
                if 'selected' in pick_up_time_option:
                    selected_pick_up_time_option = pick_up_time_option
                    selected = True
                pick_up_time_option.update({
                    'needIcon': False,
                    'tableType': 0,
                    'selected': selected,
                    'itemType': int(pick_up_time_option['itemType'])
                })
                pick_up_time_options.append(pick_up_time_option)

            json_data.update({
                'pickUpTimeOptions': pick_up_time_options,
                'pickupTimeCode': selected_pick_up_time_option['code'],
                'pickupTimeType': selected_pick_up_time_option['itemType'],
                'eatTypeCode': eat_type,
            })
        elif int(order_type) == 2:
            selected_delivery_date_option = None
            selected_delivery_time_option = None
            for delivery_date_option in valid_info['confirmInfo']['deliveryInfo']['expectDeliveryTimeOptions']:
                selected_found = False
                for delivery_time_option in delivery_date_option['deliveryTime']:
                    if 'selected' in delivery_time_option:
                        selected_delivery_date_option = delivery_date_option
                        selected_delivery_time_option = delivery_time_option
                        selected_found = True
                        break
                if selected_found:
                    break

            json_data.update({
                'addressId': address_id,
                "expectDeliveryDateCode": selected_delivery_date_option['deliveryDateCode'],
                "expectDeliveryTime": f"{selected_delivery_date_option['deliveryDateCode']} {selected_delivery_time_option['code']}",
                "expectDeliveryTimeCode": selected_delivery_time_option['code'],
                'pickUpTimeOptions': [],
            })

        return await self._base_post_request(url, extra_headers, json_data, app_version='********')


    async def make_order_directly(self, sid, cart_items, real_total_amount, store_code, order_type, day_part_code,
                                  be_type, be_code, eat_type, cash_coupon_code='', cash_coupon_id='', address_id='',
                                  delivery_time='', real_delivery_price='', promotion_list=None, card_id=''):
        """
        Make order directly
        :param sid: sid
        :param cart_items: cart items
        :param real_total_amount: real total amount
        :param store_code: store code
        :param order_type: order type
        :param day_part_code: day part code
        :param be_type: business type
        :param be_code: business code
        :param eat_type: eat type
        :param cash_coupon_code: cash coupon code if used
        :param cash_coupon_id: cash coupon id if used
        :param address_id: address id which is only required in delivery
        :param delivery_time: delivery time
        :param real_delivery_price: real delivery price
        :param promotion_list: promotion list
        :param card_id: card id
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/order/orders'

        extra_headers = {
            'biz_scenario': '202',
            'biz_from': '1012',
            'sid': sid,
        }

        json_data = {
            "beType": be_type,
            "beCode": be_code,
            "cartItems": cart_items,
            "customerConfirm": False,
            "dayPartCode": day_part_code,
            "driveDuration": -1,
            "orderType": order_type,
            "realTotalAmount": real_total_amount,
            "selectCashCouponCode": '',
            "source": 0,
            "storeCode": store_code,
            "tablewareCode": "no",
        }

        if cash_coupon_code != '':
            json_data.update({
                "cashCoupon": {
                    "couponCode": cash_coupon_code,
                    "couponId": cash_coupon_id
                },
            })

        if promotion_list:
            json_data.update({
                'promotionList': promotion_list
            })

        if card_id != '':
            json_data.update({
                'cardId': card_id
            })

        if int(order_type) == 1:
            json_data.update({
                "pickupTimeCode": "now",
                "pickupTimeType": "1",
                "eatTypeCode": eat_type,
            })
        elif int(order_type) == 2:
            d, t = delivery_time.split()
            json_data.update({
                'addressId': address_id,
                "expectDeliveryDateCode": d,
                "expectDeliveryTime": t,
                "expectDeliveryTimeCode": delivery_time,
                "realDeliveryPrice": real_delivery_price,
            })

        return await self._base_post_request(url, extra_headers, json_data, app_version='********')

    async def ecmall_add_to_cart(self, sid, city_code, sku_id):
        """
        ECMALL add to cart
        :param sid: sid
        :param city_code: city code
        :param sku_id: sku id
        :return: cart info
        """
        url = 'https://api.mcd.cn/bff/ectrade/settle/detail'

        extra_headers = {
            'biz_from': '1020',
            'biz_scenario': '300',
            'sid': sid,
        }

        json_data = {
            "activId": 0,
            "cityCode": str(city_code),
            "giftOptType": 0,
            "orderId": "",
            "scene": 1,
            "secKill": False,
            "shopId": 1,
            "skus": [{
                "count": 1,
                "skuId": sku_id
            }],
            "spuMainImage": ""
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def ecmall_make_order(self, sid, city_code, sku_id, pre_commit_no):
        """
        ECMALL make order
        :param sid: sid
        :param city_code: city code
        :param sku_id: sku id
        :param pre_commit_no: pre commit number
        :return: order info
        """
        url = 'https://api.mcd.cn/bff/ectrade/order/create'

        extra_headers = {
            'biz_from': '1021',
            'biz_scenario': '300',
            'sid': sid,
        }

        json_data = {
            "activId": 0,
            "catType": 1,
            "cityCode": str(city_code),
            "gift": False,
            "giftType": 0,
            "goods": [{
                "count": 1,
                "skuId": sku_id
            }],
            "preCommitNo": pre_commit_no,
            "scene": 1,
            "secKill": False,
            "shopId": 1
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def prepare_order(self, sid, pay_id, pay_channel='ALI', source=0):
        """
        Prepare order for payment
        :param sid: sid
        :param pay_id: pay id returned from making order API
        :param pay_channel: 'ALI' or 'WX'
        :param source: 0 for normal order, 1 for ecmall order
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/cashier/preorder'

        extra_headers = {
            'biz_from': '1002',
            'biz_scenario': '501',
            'sid': sid,
        }

        json_data = {
            'freePayFlg': False,
            'payChannel': pay_channel,
            'payId': pay_id,
            'source': source,
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def get_order_detail(self, sid, order_id):
        """
        Get order detail
        :param sid: sid
        :param order_id: the order id
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/order/orders/{order_id}'

        extra_headers = {
            'biz_from': '1023',
            'biz_scenario': '202',
            'sid': sid,
        }

        return await self._base_get_request(url, extra_headers, None, order_id)

    async def get_ectrade_order_detail(self, sid, order_id):
        """
        Get ecmall order detail
        :param sid: sid
        :param order_id: the order id
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/ectrade/order/detail'

        extra_headers = {
            'biz_from': '1044',
            'biz_scenario': '300',
            'sid': sid,
        }

        params = {
            'orderId': order_id
        }

        return await self._base_get_request(url, extra_headers, params)

    async def cancel_order(self, sid, order_id, cancel_reason_code='1'):
        """
        Cancel order function
        :param sid: sid
        :param order_id: the order id to be cancelled
        :param cancel_reason_code: the reason code to cancel the order
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/order/orders/{order_id}/cancellation'

        extra_headers = {
            'biz_from': '1033',
            'biz_scenario': '202',
            'sid': sid,
        }

        json_data = {
            'cancelReasonCode': cancel_reason_code,
            'cancelRemark': ""
        }

        return await self._base_put_request(url, extra_headers, json_data, order_id)

    async def get_exchange_coupon_detail(self, sid, coupon_code):
        """
        Get exchange coupon detail
        :param sid: sid
        :param coupon_code: the coupon code to be exchanged
        :return: response in json format
        """
        url = 'https://api.mcd.cn/btb/bip/coupon/exchanges'

        extra_headers = {
            'sid': sid,
        }

        json_data = {
            'couponCode': coupon_code
        }

        '''
        {
            "success": false,
            "code": 402102,
            "message": "已绑定的券码不支持兑换",
            "datetime": "2022-11-12 20:37:25",
            "traceId": "39c14fef-3b5a-4fda-a6ae-7d21050c7c1e"
        }
        {
            "success": false,
            "code": 402103,
            "message": "券码不存在",
            "datetime": "2022-11-12 20:38:07",
            "traceId": "90952852-5748-4223-bb77-615bc6fd321d"
        }
        {
            "success": true,
            "code": 200,
            "message": "请求成功",
            "datetime": "2022-11-12 20:37:02",
            "data": {
                "couponDetail": {
                    "couponCode": "80644246046322471036",
                    "couponCodeStartTime": "2022.09.21",
                    "couponCodeEndTime": "2025.09.19",
                    "availableQuantity": 1,
                    "tradeChannel": "麦当劳APP/微信小程序/支付宝小程序/线下餐厅扫码",
                    "couponTitle": "麦当劳10元电子券"
                }
            }
        }
        '''
        return await self._base_web_post_request(url, extra_headers, json_data)

    async def do_exchange_coupon(self, sid, coupon_code):
        """
        Exchange coupon by code
        :param sid: sid
        :param coupon_code: the coupon code to be exchanged
        :return: response in json format
        """
        url = 'https://api.mcd.cn/btb/bip/coupon/exchanges'

        extra_headers = {
            'sid': sid,
        }

        json_data = {
            'couponCode': coupon_code,
            'checkDetail': False,
            'password': ''
        }

        '''
        {
            "success": true,
            "code": 200,
            "message": "请求成功",
            "datetime": "2022-11-12 20:40:04",
            "data": {
                "successMsg": "卡券已存入您的麦钱包，请查收",
                "url": "mcdapp://page?iosPageName=MCDMyWalletHomeViewController&androidPageName=ComponentUser&androidPageAction=coupon_list"
            }
        }
        '''
        return await self._base_web_post_request(url, extra_headers, json_data)

    async def get_exchange_coupon_history(self, sid, page_no=1, page_size=10):
        """
        Get exchange coupon history
        :param sid: sid
        :param page_no: page number
        :param page_size: size per page
        :return: response in json format
        """
        url = 'https://api.mcd.cn/btb/bip/coupon/exchanges'

        extra_headers = {
            'sid': sid,
        }

        params = {
            'pageNo': page_no,
            'pageSize': page_size
        }

        return await self._base_web_get_request(url, extra_headers, params)

    async def get_membership_coupons(self, sid, city_code, channel_code, page_id):
        """
        Get coupons for membership day
        :param sid: sid
        :param city_code: city code
        :param channel_code: channel code
        :param page_id: page id
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/page/coupon/customer/getable'

        extra_headers = {
            'sid': sid,
            'ct': '10',
            'Origin': 'https://cdn.mcd.cn',
            'X-Requested-With': 'com.mcdonalds.gma.cn',
            'Referer': 'https://cdn.mcd.cn/',
        }

        params = {
            'putChannel': channel_code,
            'pageId': page_id,
            'cityCode': city_code,
            'sid': sid,
        }

        return await self._base_web_get_request(url, extra_headers, params)

    async def take_membership_coupons(self, sid, city_code, channel_code, page_id):
        """
        Take available coupons on membership day
        :param sid: sid
        :param city_code: city code
        :param channel_code: channel code
        :param page_id: page id
        :return: response in json format
        """
        res = await self.get_membership_coupons(sid, city_code, channel_code, page_id)
        coupons = []
        for group in res['data']['groups']:
            if 'coupon' not in group['groupTag']:
                continue
            for module in group['modules']:
                if module['actionStatus'] != 3:
                    continue
                for coupon in module['coupons']:
                    if coupon['status'] != 1 or coupon['label'] is not None:
                        continue
                    coupons.append({
                        'couponId': coupon['couponId'],
                        'receiveQuantity': coupon['receiveQuantity'],
                        'newUserFlag': coupon['newUserFlag']
                    })

        url = 'https://api.mcd.cn/bff/member/coupon/bind'

        extra_headers = {
            'sid': sid,
            'ct': '10',
            'Origin': 'https://cdn.mcd.cn',
            'X-Requested-With': 'com.mcdonalds.gma.cn',
            'Referer': 'https://cdn.mcd.cn/',
        }

        json_data = {
            'channelCode': channel_code,
            'coupons': coupons,
            'pageId': page_id,
            'cityCode': city_code,
        }

        return await self._base_web_post_request(url, extra_headers, json_data)

    async def draw_lottery(self, sid):
        """
        Draw lottery with account points
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/market/lottery/point/draw'

        extra_headers = {
            'biz_from': '1004',
            'biz_scenario': '400',
            'sid': sid,
        }

        params = {
            'activityCode': 'jxv0',
        }

        return await self._base_get_request(url, extra_headers, params)

    async def points_products(self, sid, city_code):
        """
        Get points exchangeable products
        :param sid: sid
        :param city_code: city code
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/myrewards/home/<USER>'

        extra_headers = {
            'biz_from': '1006',
            'biz_scenario': '202',
            'sid': sid
        }

        params = {
            'cityCode': city_code
        }

        return await self._base_get_request(url, extra_headers, params)

    async def points_product_sku_id(self, sid, product_id, shop_id, city_code=''):
        """
        Get points product sku id
        :param sid: sid
        :param product_id: points product id
        :param shop_id: shop id
        :param optional city_code: city code
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/ecmall/detail/{product_id}'

        extra_headers = {
            'biz_from': '1006',
            'biz_scenario': '202',
            'sid': sid
        }

        params = {
            # 'cityCode': city_code,
            'shopId': shop_id
        }

        return await self._base_get_request(url, extra_headers, params, product_id)

    async def points_product_pre_commit_no(self, sid, product_id):
        """
        Get points product pre commit number
        :param sid: sid
        :param product_id: points product id
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/myrewards/product/{product_id}/detail/other'

        extra_headers = {
            'biz_from': '1006',
            'biz_scenario': '202',
            'sid': sid
        }

        return await self._base_get_request(url, extra_headers, None, product_id)

    async def points_product_make_order(self, sid, shop_id, sku_id, pre_commit_no, city_code=''):
        """
        Get points product make order
        :param sid: sid
        :param shop_id: shop id
        :param sku_id: sku id
        :param pre_commit_no: pre commit number
        :param optional city_code: city code
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/ectrade/order/create'

        extra_headers = {
            'biz_from': '1006',
            'biz_scenario': '202',
            'sid': sid
        }

        json_data = {
            # 'addressId': '',
            # 'catType': 0,
            # 'cityCode': city_code,
            # 'executeType': 1,
            # 'gift': False,
            # 'giftType': 0,
            'goods': [
                {
                    'count': 1,
                    'skuId': int(sku_id),
                },
            ],
            'preCommitNo': pre_commit_no,
            'scene': 1,
            # 'secKill': False,
            'shopId': shop_id,
            # 'spuMainImage': 'https://img.mcd.cn/ecs/347ac5fd08ecb716.png',
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def get_msgbox_list(self, sid, page_no=1, page_size=10, category=1):
        """
        Get message box list
        :param sid: sid
        :param page_no: page number
        :param page_size: size per page
        :param category: message box category, 1 means unread?
        :return: response in json format
        """
        url = 'https://api.mcd.cn/mc/api/msg/box/list'

        extra_headers = {
            'biz_from': '1055',
            'biz_scenario': '201',
            'sid': sid,
        }

        params = {
            'page': page_no,
            'size': page_size,
            'category': category,
        }

        return await self._base_get_request(url, extra_headers, params)

    @staticmethod
    async def do_survey(sid, jump_url, app_version='6.0.49.1'):
        """
        Do fill the survey
        :param sid: sid
        :param jump_url: survey url
        :param app_version: app_version
        :return:
        """
        try:
            res = requests.get(jump_url, allow_redirects=False)
        except Exception as e:
            logger.exception(getattr(e, "__dict__", {}))
            return None

        logger.info(res.text)
        logger.info(res.headers)
        url = res.headers['Location']
        if 'expired' in url:
            logger.info("The survey has expired!")
            raise mcd_exceptions.SurveyExpired()
        parse_result = urllib.parse.urlparse(url)
        query_dict = urllib.parse.parse_qs(parse_result.query)
        logger.info(json.dumps(query_dict, indent=4, ensure_ascii=False))
        if 'surveyId' in query_dict:
            logger.info("The survey has completed!")
            raise mcd_exceptions.SurveyCompleted()

        cem_trade_no = query_dict['cem_trade_no'][0]
        cem_store_code = query_dict['cem_store_code'][0]
        cem_trade_time = query_dict['cem_trade_time'][0]
        cem_signature = query_dict['cem_signature'][0]
        rspd = query_dict['rspd'][0]

        cookies = {
            'p': '110',
            'ct': '102',
            'web_env': 'api.mcd.cn',
            'v': app_version,
            'language': 'cn',
            'locale': 'zh_CN',
            'sid': sid,
        }

        headers = {
            'Host': 'survey.mcd.com.cn',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P Build/OPM7.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.117 Mobile Safari/537.36mcd-android',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'http://survey.mcd.com.cn',
            'X-Requested-With': 'com.mcdonalds.gma.cn',
            'Referer': url,
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        }

        json_data = {
            'status': 1,
            'answers': {
                'Q1': cem_trade_no,
                'Q2': cem_store_code,
                'Q3': parser.parse(cem_trade_time).strftime('%Y/%m/%d'),
                'Q4': 4,
                'Q5': 1,
                'Q6': 5,
                'Q48': 2,
                'Q11': 5,
                'Q7': 5,
                'Q47': [
                    4,
                ],
                'Q9': 5,
                'Q14': 5,
                'Q16': 1,
                'Q39': [
                    4,
                    3,
                    2,
                    1,
                ],
                'Q60': 1,
                'Q63': 5,
                'Q23': 10,
                'Q22': 1,
                'Q25': 2,
            },
            'cem_signature': cem_signature,
            'userAgent': '',
            'cem_trade_time': cem_trade_time,
        }

        try:
            resp = requests.patch(
                f'http://survey.mcd.com.cn/api/survey/rspd/modify/{rspd}/',
                cookies=cookies,
                headers=headers,
                json=json_data,
            )

            res = resp.json()
            logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
            return res
        except Exception as e:
            logger.exception(getattr(e, "__dict__", {}))
            return None

    @staticmethod
    async def get_survey_coupons(sid, survey_id, app_version='********'):
        """
        Get survey coupons
        :param sid: sid
        :param survey_id: survey id
        :param app_version: app version
        :return: response in json format
        """
        cookies = {
            'p': '102',
            'ct': '102',
            'web_env': 'api.mcd.cn',
            'v': app_version,
            'language': 'cn',
            'sid': sid,
            # 'sensorsdata2015jssdkcross': '%7B%22distinct_id%22%3A%221846b69601eaf-01c6f1284f0d5c-36375e00-301584-1846b69601f6%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%221846b69601eaf-01c6f1284f0d5c-36375e00-301584-1846b69601f6%22%7D',
            'queryHost': 'r1',
            'tid': '00003TuM',
        }

        headers = {
            'Host': 'cdn.mcd.cn',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P Build/OPM7.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.105 Mobile Safari/537.36mcd-android',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'X-Requested-With': 'com.mcdonalds.gma.cn',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Referer': 'http://survey.mcd.com.cn/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        }

        params = {
            'surveyId': survey_id,
        }

        async with aiohttp.ClientSession() as session:
            resp = await session.get('https://cdn.mcd.cn/cms/pages/survey.html', params=params, cookies=cookies, headers=headers)
            text = await resp.text()
            r = re.findall(r'"couponIds":\[([^\]]*)\]', text)
            if r:
                coupon_ids = [ele[1:-1] for ele in r[0].split(',')]
                logger.debug(json.dumps(coupon_ids, indent=4, ensure_ascii=False))
                return {
                    'coupons': coupon_ids
                }
            return {
                'coupons': []
            }

    async def take_survey_coupon(self, sid, order_id, page_id, survey_id, coupon_id):
        """
        Take survey coupon
        :param sid: sid
        :param order_id: order id
        :param page_id: page id
        :param survey_id: survey id
        :param coupon_id: coupon id
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/coupon/survey/bind'

        extra_headers = {
            'sid': sid,
            'ct': '10',
            'Origin': 'https://cdn.mcd.cn',
            'X-Requested-With': 'com.mcdonalds.gma.cn',
            'Referer': 'https://cdn.mcd.cn/',
        }

        json_data = {
            # 'orderId': '1030682390000220212284875738',
            'orderId': order_id,
            # 'sign': '31CAC7D442EA9D294513B24EF00B3CD8',
            # 'source': '16',
            'surveyId': survey_id,
            # 'meddyId': 'MEDDY178821351473147131',
            'coupons': [
                {
                    'couponId': coupon_id,
                },
            ],
            'pageId': page_id
        }

        return await self._base_web_post_request(url, extra_headers, json_data)

    async def add_new_address(self, sid, address_info, contact_info):
        """
        Add new address for delivery
        :param sid: sid
        :param address_info: address info
        :param contact_info: contact info
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/addresses'

        extra_headers = {
            'biz_from': '1002',
            'biz_scenario': '103',
            'sid': sid,
        }

        # address_info = {
        #     'address': '首开国风北京',
        #     'cityCode': '110100',
        #     'cityName': '北京市',
        #     'latitude': 39.994422,
        #     'longitude': 116.486459,
        #     'streetAddress': '北京市朝阳区望京街道望京新城A1区',
        #     'detail': '11#11',
        # }
        # contact_info = {
        #     'fullName': '王',
        #     'gender': 1,
        #     'phone': '13800138000',
        # }

        json_data = {
            'address': address_info['address'],
            'channel': '03',
            'cityCode': address_info['cityCode'],
            'cityName': address_info['cityName'],
            'latitude': address_info['latitude'],
            'longitude': address_info['longitude'],
            'streetAddress': address_info['streetAddress'],
            'detail': address_info['detail'],
            'fullName': contact_info['fullName'],
            'gender': contact_info['gender'],
            'phone': contact_info['phone'],
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def update_address(self, sid, address_id, address_info, contact_info):
        """
        Update existing address for delivery
        :param sid: sid
        :param address_id: addressId to be updated
        :param address_info: address info
        :param contact_info: contact info
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/member/addresses/{address_id}'

        extra_headers = {
            'biz_from': '1004',
            'biz_scenario': '103',
            'sid': sid,
        }

        # address_info = {
        #     'address': '首开国风北京',
        #     'cityCode': '110100',
        #     'cityName': '北京市',
        #     'latitude': 39.994422,
        #     'longitude': 116.486459,
        #     'streetAddress': '北京市朝阳区望京街道望京新城A1区',
        #     'detail': '11#11',
        # }
        # contact_info = {
        #     'fullName': '王',
        #     'gender': 1,
        #     'phone': '13800138000',
        # }

        json_data = {
            'id': address_id,
            'address': address_info['address'],
            'channel': '03',
            'cityCode': address_info['cityCode'],
            'cityName': address_info['cityName'],
            'latitude': address_info['latitude'],
            'longitude': address_info['longitude'],
            'streetAddress': address_info['streetAddress'],
            'detail': address_info['detail'],
            'fullName': contact_info['fullName'],
            'gender': contact_info['gender'],
            'phone': contact_info['phone'],
            'tags': ''
        }

        return await self._base_put_request(url, extra_headers, json_data, address_id)

    async def get_addresses(self, sid):
        """
        Get all saved addresses
        :param sid: sid
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/addresses'

        extra_headers = {
            'biz_from': '1001',
            'biz_scenario': '103',
            'sid': sid,
        }

        return await self._base_get_request(url, extra_headers, None)

    async def archcard_pay_init(self, user_id: str, sid: str) -> object:
        """
        Archard pay init
        :param user_id: user id
        :param sid: sid
        :return: response in json format
        """
        url = 'https://wbapi.archcard.mcdonalds.com.cn/'

        payload = {
            'userId': user_id,
            'memberId': user_id,
            'token': sid,
            'lang': 'zh'
        }

        # {"amount":"500","transId":"201802804665328525180928","tn":"2e1108f0ed760e293188faae84b5c0ee","userId":"MEDDY920168127384188971","memberId":"MEDDY920168127384188971","token":"cbeef1cd2e962420cf4786463062054c_","clientType":"6","lang":"zh"}
        payload_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
        logger.info(f"payload: {payload_str}")

        key = generate_random_key()

        encrypt_key = rsa_encrypt(key)
        logger.info(f"encrypted: {encrypt_key}")

        signature = rsa_sign(payload_str)
        logger.info(f"signature: {signature}")
        verify_rsa_sign(signature)

        encrypt_data = aes_encrypt(key, payload_str)

        json_data = {
            'funcode': 'A1.AC002',
            'cooperator': 'C8888',
            'version': '1.0.0',
            'type': 'app',
            'extend': {
                'userId': user_id,
                'token': sid,
                'pwdVersion': 1,
                'sdkVersion': 10,
            },
            'encryptData': encrypt_data,
            'encryptKey': encrypt_key,
            'signature': signature,
        }

        res = await self._base_wbapi_post_request(url, json_data)
        verify_rsa_sign(res['signature'])
        recovered_key = rsa_decrypt(res['encryptKey'])
        plain_text = aes_decrypt(recovered_key, res['encryptData'])
        jobj = json.loads(plain_text)
        logger.info(json.dumps(jobj, indent=4, ensure_ascii=False))
        return jobj

    async def archcard_pay_verify(self, user_id: str, sid: str, amount: str, trans_id: str, tn: str) -> object:
        """
        archcard pay verify
        :param user_id: user id
        :param sid: sid
        :param amount: amount to pay
        :param trans_id: trans_id returned from preorder
        :param tn: tn returned from preorder
        :return: response in json format
        """
        url = 'https://wbapi.archcard.mcdonalds.com.cn/'

        payload = {
            "amount": amount,
            # "transId": "201802804866183236947968",
            # "tn": "bada631f098f8fd80f76ce6195aafb99",
            "transId": trans_id,
            "tn": tn,
            "userId": user_id,
            "memberId": user_id,
            "token": sid,
            "clientType": "6",
            "lang": "zh"
        }

        payload_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
        logger.info(f"payload: {payload_str}")

        key = generate_random_key()

        encrypt_key = rsa_encrypt(key)
        logger.info(f"encrypted: {encrypt_key}")

        signature = rsa_sign(payload_str)
        logger.info(f"signature: {signature}")
        verify_rsa_sign(signature)

        encrypt_data = aes_encrypt(key, payload_str)

        json_data = {
            'funcode': 'A2.YEC008',
            'cooperator': 'C8888',
            'version': '1.0.0',
            'type': 'app',
            'extend': {
                'userId': user_id,
                'token': sid,
                'pwdVersion': 1,
                'sdkVersion': 10,
            },
            'encryptData': encrypt_data,
            'encryptKey': encrypt_key,
            'signature': signature,
        }

        res = await self._base_wbapi_post_request(url, json_data)
        verify_rsa_sign(res['signature'])
        recovered_key = rsa_decrypt(res['encryptKey'])
        plain_text = aes_decrypt(recovered_key, res['encryptData'])
        jobj = json.loads(plain_text)
        logger.info(json.dumps(jobj, indent=4, ensure_ascii=False))
        return jobj

    async def archcard_pay_with_pwd(self, user_id: str, sid: str, trans_id: str, password: str) -> object:
        """
        archcard pay with password
        :param user_id: user id
        :param sid: sid
        :param trans_id: trans_id returned from preorder
        :param password: plain six-digit password
        :return:
        """
        url = 'https://wbapi.archcard.mcdonalds.com.cn/'

        rand_num = str(int((random.random() * 9 + 1) * 100000))
        encrypted_password = hexlify(rsa_encrypt_webank(f"{password}{rand_num}".encode('utf-8'))).decode().upper()

        payload = {
            # "transId": "201802804866183236947968",
            # "password": "68453826A87633D866A912B16C87F14779A858DF0AF629699D14A9A9B94B1BEB92D5BCE70D3C6F1BD15AED331A3098953BB27541B43AC3CE71F548F0863C356A77EBE4970EA241FEE98568C3BCE86470FCCBD278C6C5C793DD23B4FB3B55A5E870B151ABFE08E8A1E17B81AC9E5BA3290DD12391617D40778E732A0888384BEBC1E6CE2D545AAEA0F04232FE5A1EFE0DC3FF13CA3C27E93461F419DED50EA30217E1CE08A50E9FCE649B6926FF261EBAFBF5A7F81DB7A607866193C34309711A9B432C86C0A7F809C66378C9A8CA2DBABB3893FA653B4CA86850237E2F5F317239139F62116852B424039CE6787227D9A0E6DC109C5D2B3B9BB2BA9B51AF03E5",
            "transId": trans_id,
            "password": encrypted_password,
            "memberId": user_id,
            "lang": "zh",
            "random": rand_num
        }

        payload_str = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
        logger.info(f"payload: {payload_str}")

        key = generate_random_key()

        encrypt_key = rsa_encrypt(key)
        logger.info(f"encrypted: {encrypt_key}")

        signature = rsa_sign(payload_str)
        logger.info(f"signature: {signature}")
        verify_rsa_sign(signature)

        encrypt_data = aes_encrypt(key, payload_str)

        json_data = {
            'funcode': 'A2.YEC011',
            'cooperator': 'C8888',
            'version': '1.0.0',
            'type': 'app',
            'extend': {
                'userId': user_id,
                'token': sid,
                'pwdVersion': 1,
                'sdkVersion': 10,
            },
            'encryptData': encrypt_data,
            'encryptKey': encrypt_key,
            'signature': signature,
        }

        res = await self._base_wbapi_post_request(url, json_data)
        verify_rsa_sign(res['signature'])
        recovered_key = rsa_decrypt(res['encryptKey'])
        plain_text = aes_decrypt(recovered_key, res['encryptData'])
        jobj = json.loads(plain_text)
        logger.info(json.dumps(jobj, indent=4, ensure_ascii=False))
        return jobj

    async def get_benefit_use_detail(self, sid, benefit_id):
        """
        Get benefit use detail
        :param sid: sid
        :param benefit_id: benefit id
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/myrewards/benefit/use/detail/{benefit_id}'

        extra_headers = {
            'sid': sid,
        }

        return await self._base_get_request(url, extra_headers, None, benefit_id)

    async def get_benefit_detail(self, sid, benefit_id, city_code):
        """
        Get benefit use detail
        :param sid: sid
        :param benefit_id: benefit id
        :param city_code: city code
        :return: response in json format
        """
        url = f'https://api.mcd.cn/bff/myrewards/benefit/introduce/detail/{benefit_id}'

        extra_headers = {
            'sid': sid,
        }

        params = {
            'cityCode': str(city_code)
        }

        return await self._base_get_request(url, extra_headers, params, str(benefit_id))

    async def get_orders(self, sid, order_category_id=0, page=1):
        """
        Get orders
        :param sid: sid
        :param order_category_id: order category id. 0: all orders
        :param page: page number
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/order/orders'

        extra_headers = {
            'biz_from': '1001',
            'biz_scenario': '202',
            'sid': sid,
        }

        params = {
            'orderCategoryId': order_category_id,
            'page': page,
        }
        return await self._base_get_request(url, extra_headers, params)

    async def get_right_card_detail(self, sid, card_type, card_id, card_no):
        """
        Get right card detail
        :param sid: sid
        :param card_type: card type
        :param card_id: card id
        :param card_no: card no
        :return: response in json format
        """
        url = "https://api.mcd.cn/bff/promotion/coupons/rightCard/detail/v2"

        extra_headers = {
            'biz_from': '1011',
            'biz_scenario': '201',
            'sid': sid,
        }

        params = {
            'cardType': card_type,
            # cardId is optional
            'cardId': card_id,
            'cardNo': card_no
        }

        return await self._base_get_request(url, extra_headers, params)

    async def generate_coupon_share_link(self, sid, coupon_code, coupon_id):
        """
        Generate share link for the coupon
        :param sid: sid
        :param coupon_code: coupon code
        :param coupon_id: coupon id
        :return:
        """
        url = 'https://api.mcd.cn/bff/gift/red/envelop/send'

        extra_headers = {
            'biz_from': '1094',
            'biz_scenario': '300',
            'sid': sid,
        }

        json_data = {
            'headerUrl': 'https://img.mcd.cn/gallery/0fc708ab0912a303.png',
            'nickName': '麦麦',
            'themeType': 0,
            'themeUrl': 'https://img.mcd.cn/ecs/f57d1482ec81ec40.png',
            'type': 1,
            'word': '下一顿麦麦我请你',
            'codes': [
                {
                    'couponCode': coupon_code,
                    'couponId': coupon_id,
                    'type': '1',
                },
            ],
            'themeId': '8',
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def get_shared_coupon_detail(self, sid, uni_code):
        """
        Get shared coupon detail
        :param sid: sid
        :param uni_code: unique share code
        :return:
        """
        url = f'https://api.mcd.cn/bff/gift/gift/detail/{uni_code}'

        extra_headers = {
            'biz_from': '1071',
            'biz_scenario': '300',
            'sid': sid,
            'ct': '31',
            'p': '',
            'tid': '00003TuL'
        }

        return await self._base_get_request(url, extra_headers, {}, uni_code)

    async def grab_shared_coupon(self, sid, red_id, uni_code):
        """
        Grab shared coupon
        :param sid: sid
        :param red_id: red id
        :param uni_code: unique share code
        :return:
        """
        url = 'https://api.mcd.cn/bff/gift/red/envelop/grab'
        extra_headers = {
            'biz_from': '1090',
            'biz_scenario': '300',
            'sid': sid,
            'ct': '31',
            'p': '',
            'tid': '00003TuL'
        }

        json_data = {
            # 'headUrl': 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
            'headUrl': '',
            # 'nickname': '微信用户',
            'redId': red_id,
            'uniCode': uni_code,
        }

        return await self._base_post_request(url, extra_headers, json_data)

    async def get_invite_link(self, sid, task_id):
        """
        Get invite link
        :param sid: sid
        :param task_id: task id
        :return:
        """
        url = f'https://api.mcd.cn/bff/market/invite/task/{task_id}/basic'
        extra_headers = {
            'sid': sid,
            'p': '104',
        }

        # app_version='6.0.62.1' 新版本的签名算法好像变了，如果换成新版本会报签名错误
        return await self._base_get_request(url, extra_headers, None, path=str(task_id))
        
        
    #自己新增的接口
    async def equity_oder(self, sid, city_code, sku_id, pre_commit_no):
        """
        ECMALL make order
        :param sid: sid
        :param city_code: city code
        :param sku_id: sku id
        :param pre_commit_no: pre commit number
        :return: order info
        """
        url = 'https://api.mcd.cn/bff/ectrade/order/create'

        extra_headers = {
            'biz_from': '1021',
            'biz_scenario': '300',
            'sid': sid,
        }

        json_data = {
            "cityCode": str(city_code),
            "scene":1,
            "shopId": 1,
            "gift": False,
            "catType": 1,
            "goods": [{
                "count": 1,
                "skuId": sku_id
            }],
            "giftType": 0,
            "myGiftInfo": {
                "activId": 0
            },
            "secKill": False,
            "preCommitNo": pre_commit_no
        }

        return await self._base_post_request(url, extra_headers, json_data)
    
    #购物车从优惠券页跳过来
    async def add_to_carts(self, sid, store_code,  cart_type, channel_code, be_type, be_code, day_part_code,
                    coupon_id='', coupon_code='', promotion_id='', order_type=1, quantity=1, customization=None, combo_items=None, sub_product_code='', membership_code='', product_type=1, card_type=0, card_id=''):
        """
        Add goods to cart
        :param sid: sid
        :param store_code: store code
        :param product_code: product code
        :param cart_type: cart type
        :param channel_code: channel code
        :param be_type: business type
        :param be_code: business code
        :param day_part_code: day part code
        :param coupon_id: coupon id
        :param coupon_code: coupon code
        :param promotion_id: promotion id
        :param order_type: order type
        :param quantity: quantity
        :param combo_items: comboItems indicates that the product can be customized
        :param sub_product_code: sub product code
        :param membership_code: membership code
        :param product_type: product type 1: normal, 0: membership/card
        :param card_type: card type 0: normal, 2: breakfast card
        :param card_id: card id like breakfast card id CRD09698D2C951AB3B30C57B48EE0FB1F30
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/cart/carts'

        extra_headers = {
            'biz_scenario': '101',
            'biz_from': '1007',
            'sid': sid,
        }

        json_data = {
            'abTest': '562d8ad1-848b-4297-a1ed-38f0ee038433,01a8ee73-95e2-417e-a87e-b19379a19005',
            'beCode': be_code,
            'cartType': str(cart_type),
            'beType': be_type,
            'channelCode': channel_code,
            'storeCode': store_code,
            'maxPurchaseQuantity': 999,
            'orderType': order_type,
            'products': [
                {
                    'rightCardType':0,
                    'couponCode': coupon_code,
                    "pmtPrdReplace": 0,
                    'couponId': coupon_id,
                    # 正常商品0, 早餐卡2
                    'cardType': 0,
                    "useRightCard": 0,
                    'operationType': 0,
                    "price": 0,
                    "alcPrice": 0,
                    "originalPrice": 0,
                    "bogoAbOnly": 0,
                    'promotionId': promotion_id,
                    'quantity': quantity,
                    'sequence': -1,
                },
            ],
             # TODO: 代表什么意思，有时值会为3? 是指从优惠券页跳过来吗？
            'dataSource': 3,
            'daypartCode': str(day_part_code),
            'storeName': '',
        }

        if membership_code != '':
            json_data['products'][0]['membershipCode'] = membership_code

        if customization:
            '''
            # 单选
            {
                "items": [{
                    "code": "200002",
                    "values": [{
                        # 单选，默认checked=1
                        "code": "2",
                    }]
                }],
                "options": []
            }
    
            # 多选
            {
                "items": [],
                "options": [{
                    # 多选，因为默认是checked=1，所以需要把checked=0的都传过来
                    "checked": 0,
                    "code": "100136",
                }]
            }
            '''
            json_data['products'][0].update({
                'customization': customization
            })

        # [
        #     {
        #         "code": "503700",
        #         "round": 1,
        #         "hasCustomization": 1,
        #         "options": [
        #             {
        #                 "code": "100141",
        #                 "checked": 0
        #             },
        #             {
        #                 "code": "100202",
        #                 "checked": 0
        #             }
        #         ]
        #     },
        #     {
        #         "code": "504495",
        #         "round": 2,
        #         "hasCustomization": 0,
        #     },
        #     {
        #         "code": "509303",
        #         "round": 3,
        #         "hasCustomization": 1,
        #         "items": [{
        #             "code": "200002",
        #             "values": [{
        #                 "code": "2",
        #                 "checked": 0
        #             }]
        #         }]
        #     },
        # ]
        if combo_items:
            req_combo_items = []

            for idx, item in enumerate(combo_items):
                combo_item = {}
                if item['hasCustomization']:
                    customization = {}
                    if 'options' in item:
                        customization.update({
                            'options': item['options']
                        })
                    elif 'items' in item:
                        customization.update({
                            'items': item['items']
                        })
                    combo_item.update({
                        'comboProducts': [
                            {
                                "code": item['code'],
                                'customization': customization,
                                'quantity': 1,
                            }
                        ],
                        # 麦满分早餐的饮品round值不是连续的，会跳一个数
                        'round': item['round'] if 'round' in item else idx + 1,
                    })
                else:
                    combo_item.update({
                        'comboProducts': [
                            {
                                'code': item['code'],
                                'quantity': 1,
                            }
                        ],

                        "round": item['round'] if 'round' in item else idx + 1,
                    })
                req_combo_items.append(combo_item)
            json_data['products'][0].update({
                'comboItems': req_combo_items
            })

        return await self._base_put_request(url, extra_headers, json_data, app_version='********')
    async def get_wallet_balance(self, sid):
        """
        获取用户钱包余额
        :param sid: 用户的SID
        :return: 钱包余额信息
        """
        url = 'https://api.mcd.cn/bff/member/mwallet/balance'

        extra_headers = {
            'biz_from': '1001',
            'biz_scenario': '201',
            'sid': sid
        }

        return await self._base_get_request(url, extra_headers, None)

    async def order_confirmation_rightcard(self, sid, request_data):
        """
        订单确认中的会员卡信息
        :param sid: 用户的SID
        :param request_data: 请求数据
        :return: 会员卡信息
        """
        url = 'https://api.mcd.cn/bff/order/confirmation/rightcard'

        extra_headers = {
            'biz_from': '1004',
            'biz_scenario': '200',
            'sid': sid,
        }

        return await self._base_put_request(url, extra_headers, request_data, app_version='********')


