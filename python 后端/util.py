""" Util module provides utility functions for generating fields in MCD request """
import binascii
import json
import random
from hashlib import md5, sha1
from time import time

from Crypto.Cipher import AES
from loguru import logger


class Util:
    """ Util class provides useful functions for generating fields in MCD request """
    is_debug_mode = False

    @staticmethod
    def make_md5(s: str, encoding='utf-8') -> str:
        """
        Make md5 hash from source string
        :param s: source string
        :param encoding: encoding, default to UTF-8
        :return: md5 hash string of source string in hex format
        """
        return md5(s.encode(encoding)).hexdigest()

    @staticmethod
    def make_sha1(s: str, encoding='utf-8') -> str:
        """
        Make sha1 hash for source string
        :param s: source string
        :param encoding: encoding, default to UTF-8
        :return: sha1 hash of source string in hex format
        """
        return sha1(s.encode(encoding)).hexdigest()

    @staticmethod
    def make_st(ts: int) -> str:
        """
        Make st value from timestamp
        :param ts: timestamp
        :return: st value
        """
        return str(int(ts/1000))

    @staticmethod
    def make_nonce(ts: int) -> str:
        """
        Make nonce from timestamp
        :param ts: timestamp
        :return: nonce value
        """
        return f'{str(ts)}{str(random.randint(1, 900000-1) + 100000)}'

    @staticmethod
    def generate_sign(headers: dict, query_params: dict | None, post_body: dict | None, path: str = '', is_android_device: bool = True):
        """
        Generate sign from input params
        :param headers: request headers
        :param query_params: query params
        :param post_body: post body
        :param path: request path
        :param is_android_device: if the device is android
        :return: sign value
        """
        keys = {10: '71d414c0-8fe1-495d-ac55-207414632479', 11: '71d414c0-8fe1-495d-ac55-207414632479', 20: '59b891d7-8d5a-4993-9b16-37d905a16967', 21: 'ad5bac4a-2f99-4728-a7e5-818b5770b559', 30: '3c0860ff-fabe-436a-ba47-94d42669591b', 31: '71d414c0-8fe1-495d-ac55-207414632479', 32: '71d414c0-8fe1-495d-ac55-207414632479', 33: '71d414c0-8fe1-495d-ac55-207414632479', 34: '71d414c0-8fe1-495d-ac55-207414632479', 36: '71d414c0-8fe1-495d-ac55-207414632479', 101: '97741bd7-21bb-4f0d-8b4a-501a089cd208', 102: '97741bd7-21bb-4f0d-8b4a-501a089cd208', 302: '71d414c0-8fe1-495d-ac55-207414632479'}

        r = []

        # 微信小程序及web端是 param_str&header_str&key_str, 而 Android端则是 header_str#param_str#key_str
        separator = '#' if is_android_device else '&'

        ts = int(time() * 1000)
        st = Util.make_st(ts)
        nonce = Util.make_nonce(ts)

        if 'st' in headers:
            st = headers['st']

        if 'nonce' in headers:
            nonce = headers['nonce']

        if Util.is_debug_mode:
            logger.info(f"st: {st}")
            logger.info(f"nonce: {nonce}")

        params_str = ''
        if query_params:
            for i in sorted(query_params.keys()):
                if query_params[i] != '':
                    params_str += f'{i}={query_params[i]}{separator}'

        params_str = params_str[:-1]
        if Util.is_debug_mode:
            logger.info(f"params_str: {params_str}")

        h = ["ct", "language", "ov", "p", "sid", "token", "v", "st", "nonce"]

        header_str = ''
        for i in sorted(h):
            if i in headers and headers[i] != '':
                header_str += f'{i}={headers[i]}{separator}'

        header_str = header_str[:-1]
        if Util.is_debug_mode:
            logger.info(f"header_str: {header_str}")

        post_body_str = ''
        if post_body:
            post_body_str = json.dumps(post_body, separators=(',', ':'), ensure_ascii=False)
        if Util.is_debug_mode:
            logger.info(f"post_body_str: {post_body_str}")

        key = keys[int(headers['ct'])]
        key_str = f'key={key}'

        if is_android_device:
            if header_str != '':
                r.append(header_str)

            if post_body_str != '':
                r.append(post_body_str)

            if params_str != '':
                r.append(params_str)

            if path != '':
                r.append(path)
        else:
            if post_body_str != '':
                r.append(post_body_str)

            if params_str != '':
                r.append(params_str)

            if path != '':
                r.append(path)

            if header_str != '':
                r.append(header_str)

        if key_str != '':
            r.append(key_str)

        raw = separator.join(r)
        if Util.is_debug_mode:
            logger.info(f"raw: {raw}")

        sign = Util.make_md5(raw)
        if Util.is_debug_mode:
            logger.info(f"sign: {sign}")
        return sign

    @staticmethod
    def generate_adaptive_sign(headers: dict, query_params: dict | None, post_body: dict | None, path: str = ''):
        """
        Generate adaptive sign from input params, can adapt to android device and web browser
        :param headers: request headers
        :param query_params: query params
        :param post_body: post body
        :param path: request path
        :return: sign value
        """
        is_android_device = int(headers['ct']) >= 101
        keys = {10: '71d414c0-8fe1-495d-ac55-207414632479', 11: '71d414c0-8fe1-495d-ac55-207414632479', 20: '59b891d7-8d5a-4993-9b16-37d905a16967', 21: 'ad5bac4a-2f99-4728-a7e5-818b5770b559', 30: '3c0860ff-fabe-436a-ba47-94d42669591b', 31: '71d414c0-8fe1-495d-ac55-207414632479', 32: '71d414c0-8fe1-495d-ac55-207414632479', 33: '71d414c0-8fe1-495d-ac55-207414632479', 34: '71d414c0-8fe1-495d-ac55-207414632479', 36: '71d414c0-8fe1-495d-ac55-207414632479', 101: '97741bd7-21bb-4f0d-8b4a-501a089cd208', 102: '97741bd7-21bb-4f0d-8b4a-501a089cd208', 302: '71d414c0-8fe1-495d-ac55-207414632479'}

        r = []

        # 微信小程序及web端是 param_str&header_str&key_str, 而 Android端则是 header_str#param_str#key_str
        separator = '#' if is_android_device else '&'

        ts = int(time() * 1000)
        st = Util.make_st(ts)
        nonce = Util.make_nonce(ts)

        if 'st' in headers:
            st = headers['st']

        if 'nonce' in headers:
            nonce = headers['nonce']

        if Util.is_debug_mode:
            logger.info(f"st: {st}")
            logger.info(f"nonce: {nonce}")

        params_str = ''
        if query_params:
            for i in sorted(query_params.keys()):
                if query_params[i] != '':
                    params_str += f'{i}={query_params[i]}{separator}'

        params_str = params_str[:-1]
        if Util.is_debug_mode:
            logger.info(f"params_str: {params_str}")

        h = ["ct", "language", "ov", "p", "sid", "token", "v", "st", "nonce"]

        header_str = ''
        for i in sorted(h):
            if i in headers and headers[i] != '':
                header_str += f'{i}={headers[i]}{separator}'

        header_str = header_str[:-1]
        if Util.is_debug_mode:
            logger.info(f"header_str: {header_str}")

        post_body_str = ''
        if post_body:
            post_body_str = json.dumps(post_body, separators=(',', ':'), ensure_ascii=False)
        if Util.is_debug_mode:
            logger.info(f"post_body_str: {post_body_str}")

        key = keys[int(headers['ct'])]
        key_str = f'key={key}'

        if is_android_device:
            if header_str != '':
                r.append(header_str)

            if post_body_str != '':
                r.append(post_body_str)

            if params_str != '':
                r.append(params_str)

            if path != '':
                r.append(path)
        else:
            if post_body_str != '':
                r.append(post_body_str)

            if params_str != '':
                r.append(params_str)

            if path != '':
                r.append(path)

            if header_str != '':
                r.append(header_str)

        if key_str != '':
            r.append(key_str)

        raw = separator.join(r)
        if Util.is_debug_mode:
            logger.info(f"raw: {raw}")

        sign = Util.make_md5(raw)
        if Util.is_debug_mode:
            logger.info(f"sign: {sign}")
        return sign

    @staticmethod
    def generate_web_sign(headers: dict, query_params: dict | None, post_body: dict | None, path: str = ''):
        """
        Generate sign from input params for web request
        :param headers: request headers
        :param query_params: query params
        :param post_body: post body
        :param path: request path
        :return: sign value
        """
        return Util.generate_sign(headers, query_params, post_body, path, False)

    @staticmethod
    def aes_ecb(raw_str: str, operation_mode: int = 1):
        """
        Encrypt/decrypt with AES in ECB mode
        :param raw_str: raw string
        :param operation_mode: 1: encrypt 2: decrypt
        :return: encrypt/decrypt result
        """
        if Util.is_debug_mode:
            logger.info(f"raw_str: {raw_str}")
        # the block size for the cipher object; must be 16, 24, or 32 for AES
        BLOCK_SIZE = 16

        # one-liner to sufficiently pad the text to be encrypted
        pad = lambda s: s + (BLOCK_SIZE - len(s) % BLOCK_SIZE) * chr((BLOCK_SIZE - len(s) % BLOCK_SIZE))
        unpad = lambda s: s[0:-ord(s[-1])]

        # generate a random secret key
        secret = b'w8ZJ4wrUl7dDB1A7'

        # create a cipher object using the random secret
        cipher = AES.new(secret, AES.MODE_ECB)

        if operation_mode == 1:
            return binascii.hexlify(cipher.encrypt(pad(raw_str).encode('utf-8'))).decode('utf-8')
        if operation_mode == 2:
            return unpad(cipher.decrypt(binascii.unhexlify(raw_str)).decode('utf-8'))

        raise Exception(f"unsupported type: {operation_mode}")

    @staticmethod
    def generate_random_imei() -> str:
        """
        Generate random IMEI
        :return: random IMEI
        """
        # Generate the first 14 digits of the IMEI number
        imei_prefix = ''.join(str(random.randint(0, 9)) for _ in range(14))

        # Calculate the last digit (the checksum)
        imei_digits = [int(digit) for digit in imei_prefix]
        imei_digits[::2] = [2 * digit for digit in imei_digits[::2]]
        imei_sum = sum(divmod(digit, 10)[0] + divmod(digit, 10)[1] for digit in imei_digits)
        imei_last_digit = (10 - imei_sum % 10) % 10

        # Combine the prefix and the last digit to form the complete IMEI number
        imei_number = imei_prefix + str(imei_last_digit)
        return imei_number


if __name__ == '__main__':
    headers = {
        'biz_scenario': '500',
        'biz_from': '1011',
        'User-Agent': 'mcdonald_Android/******** (Android)',
        'ct': '102',
        'v': '********',
        'p': '102',
        'language': 'cn',
        'traceid': 'b21fc866-a923-4807-ba20-c08fc5a12c5f',
        'st': '1662553540',
        'nonce': '1662553540042381843',
        'sv': 'v3',
        'sign': '3c90c670433e589017b0527d3432764a',
        'tid': '00003TuN',
        'device-id': 'BQuLoOFbMfVK30is464W4mep0iXmkEqlh0b7zcnqGLuxpellGcpLVVvEHvDnUxWEQst3PoCbGho3B3FxlLfk2tg==',
        'Content-Type': 'application/json; charset=UTF-8',
        'Host': 'api.mcd.cn',
    }

    json_data = {
        'tel': '303a581671df2a33eeb438e8210103ca',
        'type': 1,
    }

    sign = Util.generate_sign(headers, None, json_data)
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    headers = {
        'biz_from': '1013',
        'biz_scenario': '103',
        'User-Agent': 'mcdonald_Android/******** (Android)',
        'ct': '102',
        'v': '********',
        'p': '102',
        'sid': 'ffccfbf991589b51c717593d02b0afaa_',
        'language': 'cn',
        'traceid': '5b67dedd-fee9-4786-819a-7fcd0c2fede6',
        'st': '1662643867',
        'nonce': '1662643867297670636',
        'sv': 'v3',
        'sign': 'f4f6dae8b7dd60c200ba9cf2a89d0a83',
        'tid': '00003TuN',
        'device-id': 'BbmobWmUBrz5GTf7U4Wf4yrgGllhvR7O9iQArQe7XVa3xx+n0JUJZ/QQh9QN2LtSN8FhUyVJq2gIdvQAIoRW0+w==',
        'Host': 'api.mcd.cn',
    }

    params = {
        'pageNo': '1',
        'pageSize': '10',
        'cityCode': '110100',
        'keyword': '北苑',
        'hotTagCode': '',
        'beType': '1',
    }

    sign = Util.generate_sign(headers, params, None)
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    headers = {
        'biz_scenario': '101',
        'biz_from': '1004',
        'User-Agent': 'mcdonald_Android/******** (Android)',
        'ct': '102',
        'v': '********',
        'p': '102',
        'sid': 'ffccfbf991589b51c717593d02b0afaa_',
        'language': 'cn',
        'traceid': '627e8690-b816-4bfa-8c7d-c7fb98d13c6f',
        'st': '1662735620',
        'nonce': '1662735620580540880',
        'sv': 'v3',
        'sign': '412ff14c8c0b92f1df27b9fa7b1046bf',
        'tid': '00003TuN',
        'device-id': 'ByHNtWK++92CPwHD/E/51uU/ZKvNdhtgUNTXK4gBsuHEUG9PFlV6VG1W8ZfrNmoUzRHHWF4FPE1x4GkW/nNzXaQ==',
        'Host': 'api.mcd.cn',
    }

    params = {}
    sign = Util.generate_sign(headers, None, None)
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    headers = {
        'biz_scenario': '101',
        'biz_from': '1001',
        'User-Agent': 'mcdonald_Android/******** (Android)',
        'ct': '102',
        'v': '********',
        'p': '102',
        'sid': 'ffccfbf991589b51c717593d02b0afaa_',
        'language': 'cn',
        'traceid': '30957578-a797-4bd7-9ed8-8ed4ed5915a4',
        'st': '1662735657',
        'nonce': '1662735657702762183',
        'sv': 'v3',
        'sign': '4b5de3fce379144d50bdb72f2936bfd5',
        'tid': '00003TuN',
        'device-id': 'ByHNtWK++92CPwHD/E/51uU/ZKvNdhtgUNTXK4gBsuHEUG9PFlV6VG1W8ZfrNmoUzRHHWF4FPE1x4GkW/nNzXaQ==',
        'Content-Type': 'application/json; charset=UTF-8',
        'Host': 'api.mcd.cn',
    }

    json_data = {
        'beCode': '',
        'cardId': '',
        'cartType': '1',
        'channelCode': '03',
        'couponCode': '',
        'couponId': '',
        'daypartCode': '5',
        'hasCard': False,
        'orderType': 1,
        'pageSource': 1,
        'productCode': 'G504960',
        'productPromotions': [],
        'storeCode': '1950181',
        'abtestIds': [
            '562d8ad1-848b-4297-a1ed-38f0ee038433',
            '01a8ee73-95e2-417e-a87e-b19379a19005',
        ],
    }

    sign = Util.generate_sign(headers, None, json_data, 'G504960')
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    headers = {
        'Host': 'api.mcd.cn',
        'sv': 'v3',
        'sid': 'a34b5ff0f4e7ffcba858b8b962314199_',
        'nonce': '1668249420623934448',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P Build/OPM7.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.91 Mobile Safari/537.36mcd-android',
        'st': '1668249421',
        'ct': '102',
        'Accept': 'application/json, text/plain, */*',
        'sign': 'b5ccb287b79b992ddbdfe5f65697a1a5',
        'Origin': 'https://m.mcd.cn',
        'X-Requested-With': 'com.mcdonalds.gma.cn',
        'Sec-Fetch-Site': 'same-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://m.mcd.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
    }

    params = {
        'pageNo': '1',
        'pageSize': '10',
    }

    sign = Util.generate_sign(headers, params, None)
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    headers = {
        'Host': 'api.mcd.cn',
        'Accept': 'application/json, text/plain, */*',
        'st': '1668252484',
        'nonce': '1668252484436909371',
        'ct': '101',
        'Accept-Language': 'en-AU,en;q=0.9',
        # 'Accept-Encoding': 'gzip, deflate, br',
        # Already added when you pass json=
        # 'Content-Type': 'application/json',
        'sv': 'v3',
        'Origin': 'https://m.mcd.cn',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) mcd-ios',
        'Referer': 'https://m.mcd.cn/',
        'sid': '041e7c43d795c50e2067356ca0835d2c_',
        # 'Content-Length': '37',
        'Connection': 'keep-alive',
        'sign': 'aede04998ff77b1a94516b9f437b207a',
    }

    json_data = {
        'couponCode': '80646021967400252202',
    }

    sign = Util.generate_adaptive_sign(headers, None, json_data)
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    headers = {
        'Host': 'api.mcd.cn',
        'sv': 'v3',
        'sid': '5a699373a30e16afcafe24670c8b58d8_',
        'nonce': '1668686237750537449',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P Build/OPM7.181205.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/107.0.5304.105 Mobile Safari/537.36mcd-android',
        'ct': '10',
        'st': '1668686238',
        'Accept': 'application/json, text/plain, */*',
        'sign': '53de4477a38f5803eedaabdb5f9a319d',
        'tid': '00003TuM',
        'Origin': 'https://cdn.mcd.cn',
        'X-Requested-With': 'com.mcdonalds.gma.cn',
        'Sec-Fetch-Site': 'same-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://cdn.mcd.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
    }

    params = {
        'putChannel': '03',
        'pageId': '100895',
        'cityCode': '310100',
        'sid': '5a699373a30e16afcafe24670c8b58d8_',
    }

    sign = Util.generate_adaptive_sign(headers, params, None)
    logger.debug(sign)
    if sign == headers['sign']:
        logger.debug('Bingo!')
    else:
        logger.debug("sign not match")

    res = Util.aes_ecb('18610288148')
    logger.debug(res)

    res = Util.aes_ecb('4eabd38b096a96b1d78785ab77b7d6a7', 2)
    logger.debug(res)

    imei = Util.generate_random_imei()
    logger.debug(imei)
