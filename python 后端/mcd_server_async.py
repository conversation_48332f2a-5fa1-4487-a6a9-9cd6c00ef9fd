"""
MCD server APIs
"""
import json
import sys
import urllib.parse
from datetime import datetime, timezone, timedelta
import time
import redis
from flask import Flask, make_response, request, jsonify
from flask_cors import CORS
from loguru import logger

import mcd_exceptions
from MCD_Web_Async import MC<PERSON><PERSON><PERSON>
from MCD_async import <PERSON><PERSON>sy<PERSON>
from icoke import iCoke
from moji_web_async import <PERSON>ji<PERSON>eb
from proxypool.redisclient import RedisClient, PoolEmptyError
import pymysql


app = Flask(__name__)
CORS(app)

redis_client = redis.StrictRedis(
    host='127.0.0.1',
    port=6379,
    db=1
)

REDIS_ORDER_KEY = 'orders'
DEFAULT_PROXY_ID = 'default'

# 密码
ARCHCARD_PASSWORD = '000999'
ORDER_TYPE_ECTRADE = 'ectrade'
SURVEY_HOST = 'survey.mcd.com.cn'

LOG_LEVEL = "DEBUG"
logger.remove()
logger.add(sys.stderr, level=LOG_LEVEL)

def _get_day_part_code():
    """
    Get day part code
    :return: 1：早餐 05:00:00 ～ 10:29:59 8：午餐 10:30:00 ～ 14:29:59 4：下午茶 14:30:00 ～ 16:59:59 5：夜市 17:00:00 ～ 04:59:59
    """
    local_now = datetime.now(timezone(timedelta(hours=8), name='Asia/Shanghai'))
    local_time = local_now.strftime("%H:%M:%S")
    if '05:00:00' <= local_time < '10:30:00':
        return 1
    if '10:30:00' <= local_time < '14:30:00':
        return 8
    if '14:30:00' <= local_time < '17:00:00':
        return 4

    return 5


async def _get_order_info(sid, pay_id, pay_channel='ALI'):
    """
    Get order info
    :param sid: sid
    :param pay_id: payId
    :param pay_channel: pay channel, accept values are ALI, WX, ARCHCARD
    :return: order info for app payment use
    """
    async with MCDAsync() as client:
        res = await client.prepare_order(sid, pay_id, pay_channel)
    if pay_channel in ['ALI', 'WX'] and res['success'] and res['data']['responseCode'] == 'SUCCESS':
        jobj = json.loads(res['data']['channelPayData'])
        logger.debug(json.dumps(jobj, indent=4, ensure_ascii=False))
        pairs = []
        for k, v in jobj.items():
            encoded_v = urllib.parse.quote_plus(v)
            pairs.append(f'{k}={encoded_v}')

        query_string = '&'.join(pairs)
        logger.debug(query_string)
        # alipay_gw = 'https://openapi.alipay.com/gateway.do'
        return query_string
    return None


@app.route('/store/cities/group', methods=['GET'])
async def get_all_cities():
    """
    Get all cities
    :return: cities data with code and coordinate
    """
    async with MCDAsync() as client:
        return await client.get_all_cities()


@app.route('/', methods=['GET'])
async def getindex():
    return make_response(jsonify({
        'code': 200,
        'errmsg': '首页',
    }), 200)


@app.route('/store/cities', methods=['GET'])
async def get_city_by_coordinate():
    """
    Get current city according to the coordinate
    :return: current city
    """
    lat = request.args.get('lat')
    lng = request.args.get('lng')
    async with MCDAsync() as client:
        return await client.get_city_by_coordinate(lat, lng)


@app.route('/store/stores/vicinity', methods=['GET'])
async def get_nearby_stores():
    """
    Get nearby stores
    :return: nearby stores with code and coordinate
    """
    lat = request.args.get('lat')
    lng = request.args.get('lng')
    show_type = request.args.get('showType', 2)
    be_type = request.args.get('beType', 1)
    order_type = request.args.get('orderType', 1)

    async with MCDAsync() as client:
        return await client.get_nearby_stores(lat, lng, show_type, be_type, order_type)


@app.route('/store/stores', methods=['GET'])
async def search_store_by_keyword():
    """
    Search store by keyword
    :return: stores match the keyword
    """
    city_code = request.args.get('cityCode')
    keyword = request.args.get('keyword')
    page_no = request.args.get('pageNo', 1)
    page_size = request.args.get('pageSize', 10)

    async with MCDAsync() as client:
        return await client.search_stores_by_keyword(city_code, keyword, page_no, page_size)

@app.route('/store/storesGetCityByLocation', methods=['GET'])
async def storesGetCityByLocation():
    """
    Search store by keyword
    :return: stores match the keyword
    """
    city_code = request.args.get('cityCode')
    page_no = request.args.get('pageNo', 1)
    page_size = request.args.get('pageSize', 10)

    async with MCDAsync() as client:
        return await client.cityAcquisitionStore(city_code, page_no, page_size)
@app.route('/store/stores/<store_code>', methods=['GET'])
async def get_store_info(store_code):
    """
    Get store info
    :param store_code: store code
    :return: the store detail
    """
    async with MCDAsync() as client:
        return await client.get_store_info(store_code)


@app.route('/spc/menu', methods=['GET'])
async def get_store_menu():
    """
    Get store menu
    :return: store menu
    """
    store_code = request.args.get('storeCode')
    '''
    到店
    beType=1&beCode=&orderType=1
    外卖
    beType=2&beCode={storeCode}02&orderType=2
    '''
    be_type = request.args.get('beType', 1)
    be_code = request.args.get('beCode', '')
    order_type = request.args.get('orderType', 1)
    day_part_code = request.args.get('dayPartCode')
    if not day_part_code:
        day_part_code = _get_day_part_code()

    async with MCDAsync() as client:
        return await client.get_store_menu(store_code, order_type, be_type, be_code, day_part_code)


@app.route('/spc/products/detail/<product_code>', methods=['GET', 'POST'])
async def get_product_detail(product_code):
    """
    Get product detail
    :param product_code: product code
    :return: product detail
    """
    store_code = request.args.get('storeCode')
    '''
    到店
    orderType=1&beCode=
    外卖
    orderType=2&beCode={store_code}02
    '''
    order_type = request.args.get('orderType', 1)
    be_code = request.args.get('beCode', '')
    day_part_code = request.args.get('dayPartCode')
    if not day_part_code:
        day_part_code = _get_day_part_code()
    channel_code = request.args.get('channelCode', '03')

    async with MCDAsync() as client:
        return await client.get_products_detail(store_code, product_code, channel_code, order_type, be_code, day_part_code)


@app.route('/promotion/coupons/rightCards', methods=['GET'])
async def get_user_coupons():
    """
    Get user coupons
    :return: user coupons
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_user_coupons(sid)


@app.route('/promotion/coupons/v2', methods=['GET'])
async def get_available_coupons():
    """
    Get available coupons
    :return: user's available coupons
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_available_coupons(sid)


@app.route('/promotion/coupons/card', methods=['GET'])
async def get_coupon_card():
    """
    Get coupon card
    :return: coupon card
    """
    store_code = request.args.get('storeCode')
    be_type = request.args.get('beType', 1)
    be_code = request.args.get('beCode', '')
    order_type = request.args.get('orderType', 1)
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_coupon_card(sid, store_code, be_type, be_code, order_type)


@app.route('/promotion/coupons/products', methods=['GET'])
async def get_coupon_product():
    """
    Get coupon product
    :return: the product that the coupon can be used on
    """
    coupon_id = request.args.get('couponId')
    coupon_code = request.args.get('couponCode')
    promotion_id = request.args.get('promotionId')
    store_code = request.args.get('storeCode')
    order_type = request.args.get('orderType', 1)
    be_code = request.args.get('beCode', '')
    daypart_codes = request.args.get('daypartCodes')
    if not daypart_codes:
        daypart_codes = _get_day_part_code()
    date = request.args.get('date')
    time = request.args.get('time')

    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_coupon_product(sid, coupon_id, coupon_code, promotion_id, store_code, order_type, be_code, daypart_codes, date, time)

@app.route('/promotion/coupons/<coupon_code>', methods=['GET'])
async def get_coupon_detail(coupon_code):
    """
    Get coupon detail
    :return: coupon detail
    """
    coupon_id = request.args.get('couponId')
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_coupon_detail(sid, coupon_id, coupon_code)


@app.route('/cart/carts', methods=['PUT'])
async def add_to_cart():
    """
    Add products to cart
    :return: success or fail
    """
    req_body = request.json
    store_code = req_body.get('storeCode')
    product_code = req_body.get('productCode')
    sub_product_code = req_body.get('subProductCode', '')
    coupon_code = req_body.get('couponCode', '')
    coupon_id = req_body.get('couponId', '')
    promotion_id = req_body.get('promotionId', '')
    cart_type = req_body.get('cartType', 1)
    channel_code = req_body.get('channelCode', '03')
    '''
    到店
    beType=1&beCode=&orderType=1

    外卖
    beType=2&beCode={storeCode}02&orderType=2
    '''
    be_type = req_body.get('beType', 1)
    be_code = req_body.get('beCode', '')
    order_type = req_body.get('orderType', 1)
    day_part_code = req_body.get('dayPartCode')
    if not day_part_code:
        day_part_code = _get_day_part_code()
    quantity = req_body.get('quantity', 1)
    membership_code = req_body.get('membershipCode', '')
    product_type = req_body.get('productType', 1)
    card_type = req_body.get('cardType', 0)
    card_id = req_body.get('cardId', '')
    if membership_code != '':
        product_type = 0

    sid = req_body.get('debug_sid')
    customization = req_body.get('customization', [])
    combo_items = req_body.get('comboItems', [])

    async with MCDAsync() as client:
        return await client.add_to_cart(sid, store_code=store_code, product_code=product_code, cart_type=cart_type,
                                        channel_code=channel_code, be_type=be_type, be_code=be_code, day_part_code=day_part_code,
                                        coupon_code=coupon_code, coupon_id=coupon_id, promotion_id=promotion_id, order_type=order_type,
                                        quantity=quantity, customization=customization, combo_items=combo_items, sub_product_code=sub_product_code,
                                        membership_code=membership_code, product_type=product_type, card_type=card_type, card_id=card_id)
                                        


@app.route('/cart/carts/empty', methods=['PUT'])
async def empty_cart():
    """
    Empty cart
    :return: success or fail
    """
    req_body = request.json
    store_code = req_body.get('storeCode')
    cart_type = req_body.get('cartType', 1)
    channel_code = req_body.get('channelCode', '03')
    be_type = req_body.get('beType', 1)
    be_code = req_body.get('beCode', '')
    day_part_code = req_body.get('dayPartCode')
    if not day_part_code:
        day_part_code = _get_day_part_code()
    order_type = req_body.get('orderType', 1)

    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        return await client.empty_cart(sid, store_code=store_code, cart_type=cart_type, channel_code=channel_code, be_type=be_type,
                             be_code=be_code, day_part_code=day_part_code, order_type=order_type)

@app.route('/order/confirmation/validationinfo', methods=['POST'])
async def get_cart_validation_info():
    """
    Get cart validation info
    :return:
    """
    req_body = request.json
    store_code = req_body.get('storeCode')
    cart_type = req_body.get('cartType', 1)
    channel_code = req_body.get('channelCode', '03')
    day_part_code = req_body.get('dayPartCode')
    if not day_part_code:
        day_part_code = _get_day_part_code()
    '''
    到店
    beType=1&beCode=&orderType=1

    外卖
    beType=2&beCode={storeCode}02&orderType=2
    '''
    be_code = req_body.get('beCode', '')
    order_type = req_body.get('orderType', 1)
    address_id = req_body.get('addressId')
    if int(order_type) == 2 and address_id is None:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '外卖单需要提供外送地址信息',
        }), 200)

    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_cart_validation_info(sid, store_code, channel_code, order_type, cart_type, day_part_code, be_code=be_code, address_id=address_id)

@app.route('/bff/order/confirmation/rightcard', methods=['PUT'])
async def order_confirmation_rightcard():
    """
    订单确认中的会员卡信息
    :return: 会员卡信息
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    
    if not sid:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '缺少用户会话信息',
        }), 200)
    
    # 移除debug_sid，避免传递给真实API
    request_data = {k: v for k, v in req_body.items() if k != 'debug_sid'}
    
    async with MCDAsync() as client:
        return await client.order_confirmation_rightcard(sid, request_data)

@app.route('/order/orders', methods=['POST'])
async def make_order():
    """
    Make order
    :return: order info
    """
    req_body = request.json
    store_code = req_body.get('storeCode')
    cart_type = req_body.get('cartType', 1)
    channel_code = req_body.get('channelCode', '03')
    day_part_code = req_body.get('dayPartCode')
    if not day_part_code:
        day_part_code = _get_day_part_code()
    '''
    到店
    beType=1&beCode=&orderType=1

    外卖
    beType=2&beCode={storeCode}02&orderType=2
    '''
    be_code = req_body.get('beCode', '')
    be_type = req_body.get('beType', 1)
    order_type = req_body.get('orderType', 1)
    cash_coupon_code = req_body.get('cashCouponCode', '')
    cash_coupon_id = req_body.get('cashCouponId', '')
    # valid eatType ["eat-in", "take-in-store", "take-in-dt"]
    eat_type = req_body.get('eatType', 'take-in-store')
    address_id = req_body.get('addressId')
    proxy_id = req_body.get('proxyId', DEFAULT_PROXY_ID)
    if int(order_type) == 2 and address_id is None:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '外卖单需要提供外送地址信息',
        }), 200)

    sid = req_body.get('debug_sid')
    is_balance_pay = int(req_body.get('payByBalance', 0))

    async with MCDAsync() as client:
        resp = await client.get_cart_validation_info(sid, store_code, channel_code, order_type, cart_type, day_part_code, be_code=be_code, address_id=address_id)
        if resp['success']:
            valid_info = resp['data']
            resp = await client.make_order(sid, valid_info, store_code, order_type, day_part_code, be_type, be_code, eat_type, cash_coupon_code, cash_coupon_id, address_id)
            '''
            {
                "code": 200,
                "data": {
                    "eatTypeCode": "take-in-store",
                    "orderId": "1030290620000220043715819563",
                    "orderStatus": "1",
                    "payId": "11509837244272816128"
                },
                "datetime": "2022-11-07 21:11:11",
                "message": "请求成功",
                "success": true
            }
            '''
            if resp['success']:
                logger.debug(f"orderId: {resp['data']['orderId']}")
                logger.debug(f"payId: {resp['data']['payId']}")
                logger.debug(f"payByBalance: {is_balance_pay}")
            if resp['success'] and not is_balance_pay:
                key = f'{REDIS_ORDER_KEY}_{proxy_id}'
                logger.debug(f"save order info into redis with key: {key}, orderId: {resp['data']['orderId']}")
                redis_client.rpush(key, json.dumps({
                    'sid': sid,
                    'orderId': resp['data']['orderId'],
                    'payId': resp['data']['payId']
                }))

        return resp

@app.route('/order/orders2', methods=['POST'])
async def make_order_directly():
    """
    Make order directly
    :return: order info
    """
    req_body = request.json
    cart_items = req_body.get('cartItems')
    real_total_amount = req_body.get('realTotalAmount')
    promotion_list = req_body.get('promotionList', None)
    store_code = req_body.get('storeCode')
    day_part_code = _get_day_part_code()
    '''
    到店
    beType=1&beCode=&orderType=1

    外卖
    beType=2&beCode={storeCode}02&orderType=2
    '''
    be_code = req_body.get('beCode', '')
    be_type = req_body.get('beType', 1)
    order_type = req_body.get('orderType', 1)
    cash_coupon_code = req_body.get('cashCouponCode', '')
    cash_coupon_id = req_body.get('cashCouponId', '')
    # valid eatType ["eat-in", "take-in-store", "take-in-dt"]
    eat_type = req_body.get('eatType', 'take-in-store')
    address_id = req_body.get('addressId')
    real_delivery_price = req_body.get('realDeliveryPrice')
    delivery_time = req_body.get('deliveryTime')
    card_id = req_body.get('cardId', '')
    proxy_id = req_body.get('proxyId', DEFAULT_PROXY_ID)
    if int(order_type) == 2 and address_id is None:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '外卖单需要提供外送地址信息',
        }), 200)

    sid = req_body.get('debug_sid')
    is_balance_pay = int(req_body.get('payByBalance', 0))

    async with MCDAsync() as client:
        resp = await client.make_order_directly(sid, cart_items, real_total_amount, store_code, order_type, day_part_code, be_type, be_code, eat_type, cash_coupon_code, cash_coupon_id, address_id, delivery_time, real_delivery_price, promotion_list=promotion_list, card_id=card_id)
        if resp['success']:
            logger.debug(f"orderId: {resp['data']['orderId']}")
            logger.debug(f"payId: {resp['data']['payId']}")
            logger.debug(f"payByBalance: {is_balance_pay}")
            if not is_balance_pay:
                key = f'{REDIS_ORDER_KEY}_{proxy_id}'
                logger.debug(f"save order info into redis with key: {key}, orderId: {resp['data']['orderId']}")
                redis_client.rpush(key, json.dumps({
                    'sid': sid,
                    'orderId': resp['data']['orderId'],
                    'payId': resp['data']['payId']
                }))
                # //mysql存储
                con = pymysql.connect(
                        host ='127.0.0.1',
                        port = 3306,
                        user = 'pythonRd',
                        password ='mARbS8f75x6HJzJY',
                        database = 'pythonrd'
                    )
                cur = con.cursor()
                # 获取时间戳
                timestamp = int(time.time() * 1000)
                sqlinset = f"INSERT INTO pythonrd.oders ( sid, orderId, payId, proxy_id,timestamp) VALUES ('{sid}','{resp['data']['orderId']}','{resp['data']['payId']}','{proxy_id}','{timestamp}')"
                logger.debug(f"sql: {sqlinset}")
                cur.execute(sqlinset)
                con.commit()
                cur.close()
                con.close()
                
        return resp

@app.route('/ectrade/order/create', methods=['POST'])
async def ecmall_make_order():
    """
    ECMALL Make order
    :return: order info
    """
    req_body = request.json
    city_code = req_body.get('cityCode')
    sku_id = req_body.get('skuId')
    sid = req_body.get('debug_sid')
    proxy_id = req_body.get('proxyId', DEFAULT_PROXY_ID)

    async with MCDAsync() as client:
        res = await client.ecmall_add_to_cart(sid, city_code, sku_id)
        if res and res['success']:
            pre_commit_no = res['data']['detail']['preCommitNo']
            resp = await client.ecmall_make_order(sid, city_code, sku_id, pre_commit_no)
            if resp['success']:
                key = f'{REDIS_ORDER_KEY}_{proxy_id}'
                logger.debug(f"save ecmall order info into redis with key: {key}, orderId: {resp['data']['orderId']}")
                redis_client.rpush(key, json.dumps({
                    'sid': sid,
                    'orderId': resp['data']['orderId'],
                    'payId': resp['data']['payId'],
                    'orderType': ORDER_TYPE_ECTRADE,
                }))
            return resp

    return make_response(jsonify({
        'code': -1,
        'errmsg': "创建商城订单失败!"
    }))

@app.route('/cashier/preorder', methods=['POST'])
async def prepare_order():
    """
    Prepare order for payment
    :return: payment order info which can be used to jump to third payment like ALIPAY or WX
    """
    req_body = request.json
    pay_id = req_body.get('payId')
    # ALI, WX or ARCHCARD(余额支付)
    pay_channel = req_body.get('payChannel', 'ALI')

    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        res = await client.prepare_order(sid, pay_id, pay_channel)
        if pay_channel in ['ALI', 'WX'] and res['success'] and res['data']['responseCode'] == 'SUCCESS':
            jobj = json.loads(res['data']['channelPayData'])
            logger.debug(json.dumps(jobj, indent=4, ensure_ascii=False))
            pairs = []
            for k, v in jobj.items():
                encoded_v = urllib.parse.quote_plus(v)
                pairs.append(f'{k}={encoded_v}')

            query_string = '&'.join(pairs)
            logger.debug(query_string)
            # alipay_gw = 'https://openapi.alipay.com/gateway.do'
            res['data']['orderInfo'] = query_string
        return res


@app.route('/order/orders/<order_id>', methods=['GET'])
async def get_order_detail(order_id):
    """
    Get order detail
    :param order_id: order id
    :return: order detail
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_order_detail(sid, order_id)

@app.route('/ectrade/order/detail', methods=['GET'])
async def get_ectrade_order_detail(order_id):
    """
    Get ecmall order detail
    :param order_id: order id
    :return: order detail
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_ectrade_order_detail(sid, order_id)


@app.route('/order/orders/<order_id>/cancellation', methods=['PUT'])
async def cancel_order(order_id):
    """
    Cancel order
    :param order_id: order id
    :return: success or fail
    """
    req_body = request.json
    cancel_reason_code = req_body.get('cancelReasonCode', '1')

    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        return await client.cancel_order(sid, order_id, cancel_reason_code)



@app.route("/payLink",methods=['POST'])
async def getLink():
    req_body = request.json
    sid = req_body.get('sid')
    pay_id = req_body.get('payid')
    order_info = await _get_order_info(sid, pay_id)
    return make_response(jsonify({
                'code': 1,
                'errmsg': '',
                'data': order_info,
            }), 200)



@app.route('/order', methods=['GET'])
async def get_order():
    """
    Get order from pool
    :return: order info for payment
    """
    proxy_id = request.args.get('proxy_id', DEFAULT_PROXY_ID)
    if len(proxy_id) == 0:
        proxy_id = DEFAULT_PROXY_ID

    async with MCDAsync() as client:
        res = None
        while True:
            key = f'{REDIS_ORDER_KEY}_{proxy_id}'
            logger.debug(f"retrieve order info from redis with key: {key}")
            order = redis_client.lpop(key)
            if order is None:
                break

            order = json.loads(order)
            logger.debug(json.dumps(order, indent=4, ensure_ascii=False))
            sid = order['sid']
            order_id = order['orderId']
            pay_id = order['payId']
            if 'orderType' in order and order['orderType'] == ORDER_TYPE_ECTRADE:
                resp = await client.get_ectrade_order_detail(sid, order_id)
                order_status = resp['data']['orderStatus']
            else:
                resp = await client.get_order_detail(sid, order_id)
                order_status = resp['data']['orderStatusCode']
            if int(order_status) == 1:
                # 待支付
                order_info = await _get_order_info(sid, pay_id)
                if order_info:
                    res = {
                        'sid': sid,
                        'order_id': order_id,
                        'pay_id': pay_id,
                        'order_info': order_info
                    }
                    logger.debug(json.dumps(res, indent=4, ensure_ascii=False))
                    break

        if res:
            return make_response(jsonify({
                'code': 0,
                'errmsg': '',
                'data': res,
            }), 200)

        return make_response(jsonify({
            'code': -1,
            'errmsg': '没有可用订单!',
            'data': {},
        }), 400)


@app.route('/return_order', methods=['POST'])
async def return_order_to_pool():
    """
    Return order to the pool
    :return: success or fail
    """
    req_body = request.json
    sid = req_body.get('sid')
    order_id = req_body.get('orderId')
    pay_id = req_body.get('payId')
    proxy_id = req_body.get('proxyId', DEFAULT_PROXY_ID)
    
     # //mysql存储
    con = pymysql.connect(
            host ='127.0.0.1',
            port = 3306,
            user = 'pythonRd',
            password ='mARbS8f75x6HJzJY',
            database = 'pythonrd'
        )
    cur = con.cursor()
    # 获取时间戳
    timestamp = int(time.time() * 1000)
    sqlinset = f"SELECT * FROM pythonrd.oders WHERE orderId = {order_id}"
    
    cur.execute(sqlinset)
    rest = cur.fetchall()
    logger.debug(f"retoder:{rest}")
    con.commit()
    cur.close()
    con.close()
    
    

    async with MCDAsync() as client:
        resp = await client.get_order_detail(sid, order_id)
        if int(resp['data']['orderStatusCode']) == 1:
            key = f'{REDIS_ORDER_KEY}_{proxy_id}'
            logger.debug(f"return the order to redis with key: {key}, orderId: {order_id}")
            redis_client.rpush(key, json.dumps({
                'sid': sid,
                'orderId': order_id,
                'payId': pay_id
            }))

        return make_response(jsonify({
            'code': 0,
            'errmsg': '',
        }), 200)


@app.route('/user/portal/info', methods=['GET'])
async def get_user_info():
    """
    Get user info and wallet balance
    :return: user info with wallet balance
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        # Get user info
        user_info = await client.get_user_info(sid)
        
        # Get wallet balance
        balance_info = await client.get_wallet_balance(sid)
        
        # Combine the responses（调整：直接提取balance字段）
        if user_info and user_info.get('success'):
            # 若余额接口成功且存在balance字段，则提取；否则置为None
            user_info['data']['walletBalance'] = balance_info['data']['balance'] if (balance_info and balance_info.get('success') and 'balance' in balance_info['data']) else None
                
        return user_info

@app.route('/coupon/exchanges/<coupon_code>', methods=['GET'])
async def get_exchange_coupon_detail(coupon_code):
    """
    Get exchange/cash coupon detail
    :param coupon_code: coupon code
    :return: the coupon detail
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_exchange_coupon_detail(sid, coupon_code)


@app.route('/coupon/exchanges', methods=['POST'])
async def do_exchange_coupon():
    """
    Do exchange coupon
    :return: success or fail
    """
    req_body = request.json
    coupon_code = req_body.get('couponCode')

    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        return await client.do_exchange_coupon(sid, coupon_code)


@app.route('/coupon/exchanges/history', methods=['GET'])
async def get_exchange_coupon_history():
    """
    Get exchange coupon history
    :return: exchange coupon history
    """
    sid = request.args.get('debug_sid')
    page_no = request.args.get('pageNo', 1)
    page_size = request.args.get('pageSize', 10)

    async with MCDAsync() as client:
        return await client.get_exchange_coupon_history(sid, page_no, page_size)


@app.route('/member/coupon/bind', methods=['GET'])
async def take_membership_coupons():
    """
    Take membership coupons
    :return: success or fail
    """
    sid = request.args.get('debug_sid')
    channel_code = request.args.get('channelCode', '03')
    city_code = request.args.get('cityCode')
    page_id = request.args.get('pageId')

    async with MCDAsync() as client:
        return await client.take_membership_coupons(sid, city_code, channel_code, page_id)


@app.route('/market/lottery/point/draw', methods=['GET'])
async def draw_lottery():
    """
    Draw lottery
    :return: lottery result
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.draw_lottery(sid)

@app.route('/myrewards/home/<USER>', methods=['GET'])
async def points_products():
    """
    Points exchangeable products
    :return: lottery result
    """
    sid = request.args.get('debug_sid')
    city_code = request.args.get('cityCode')

    async with MCDAsync() as client:
        return await client.points_products(sid, city_code)

@app.route('/points/exchange', methods=['POST'])
async def points_exchange():
    """
    Points exchange
    :return: exchange result
    """
    req_body = request.json
    city_code = req_body.get('cityCode')
    product_id = req_body.get('productId')
    shop_id = req_body.get('shopId', 2)

    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        res = await client.points_product_sku_id(sid, product_id, shop_id, city_code)
        if res and 'defaultSkuId' in res['data']:
            sku_id = res['data']['defaultSkuId']
            res = await client.points_product_pre_commit_no(sid, product_id)
            if res and 'preCommitNo' in res['data']:
                pre_commit_no = res['data']['preCommitNo']
                return await client.points_product_make_order(sid, shop_id, sku_id, pre_commit_no, city_code)

    return make_response(jsonify({
        'code': -1,
        'errmsg': '兑换失败',
    }), 200)


@app.route('/member/addresses', methods=['POST'])
async def add_new_address():
    """
    Add new address for delivery
    :return: generated address id
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    address_info = req_body.get('address_info')
    contact_info = req_body.get('contact_info')

    async with MCDAsync() as client:
        return await client.add_new_address(sid, address_info, contact_info)


@app.route('/member/addresses/<address_id>', methods=['PUT'])
async def update_address(address_id):
    """
    Update existing address info
    :param address_id: address id
    :return: updated address
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    address_info = req_body.get('address_info')
    contact_info = req_body.get('contact_info')

    async with MCDAsync() as client:
        return await client.update_address(sid, address_id, address_info, contact_info)


@app.route('/member/addresses', methods=['GET'])
async def get_addresses():
    """
    Get saved addresses
    :return: addresses
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_addresses(sid)

@app.route('/msg/box/list', methods=['GET'])
async def get_msgbox_list():
    """
    Get message box list
    :return: messages
    """
    sid = request.args.get('debug_sid')
    page_no = request.args.get('pageNo', 1)
    page_size = request.args.get('pageSize', 50)
    category = request.args.get('category', 1)

    async with MCDAsync() as client:
        return await client.get_msgbox_list(sid, page_no, page_size, category)

@app.route('/survey/do', methods=['POST'])
async def do_survey():
    """
    Fill the survey
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    survey_url = req_body.get('surveyUrl')

    if len(survey_url) == 0:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '问卷地址不能为空',
        }), 200)

    parse_result = urllib.parse.urlparse(survey_url)
    if parse_result.netloc != SURVEY_HOST:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '问卷地址有误，请检查后重新提交',
        }), 200)
    try:
        res = await MCDAsync.do_survey(sid, survey_url)
        if res is not None:
            return res
    except mcd_exceptions.SurveyExpired:
        return make_response(jsonify({
            'code': -1,
            'errmsg': "问卷已过期"
        }), 200)
    except mcd_exceptions.SurveyCompleted:
        return make_response(jsonify({
            'code': -1,
            'errmsg': "问卷已填写"
        }), 200)
    except Exception as e:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '填写问卷失败',
        }), 200)

@app.route('/survey/coupons', methods=['GET'])
async def get_survey_coupons():
    """
    Get available coupons for the survey
    :return: success/fail response
    """
    sid = request.args.get('debug_sid')
    survey_id = request.args.get('surveyId')

    res = await MCDAsync.get_survey_coupons(sid, survey_id)
    return make_response(jsonify({
        'code': 0,
        'errmsg': '',
        'data': res
    }), 200)

@app.route('/coupon/survey', methods=['POST'])
async def take_survey_coupons():
    """
    Take survey coupons
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    survey_id = req_body.get('surveyId')
    coupon_id = req_body.get('couponId')
    page_id = req_body.get('pageId')
    order_id = req_body.get('orderId')

    async with MCDAsync() as client:
        return await client.take_survey_coupon(sid, order_id, page_id, survey_id, coupon_id)

@app.route('/gift/red/envelop/send', methods=['POST'])
async def generate_coupon_share_link():
    """
    Generate share link for the coupon
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    coupon_code = req_body.get('couponCode')
    coupon_id = req_body.get('couponId')

    async with MCDAsync() as client:
        return await client.generate_coupon_share_link(sid, coupon_code, coupon_id)

@app.route('/gift/gift/detail/<uni_code>', methods=['GET'])
async def get_shared_coupon_detail(uni_code):
    """
    Get shared coupon detail
    :return: success/fail response
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_shared_coupon_detail(sid, uni_code)

@app.route('/gift/red/envelop/grab', methods=['POST'])
async def grab_shared_coupon():
    """
    Grab shared coupon
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    red_id = req_body.get('redId')
    uni_code = req_body.get('uniCode')

    async with MCDAsync() as client:
        return await client.grab_shared_coupon(sid, red_id, uni_code)

@app.route('/cashier/archcard', methods=['POST'])
async def pay_by_archcard():
    """
    Pay by ARCHCARD
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    pay_id = req_body.get('payId')
    amount = req_body.get('amount', '')

    async with MCDAsync() as client:
        res = await client.get_user_info(sid)
        if 'data' in res and 'meddyId' in res['data']:
            user_id = res['data']['meddyId']
        else:
            return make_response(jsonify({
                'code': -1,
                'errmsg': '获取用户id失败',
            }), 200)

        res = await client.prepare_order(sid, pay_id, 'ARCHCARD')
        pay_data = json.loads(res['data']['channelPayData'])
        trans_id, tn = pay_data['transId'], pay_data['tn']

        res = await client.archcard_pay_init(user_id, sid)
        if 'code' in res and res['code'] == '0000':
            res = await client.archcard_pay_verify(user_id, sid, amount, trans_id, tn)
        else:
            return res

        if 'responseCode' in res and res['responseCode'] == '0000':
            res = await client.archcard_pay_with_pwd(user_id, sid, trans_id, ARCHCARD_PASSWORD)

        return res

@app.route('/passport/verifyCode/send', methods=['POST'])
async def send_sms():
    """
    Send SMS
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    verify_code_type = req_body.get('type', '1')
    region_code = req_body.get('regionCode', '')
    proxy = req_body.get('proxy')

    async with MCDWeb(proxy) as client:
        return await client.send_sms_as_app(mobile, verify_code_type, region_code)

@app.route('/passport/login/mobile', methods=['POST'])
async def login():
    """
    Login with SMS code
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    sms_code = req_body.get('code')
    region_code = req_body.get('regionCode', '')
    proxy = req_body.get('proxy')

    async with MCDWeb(proxy) as client:
        return await client.login_as_app(mobile, sms_code, region_code)

@app.route('/passport/login/refresh', methods=['POST'])
async def login_refresh():
    """
    Login refresh
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        return await client.login_refresh(sid)

@app.route('/passport/login/logout', methods=['POST'])
async def logout():
    """
    Logout
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        return await client.logout(sid)

@app.route('/member/user/mobile/change', methods=['POST'])
async def change_mobile():
    """
    Change mobile number
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    old_mobile = req_body.get('oldMobile')
    sms_code = req_body.get('code')
    new_mobile = req_body.get('newMobile')

    async with MCDAsync() as client:
        return await client.change_mobile(sid, old_mobile, sms_code, new_mobile)

@app.route('/member/user/edit', methods=['POST'])
async def edit_profile():
    """
    Edit user profile
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    birthday = req_body.get('birthday')

    async with MCDAsync() as client:
        return await client.edit_profile(sid, birthday)

@app.route('/member/login/send/sms', methods=['POST'])
async def web_send_sms():
    """
    Send SMS
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')

    async with MCDWeb() as client:
        return await client.send_sms(mobile)

@app.route('/member/login/mobile', methods=['POST'])
async def web_login():
    """
    Login with SMS code
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    sms_code = req_body.get('code')

    async with MCDWeb() as client:
        return await client.login(mobile, sms_code)

@app.route('/market/app/gift/newReceive', methods=['POST'])
async def take_new_user_gift():
    """
    Take new user gift
    :return: success/fail response
    """
    req_body = request.json
    sid = req_body.get('debug_sid')
    city_code = req_body.get('city_code')
    page_id = req_body.get('page_id')
    if page_id is None:
        return make_response(jsonify({
            'code': -1,
            'errmsg': '缺少活动id参数',
        }), 200)

    async with MCDWeb() as client:
        res = await client.get_coupon_table(sid, city_code, page_id)
        modules = [group['modules'] for group in res['data']['groups'] if 'coupon' in group['groupTag']]
        modules = modules[0] if len(modules) > 0 else []
        coupons = [module['coupons'] for module in modules if 'coupon' in module['moduleId']]
        coupons = coupons[0] if len(coupons) > 0 else []
        coupons = [{'couponId': coupon['couponId'], 'receiveQuantity': coupon['receiveQuantity']} for coupon in coupons if
                   coupon['label'] is None]
        if len(coupons) == 0:
            return make_response(jsonify({
                'code': -1,
                'errmsg': '没有新人券可领',
            }), 200)

        logger.debug(json.dumps(coupons, indent=4, ensure_ascii=False))

        return await client.take_new_user_gift(sid, coupons, page_id)

@app.route('/myrewards/benefit/use/detail/<benefit_id>', methods=['GET'])
async def get_benefit_use_detail(benefit_id):
    """
    Get benefit use detail
    :return: benefit use detail
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_benefit_use_detail(sid, benefit_id)

@app.route('/myrewards/benefit/introduce/detail/<benefit_id>', methods=['GET'])
async def get_benefit_detail(benefit_id):
    """
    Get benefit use detail
    :return: benefit detail
    """
    sid = request.args.get('debug_sid')
    city_code = request.args.get('cityCode')

    async with MCDAsync() as client:
        return await client.get_benefit_detail(sid, benefit_id, city_code)

@app.route('/order/orders', methods=['GET'])
async def get_orders():
    """
    Get orders
    :return: orders
    """
    sid = request.args.get('debug_sid')
    order_category_id = request.args.get('orderCategoryId', 0)
    page = request.args.get('page', 1)

    async with MCDAsync() as client:
        return await client.get_orders(sid, order_category_id, page)

@app.route('/promotion/coupons/rightCard/detail/v2', methods=['GET'])
async def get_right_card_detail():
    """
    Get right card detail
    :return: right card detail
    """
    sid = request.args.get('debug_sid')
    card_type = request.args.get('card_type')
    card_id = request.args.get('card_id')
    card_no = request.args.get('card_no')

    async with MCDAsync() as client:
        return await client.get_right_card_detail(sid, card_type, card_id, card_no)

@app.route('/icoke/external/sms/vf', methods=['POST'])
async def icoke_send_sms():
    """
    iCoke send SMS
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')

    client = iCoke()
    try:
        return await client.send_sms(mobile)
    finally:
       return await client.close()

@app.route('/icoke/mc/coupon/login', methods=['POST'])
async def icoke_login_with_sms():
    """
    iCoke login with SMS code
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    sms_code = req_body.get('code')

    client = iCoke()
    try:
        return await client.login_with_sms(mobile, sms_code)
    finally:
        await client.close()

@app.route('/icoke/mc/coupon/receive', methods=['POST'])
async def icoke_receive_coupon():
    """
    iCoke receive coupon
    :return: success/fail response
    """
    req_body = request.json
    token = req_body.get('token')

    client = iCoke()
    try:
        return await client.receive_coupon(token)
    finally:
        await client.close()

@app.route('/moji/cola2023/getCode', methods=['POST'])
async def moji_cola_get_code():
    """
    Moji cola send SMS
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    city_id = req_body.get('cityId')


    async with MojiWeb() as client:
        return await client.get_code(mobile, city_id)

@app.route('/moji/cola2023/raffle', methods=['POST'])
async def moji_cola_raffle():
    """
    Moji cola raffle with SMS code
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    sms_code = req_body.get('code')
    city_id = req_body.get('cityId')
    sns_id = req_body.get('snsId')

    async with MojiWeb() as client:
        return await client.raffle(mobile, sms_code, city_id, sns_id)

@app.route('/moji/cola2023/userCoupon', methods=['POST'])
async def moji_cola_user_coupon():
    """
    Moji cola get user coupon
    :return: success/fail response
    """
    req_body = request.json
    mobile = req_body.get('mobile')
    sns_id = req_body.get('snsId')
    city_id = req_body.get('cityId')

    async with MojiWeb() as client:
        return await client.user_coupon(mobile, sns_id, city_id)

@app.route('/market/invite/task/<task_id>/basic', methods=['GET'])
async def get_invite_link(task_id):
    """
    Get app download invite link
    :param task_id: task id
    :return: invite detail
    """
    sid = request.args.get('debug_sid')

    async with MCDAsync() as client:
        return await client.get_invite_link(sid, task_id)

@app.route('/proxy', methods=['GET'])
def get_proxy():
    """
    Get proxy
    :return:
    """
    try:
        r = RedisClient()
        proxy = r.random()
        return make_response(jsonify({
            'code': 0,
            'data': {
                'ip': proxy
            }
        }), 200)
    except PoolEmptyError:
        return make_response(jsonify({
                'code': -1,
                'errmsg': '没有可用代理',
            }), 200)


#自己新增接口 
@app.route('/ectrade/order/equity', methods=['POST'])
async def equity_oder():
    """
    ECMALL Make order
    :return: order info
    """
    req_body = request.json
    city_code = req_body.get('cityCode')
    sku_id = req_body.get('skuId')
    sid = req_body.get('debug_sid')

    async with MCDAsync() as client:
        res = await client.ecmall_add_to_cart(sid, city_code, sku_id)
        if res and res['success']:
            pre_commit_no = res['data']['detail']['preCommitNo']
            return await client.equity_oder(sid, city_code, sku_id, pre_commit_no)
#从券列表加购物车
@app.route('/cart/cartscoupon', methods=['GET'])
async def add_to_carts():
    """
    Add products to cart
    :return: success or fail
    """
    logger.debug('进入接口1')
    logger.debug('进入接口11')
    logger.debug(request.args.get('storeCode'))
    logger.debug('进入接口2')
    store_code = request.args.get('storeCode')
    sub_product_code = request.args.get('subProductCode', '')
    coupon_code = request.args.get('couponCode', '')
    coupon_id = request.args.get('couponId', '')
    promotion_id = request.args.get('promotionId', '')
    
    cart_type = request.args.get('cartType', 1)
    channel_code = request.args.get('channelCode', '03')
    '''
    到店
    beType=1&beCode=&orderType=1

    外卖
    beType=2&beCode={storeCode}02&orderType=2
    '''
    logger.debug('进入接口3')
    be_type = request.args.get('beType', 1)
    be_code = request.args.get('beCode', '')
    order_type = request.args.get('orderType', 1)
    day_part_code = request.args.get('dayPartCode')
    logger.debug('进入接口4')
    if not day_part_code:
        day_part_code = _get_day_part_code()
    quantity = request.args.get('quantity', 1)
    membership_code = request.args.get('membershipCode', '')
    product_type = request.args.get('productType', 1)
    card_type = request.args.get('cardType', 0)
    card_id = request.args.get('cardId', '')
    if membership_code != '':
        product_type = 0

    sid = request.args.get('debug_sid')
    customization = request.args.get('customization', [])
    combo_items = request.args.get('comboItems', [])
    logger.debug('进入接口5')
    async with MCDAsync() as client:
        return await client.add_to_carts(sid, store_code=store_code, cart_type=cart_type,
                                        channel_code=channel_code, be_type=be_type, be_code=be_code, day_part_code=day_part_code,
                                        coupon_code=coupon_code, coupon_id=coupon_id, promotion_id=promotion_id, order_type=order_type,
                                        quantity=quantity, customization=customization, combo_items=combo_items, sub_product_code=sub_product_code,
                                        membership_code=membership_code, product_type=product_type, card_type=card_type, card_id=card_id)


                                        

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9527, debug=False)
