import asyncio
import json

import aiohttp
from loguru import logger


class MojiWeb:
    """
    MojiWeb class encapsulates async APIs for accessing https://html5.moji.com/tpd/moji35plan2023/index.html?channelno=5846&appshare=0.
    """
    def __init__(self):
        self.channel = '5846'
        self.session: aiohttp.ClientSession
        self.verify_ssl = False
        self.req_timeout = 30

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(raise_for_status=True)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()

    async def __base_get_request(self, url, params):
        """
        Base get request
        :param url: url
        :param params: params
        :return:
        """
        headers = {
            'Host': 'co.moji.com',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
            'Origin': 'https://html5.moji.com',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://html5.moji.com/',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        }

        logger.debug(json.dumps(params, indent=4, ensure_ascii=False))
        try:
            async with self.session.get(url, headers=headers, params=params, ssl=self.verify_ssl,
                                         timeout=self.req_timeout) as resp:
                text = await resp.text()
                jobj = json.loads(text)
                logger.debug(json.dumps(jobj, indent=4, ensure_ascii=False))
                return jobj
        except Exception as e:
            logger.exception(getattr(e, "__dict__", {}))
            return None

    async def __base_post_request(self, url, json_data):
        """
        Base post request
        :param url: url
        :param json_data: post body in json format
        :return:
        """
        raise Exception("unimplemented post")

    async def get_code(self, mobile, city_id):
        """
        Send SMS
        :param mobile: mobile
        :param city_id: city id
        :return:
        """
        url = 'https://co.moji.com/api/cola2023/getCode'
        params = {
            'mobile': mobile,
            'app': '0',
            'channel': self.channel,
            'platform': '0',
            'snsid': '',
            'cityid': city_id,
        }

        return await self.__base_get_request(url, params)

    async def raffle(self, mobile, sms_code, city_id, sns_id=''):
        """
        Raffle with SMS code
        :param mobile: mobile
        :param sms_code: sms code
        :param city_id: city id
        :param sns_id: sns id
        :return:
        """
        url = 'https://co.moji.com/api/cola2023/raffle'

        params = {
            'snsid': sns_id,
            'mobile': mobile,
            'smscode': sms_code,
            'app': '0',
            'channel': self.channel,
            'platform': '0',
            'cityid': city_id,
        }

        return await self.__base_get_request(url, params)

    async def user_coupon(self, mobile, sns_id, city_id):
        """
        Check user coupon
        :param mobile: mobile
        :param sns_id: sns id
        :param city_id: city id
        :return:
        """
        url = 'https://co.moji.com/api/cola2023/userCoupon'
        params = {
            'app': '0',
            'channel': self.channel,
            'mobile': mobile,
            'platform': '0',
            'snsid': sns_id,
            'cityid': city_id,
        }

        return await self.__base_get_request(url, params)


async def main():
    mobile = '18610288148'
    city_id = '5234'
    sns_id = 'iWYvkU7GCFsRiWiuAAKxwg=='
    async with MojiWeb() as client:
        # await client.get_code(mobile, city_id)
        # sms_code = input("input SMS code:")
        sms_code = ''
        await client.raffle(mobile, sms_code, city_id, sns_id)
        res = await client.raffle(mobile, sms_code, city_id)
        sns_id, coupon = res['data']['snsid'], res['data']['coupon']
        await client.user_coupon(mobile, sns_id, city_id)


if __name__ == '__main__':
    asyncio.run(main())
