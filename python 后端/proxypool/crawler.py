from loguru import logger
from pyquery import PyQuery as pq
import requests
import json


def get_page(url):
    """
    Get page content
    :param url: url
    :return: html content
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Mobile Safari/537.36'
    }
    return requests.get(url, headers=headers, timeout=10, verify=False).content


class ProxyMetaclass(type):
    """Proxy Meta Class"""
    def __new__(cls, name, bases, attrs):
        count = 0
        attrs['__CrawlFunc__'] = []
        for k, v in attrs.items():
            if 'crawl_' in k:
                attrs['__CrawlFunc__'].append(k)
                count += 1
        attrs['__CrawlFuncCount__'] = count
        return type.__new__(cls, name, bases, attrs)


class Crawler(metaclass=ProxyMetaclass):
    """Crawler class"""
    def get_proxies(self, callback):
        """
        Get proxies
        :param callback: callback
        :return: proxies
        """
        proxies = []
        for proxy in eval(f"self.{callback}()"):
            logger.info(f'get proxy({proxy})')
            proxies.append(proxy)

        return proxies

    def crawl_kuaidaili(self):
        """
        Crawl kuaidaili
        :return: proxy generator
        """
        urls = [f'https://www.kuaidaili.com/free/inha/{page}/' for page in range(1, 2)]
        for url in urls:
            logger.info(f'crawling {url}')
            html = get_page(url)
            if html:
                doc = pq(html)
                trs = doc('tr').items()
                for tr in trs:
                    if len(tr.find('td')) == 0:
                        continue
                    ip = tr.find('td[data-title="IP"]').text()
                    port = tr.find('td[data-title="PORT"]').text()
                    t = tr.find('td[data-title="类型"]').text()
                    logger.info(f'ip: ({ip}), port: {port}, type: {t}')
                    yield ':'.join([ip, port])

    def crawl_ip3366(self):
        """
        Crawl ip3366
        :return: proxy generator
        """
        urls = [f'http://www.ip3366.net/free/?page={page}' for page in range(1, 2)]
        for url in urls:
            logger.info(f'crawling {url}')
            html = get_page(url)
            if html:
                doc = pq(html)
                trs = doc('tr').items()
                for tr in trs:
                    if len(tr.find('td')) == 0:
                        continue
                    ip = tr.find('td:nth-child(1)').text()
                    port = tr.find('td:nth-child(2)').text()
                    t = tr.find('td:nth-child(4)').text()
                    logger.info(f'ip: ({ip}), port: {port}, type: {t}')
                    yield ':'.join([ip, port])

    def crawl_proxylistplus(self):
        """
        Crawl proxylistplus
        :return: proxy generator
        """
        urls = [f'https://list.proxylistplus.com/Fresh-HTTP-Proxy-List-{page}' for page in range(1, 2)]
        for url in urls:
            logger.info(f'crawling {url}')
            html = get_page(url)
            if html:
                doc = pq(html)
                trs = doc('table.bg tr.cells').items()
                for tr in trs:
                    if len(tr.find('td')) == 0:
                        continue
                    ip = tr.find('td:nth-child(2)').text()
                    port = tr.find('td:nth-child(3)').text()
                    t = 'HTTP' if tr.find('td:nth-child(6)').text() == 'no' else 'HTTPS'
                    logger.info(f'ip: ({ip}), port: {port}, type: {t}')
                    yield ':'.join([ip, port])

    def crawl_fatezero(self):
        """
        Crawl fatezero
        :return: proxy generator
        """
        url = 'http://proxylist.fatezero.org/proxy.list'

        logger.info(f'crawling {url}')
        html = get_page(url)
        if html:
            lines = html.decode('utf-8').split('\n')
            for line in lines:
                if len(line.strip()) == 0:
                    continue
                jobj = json.loads(line)
                ip = jobj['host']
                port = str(jobj['port'])
                t = jobj['type'].upper()
                logger.info(f'ip: {ip}, port: {port}, type: {t}')
                yield ':'.join([ip, port])

    def crawl_seofangfa(self):
        """
        Crawl seofangfa
        :return: proxy generator
        """
        url = 'https://proxy.seofangfa.com'
        logger.info(f'crawling {url}')
        html = get_page(url)
        if html:
            doc = pq(html)
            trs = doc('tr').items()
            for tr in trs:
                if len(tr.find('td')) == 0:
                    continue
                ip = tr.find('td:nth-child(1)').text()
                port = tr.find('td:nth-child(2)').text()
                t = 'HTTP'
                logger.info(f'ip: ({ip}), port: {port}, type: {t}')
                yield ':'.join([ip, port])

    def crawl_yqie(self):
        """
        Crawl yqie
        :return: proxy generator
        """
        url = 'http://ip.yqie.com/ipproxy.htm'
        logger.info(f'crawling {url}')
        html = get_page(url)
        if html:
            doc = pq(html)
            trs = doc('tr').items()
            for tr in trs:
                if len(tr.find('td')) == 0:
                    continue
                ip = tr.find('td:nth-child(1)').text()
                port = tr.find('td:nth-child(2)').text()
                t = tr.find('td:nth-child(5)').text()
                logger.info(f'ip: ({ip}), port: {port}, type: {t}')
                yield ':'.join([ip, port])

    def crawl_taiyanghttp(self):
        """
        Crawl taiyanghttp
        :return: proxy generator
        """
        urls = [f'http://www.taiyanghttp.com/free/page{page}/' for page in range(1, 2)]
        for url in urls:
            logger.info(f'crawling {url}')
            html = get_page(url)
            if html:
                doc = pq(html)
                trs = doc('.ip_tr').items()
                for tr in trs:
                    if len(tr.find('.td')) == 0:
                        continue
                    ip = tr.find('.td:nth-child(1)').text()
                    port = tr.find('.td:nth-child(2)').text()
                    t = tr.find('.td:nth-child(6)').text()
                    logger.info(f'ip: ({ip}), port: {port}, type: {t}')
                    yield ':'.join([ip, port])





if __name__ == '__main__':
    crawler = Crawler()
    logger.info(crawler.__CrawlFuncCount__)
    for callback_label in range(crawler.__CrawlFuncCount__):
        callback = crawler.__CrawlFunc__[callback_label]
        proxies = crawler.get_proxies(callback)
        for proxy in proxies:
            logger.info(f'proxy {proxy}')
