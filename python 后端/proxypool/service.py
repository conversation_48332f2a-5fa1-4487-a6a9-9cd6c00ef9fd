from flask import Flask, g
from redisclient import RedisClient

__all__ = ['app']
app = Flask(__name__)

def get_conn():
    if not hasattr(g, 'redis'):
        g.redis = RedisClient()
    return g.redis

@app.route('/')
def index():
    return '<h2>Welcome to Proxy Pool System</h2>'

@app.route('/random')
def get_proxy():
    """
    Get random proxy
    :return: random proxy
    """
    conn = get_conn()
    return conn.random()

@app.route('/count')
def get_counts():
    """
    Get total count of proxies
    :return: total count
    """
    conn = get_conn()
    return str(conn.count())


if __name__ == '__main__':
    app.run()
