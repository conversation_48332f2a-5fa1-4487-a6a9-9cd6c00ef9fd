import asyncio

from loguru import logger

from crawler_async import <PERSON>raw<PERSON>
from redisclient import RedisClient


class ProxyGetter:
    """
    Proxy Getter
    """
    def __init__(self):
        self.redis = RedisClient()
        self.crawler = Crawler()

    async def run(self):
        """
        Main saver function
        :return:
        """
        logger.info("start retrieving proxies")
        logger.info(self.crawler.__CrawlFuncCount__)
        tasks = [self.crawler.get_proxies(self.crawler.__CrawlFunc__[callback_label]) for callback_label in
                 range(self.crawler.__CrawlFuncCount__)]
        logger.info(len(tasks))
        res = await asyncio.gather(*tasks)
        for l in res:
            for ele in l:
                logger.info(ele)
                self.redis.add(f'{ele[0]}:{ele[1]}', ele[2])


if __name__ == '__main__':
    asyncio.run(ProxyGetter().run())