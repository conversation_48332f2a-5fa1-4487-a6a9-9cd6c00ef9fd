from random import choice

import redis
from loguru import logger

MAX_SCORE = 100
MIN_SCORE = 0
INITIAL_SCORE = 10
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_PASSWORD = None
REDIS_KEY_HTTP = 'proxies_http'
REDIS_KEY_HTTPS = 'proxies_https'
PROXY_TYPE_HTTP = 'HTTP'
PROXY_TYPE_HTTPS = 'HTTPS'
D_TYPE_REDIS_KEY = {
    PROXY_TYPE_HTTP: REDIS_KEY_HTTP,
    PROXY_TYPE_HTTPS: REDIS_KEY_HTTPS
}

class PoolEmptyError(Exception):
    """Pool empty error"""
    pass

def get_redis_key(proxy_type):
    """
    Get redis key by proxy type, raise exception if no key matches.
    :param proxy_type: proxy type
    :return: redis key
    """
    key = D_TYPE_REDIS_KEY.get(proxy_type)
    if key is None:
        raise Exception(f'unknown proxy type: {proxy_type}')
    return key

class RedisClient:
    """RedisClient for proxy pool"""
    def __init__(self, host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD):
        """
        Initialize with redis config
        :param host: redis host
        :param port: redis port
        :param password: redis client
        """
        self.db = redis.StrictRedis(host=host, port=port, password=password, decode_responses=True)

    def add(self, proxy, proxy_type='HTTP', score=INITIAL_SCORE):
        """
        Add proxy to pool
        :param proxy: proxy
        :param proxy_type: proxy type HTTP or HTTPS
        :param score: score
        :return: add result
        """
        key = get_redis_key(proxy_type)

        if not self.db.zscore(key, proxy):
            self.db.zadd(key, {proxy: score})

    def random(self, proxy_type='HTTP'):
        """
        Get a random valid proxy. Try to get the proxy with highest score first if exists,
        otherwise return proxy with most highest score, error if no available proxy exists.
        :param proxy_type: proxy type
        :return: random valid proxy
        """
        key = get_redis_key(proxy_type)

        result = self.db.zrangebyscore(key, MAX_SCORE, MAX_SCORE)
        if len(result):
            return choice(result)
        else:
            # retrieve first 100 records with descending score order
            result = self.db.zrevrange(key, 0, 100)
            if len(result):
                return choice(result)
            else:
                raise PoolEmptyError

    def decrease(self, proxy, proxy_type='HTTP'):
        """
        Decrease the score of the proxy. Delete the proxy if the score has reached 0.
        :param proxy: proxy
        :param proxy_type: proxy type
        :return: updated score of the proxy
        """
        key = get_redis_key(proxy_type)

        score = self.db.zscore(key, proxy)
        if score and score > MIN_SCORE:
            logger.info(f'proxy({proxy}) score is {score}, action: -1')
            self.db.zincrby(key, -1, proxy)
        else:
            logger.info(f'proxy({proxy}) score is {score}, action: remove')
            self.db.zrem(key, proxy)

    def exists(self, proxy, proxy_type='HTTP'):
        """
        Check if the proxy exists
        :param proxy: proxy
        :param proxy_type: proxy type
        :return: True if exists, otherwise False
        """
        key = get_redis_key(proxy_type)

        return self.db.zscore(key, proxy) is not None

    def max(self, proxy, proxy_type='HTTP'):
        """
        Set proxy with MAX_SCORE
        :param proxy: proxy
        :param proxy_type: proxy type
        :return: set result
        """
        key = get_redis_key(proxy_type)

        logger.info(f'set proxy({proxy}) in {key} with MAX_SCORE')
        return self.db.zadd(key, {proxy: MAX_SCORE})

    def count(self, proxy_type='HTTP'):
        """
        Get current proxies count
        :param proxy_type: proxy type
        :return: count
        """
        key = get_redis_key(proxy_type)

        return self.db.zcard(key)

    def all(self, proxy_type='HTTP'):
        """
        Get all proxies
        :param proxy_type: proxy type
        :return:
        """
        key = get_redis_key(proxy_type)

        return self.db.zrangebyscore(key, MIN_SCORE, MAX_SCORE)