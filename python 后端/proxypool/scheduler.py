import asyncio
from multiprocessing import Process

from loguru import logger

from proxygetter import ProxyGetter
from proxytester import ProxyTester
from service import app

TESTER_CYCLE = 30
GETTER_CYCLE = 300
API_HOST = '127.0.0.1'
API_PORT = 5000
TESTER_ENABLED = True
GETTER_ENABLED = True
API_ENABLED = False


class Scheduler:
    """
    Proxy scheduler
    """
    async def schedule_tester_async(self, cycle=TESTER_CYCLE):
        """
        Schedule proxy tester
        :param cycle: cycle
        :return:
        """
        tester = ProxyTester()
        while True:
            logger.info("starting proxy tester")
            await tester.run()
            await asyncio.sleep(cycle)

    def schedule_tester(self, cycle=TESTER_CYCLE):
        asyncio.run(self.schedule_tester_async(cycle))

    async def schedule_getter_async(self, cycle=GETTER_CYCLE):
        """
        Schedule proxy getter
        :param cycle: cycle
        :return:
        """
        saver = ProxyGetter()
        while True:
            logger.info("starting proxy saver")
            await saver.run()
            await asyncio.sleep(cycle)

    def schedule_getter(self, cycle=GETTER_CYCLE):
        asyncio.run(self.schedule_getter_async(cycle))

    def schedule_api(self):
        """
        Schedule api
        :return:
        """
        app.run(API_HOST, API_PORT)

    def run(self):
        """
        Scheduler main run function
        :return:
        """
        logger.info("starting proxy pool")

        if GETTER_ENABLED:
            saver_process = Process(target=self.schedule_getter)
            saver_process.start()

        if TESTER_ENABLED:
            test_process = Process(target=self.schedule_tester)
            test_process.start()

        if API_ENABLED:
            api_process = Process(target=self.schedule_api)
            api_process.start()



if __name__ == '__main__':
    Scheduler().run()