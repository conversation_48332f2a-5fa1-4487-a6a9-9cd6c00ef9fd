import asyncio
import json

import aiohttp
from loguru import logger


class KDL:
    """
    Kuaidaili
    """
    def __init__(self, secret_id, secret_key):
        """
        Init 区分用户secret和订单secret
        :param secret_id: order secret_id
        :param secret_key: order secret key
        """
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.secret_token = None
        self.timeout = 60

    async def get_secret_token(self):
        """
        Get secret token
        :return:
        """
        url = 'https://auth.kdlapi.com/api/get_secret_token'
        post_data = {
            'secret_id': self.secret_id,
            'secret_key': self.secret_key
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, data=post_data, ssl=False, timeout=self.timeout) as resp:
                resp.raise_for_status()

                jobj = await resp.json()
                logger.debug(json.dumps(jobj, indent=4, ensure_ascii=False))
                if jobj['code'] != 0:
                    raise Exception(f"failed to get secret_token, reason: {jobj['msg']}")

                self.secret_token = jobj['data']['secret_token']

    async def getdps(self, num):
        """
        Get private proxy list
        :return:
        """
        url = 'https://dps.kdlapi.com/api/getdps'
        params = {
            'secret_id': self.secret_id,
            'signature': self.secret_token,
            'format': 'json',
            'num': num,
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, ssl=False, timeout=self.timeout) as resp:
                resp.raise_for_status()
                jobj = await resp.json()
                logger.debug(json.dumps(jobj, indent=4, ensure_ascii=False))
                if jobj['code'] != 0:
                    raise Exception(f"failed to getdps, reason: {jobj['msg']}")

                return jobj['data']['proxy_list']

async def main():
    # 用户secret
    secret_id = 'uk2gjare9ad3lv75cws2'
    secret_key = 'tg06q7c4nte8islesrwcc0x7zcoes72i'

    # 订单secret
    secret_id = 'oep8gefmu7j59mvx6ss0'
    secret_key = '6zajq4mv02e6jjgz1cmwtanltyym56gt'
    client = KDL(secret_id, secret_key)
    await client.get_secret_token()
    res = await client.getdps(1)
    proxies = []
    for ele in res:
        ip, port = ele.split(':')
        proxies.append((ip, port, 'HTTP'))
    logger.info(json.dumps(proxies, indent=4, ensure_ascii=False))


if __name__ == '__main__':
    asyncio.run(main())