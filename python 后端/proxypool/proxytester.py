import asyncio

import aiohttp
from aiohttp import Client<PERSON>rror, ClientConnectorError
from loguru import logger

from redisclient import RedisClient

VALID_STATUS_CODES = [200]
# TEST_URL = 'https://api.mcd.cn/bff/common/proxy/tid?campaignId=0&channelId=96&exposureId=0&groupId=0&salerId=0'
TEST_URL = 'https://api.mcd.cn/bff/common/proxy/tid'
BATCH_TEST_SIZE = 100

class ProxyTester:
    """
    Proxy tester
    """
    def __init__(self):
        self.redis = RedisClient()

    async def test_single_proxy(self, proxy):
        """
        Test single proxy
        :param proxy: proxy
        :return:
        """
        conn = aiohttp.TCPConnector(ssl=False)
        async with aiohttp.ClientSession(connector=conn) as session:
            try:
                if isinstance(proxy, bytes):
                    proxy = proxy.decode('utf-8')
                real_proxy = f'http://{proxy}'
                async with session.get(TEST_URL, proxy=real_proxy, timeout=15) as resp:
                    if resp.status in VALID_STATUS_CODES:
                        self.redis.max(proxy)
                        logger.info(f'proxy({proxy}) works!')
                    else:
                        logger.info(f'invalid response for proxy({proxy})')
                        self.redis.decrease(proxy)
            except (ClientError, ClientConnectorError, TimeoutError, AttributeError):
                self.redis.decrease(proxy)
            except Exception as e:
                self.redis.decrease(proxy)

    async def run(self):
        """
        Main test run function
        :return:
        """
        logger.info("start tester...")
        try:
            proxies = self.redis.all()
            for i in range(0, len(proxies), BATCH_TEST_SIZE):
                test_proxies = proxies[i:i+BATCH_TEST_SIZE]
                tasks = [self.test_single_proxy(proxy) for proxy in test_proxies]
                await asyncio.gather(*tasks)
                await asyncio.sleep(5)
        except Exception as e:
            logger.exception("Exception occurred in tester:  %s", getattr(e, "__dict__", {}))


if __name__ == '__main__':
    asyncio.run(ProxyTester().run())
