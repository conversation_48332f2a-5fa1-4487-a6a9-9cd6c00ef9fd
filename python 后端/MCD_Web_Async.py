import asyncio
import json
import time

import aiohttp

from util import Util

from loguru import logger


class MCDWeb:
    """
    MCD Web Async Client
    """
    def __init__(self):
        self.session = None
        self.verify_ssl = False
        self.req_timeout = 30

    def __del__(self):
        pass

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()

    def _get_common_headers(self, is_post=True):
        """
        Get common headers for requests
        :param is_post: if the request is POST
        :return: common headers
        """
        ts = int(time.time() * 1000)
        st = Util.make_st(ts)
        nonce = Util.make_nonce(ts)

        headers = {
            'Host': 'api.mcd.cn',
            'sec-ch-ua': '"Google Chrome";v="105", "Not)A;Brand";v="8", "Chromium";v="105"',
            'sv': 'v3',
            'nonce': str(nonce),
            'sec-ch-ua-mobile': '?0',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'st': str(st),
            'Accept': 'application/json, text/plain, */*',
            'ct': '10',
            'tid': '00003TuM',
            'sec-ch-ua-platform': '"macOS"',
            'Origin': 'http://cdn.mcd.cn',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'http://cdn.mcd.cn/',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        }

        if is_post:
            headers['Content-Type'] = 'application/json'

        return headers

    async def _base_get_request(self, url, extra_headers, params, path=''):
        """
        Base get request
        :param url: request url
        :param extra_headers: extra headers
        :param params: get params
        :param path: request path, used to calculate signature
        :return: response in json format
        """
        headers = self._get_common_headers(False)
        headers.update(extra_headers)
        sign = Util.generate_web_sign(headers, params, None, path)
        headers['sign'] = sign

        try:
            async with self.session.get(url, params=params, headers=headers, ssl=self.verify_ssl, timeout=self.req_timeout) as resp:
                resp.raise_for_status()
                return await resp.json()
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def _base_post_request(self, url, extra_headers, json_data, path=''):
        """
        Base post request
        :param url: request url
        :param extra_headers: extra headers
        :param json_data: post body in json format
        :param path: request path, used to calcualte signature
        :return: response in json format
        """
        headers = self._get_common_headers()
        headers.update(extra_headers)
        sign = Util.generate_web_sign(headers, None, json_data, path)
        headers['sign'] = sign

        post_content = json.dumps(json_data, separators=(',', ':'), ensure_ascii=False)
        try:
            async with self.session.post(url, headers=headers, data=post_content, ssl=self.verify_ssl, timeout=self.req_timeout) as resp:
                resp.raise_for_status()
                return await resp.json()
        except aiohttp.ClientError as e:
            logger.error(
                "aiohttp exception for %s [%s]: %s",
                url,
                getattr(e, "status", None),
                getattr(e, "message", None),
            )
            return None
        except Exception as e:
            logger.exception("Non-aiohttp exception occurred:  %s", getattr(e, "__dict__", {}))
            return None

    async def send_sms(self, mobile):
        """
        Send SMS
        :param mobile: mobile
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/login/send/sms'
        json_data = {
            'tel': Util.aes_ecb(mobile),
            'type': 1,
        }

        return await self._base_post_request(url, {}, json_data)

    async def login(self, mobile, sms_code):
        """
        Login with SMS
        :param mobile: mobile
        :param sms_code: sms code
        :return:
        """
        url = 'https://api.mcd.cn/bff/member/login/mobile'
        json_data = {
            'tel': Util.aes_ecb(mobile),
            'code': Util.aes_ecb(sms_code),
        }

        return await self._base_post_request(url, {}, json_data)

    async def get_coupon_table(self, sid, city_code, page_id):
        """
        Get available coupons
        :param sid: sid
        :param city_code: city code
        :param page_id: activity id
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/member/page/coupon/customer/getable'
        extra_header = {
            'sid': sid
        }
        params = {
            'putChannel': '03',
            'pageId': page_id,
            'cityCode': city_code,
            'sid': sid,
        }

        return await self._base_get_request(url, extra_header, params)

    async def take_new_user_gift(self, sid, coupons, page_id):
        """
        Take new user gift(coupons)
        :param sid: sid
        :param coupons: coupons as gift
        :param page_id: new user activity id
        :return: response in json format
        """
        url = 'https://api.mcd.cn/bff/market/app/gift/newReceive'
        extra_header = {
            'sid': sid
        }
        json_data = {
            'coupons': coupons,
            'moduleType': 0,
            'channelCode': '',
            "pageId": page_id
        }

        return await self._base_post_request(url, extra_header, json_data)


async def main():
    # mobile = '18610288148'
    # mobile = '13005991646'
    # mobile = '13264185097'  # 780b27b1cf7234b1cd8540b1ff8b6121_
    mobile = '18608556470'  # 17f0b246dd196cf3272f563404adec78_ 老号
    city_code = '310100'  # 上海
    page_id = "some_page_id"

    async with MCDWeb() as mcd_client:
        await mcd_client.send_sms(mobile)
        sms_code = input('input the sms code:')

        if sms_code is None:
            logger.error("waiting sms timeout")
            exit(0)

        res = await mcd_client.login(mobile, sms_code)
        sid = res['data']['sid']
        has_new_user_gift = res['data']['newUserGift']

        if has_new_user_gift:
            res = await mcd_client.get_coupon_table(sid, city_code, page_id)
            modules = [group['modules'] for group in res['data']['groups'] if 'coupon' in group['groupTag']][0]
            coupons = [module['coupons'] for module in modules if 'coupon' in module['moduleId']][0]

            coupons = [{'couponId': coupon['couponId'], 'receiveQuantity': coupon['receiveQuantity']} for coupon in coupons
                       if coupon['label'] is None]

            await mcd_client.take_new_user_gift(sid, coupons, page_id)


if __name__ == '__main__':
    asyncio.run(main())
