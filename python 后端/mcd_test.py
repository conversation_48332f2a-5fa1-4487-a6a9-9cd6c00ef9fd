import requests
import time
from util import Util

ts = int(time.time() * 1000)
st = Util.make_st(ts)
nonce = Util.make_nonce(ts)

headers = {
    'biz_scenario': '500',
    'biz_from': '1002',
    'User-Agent': 'mc<PERSON><PERSON>_Android/******** (Android)',
    'ct': '102',
    'v': '********',
    'p': '102',
    'sid': '3be55401a10b3afbbcbc92a12276b4c0_',
    'language': 'cn',
    'traceid': 'f4040ff4-4e37-487f-8eaf-4338901d58a8',
    # 'st': '1668432157',
    'st': str(st),
    # 'nonce': '1668432157704146506',
    'nonce': nonce,
    'sv': 'v3',
    # 'sign': 'a5ebb10e8eadbe0481eefc10990ef3a7',
    'tid': '00003TuN',
    'Host': 'api.mcd.cn',
}

sign = Util.generate_adaptive_sign(headers, {}, None)
headers['sign'] = sign

response = requests.get('https://api.mcd.cn/bff/common/normal/config', headers=headers)
print(response.json())