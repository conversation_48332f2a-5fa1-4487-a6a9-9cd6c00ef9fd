# MCD API服务Docker环境配置
# 自动生成于: 2025-07-12 00:15:12

# 环境配置
ENVIRONMENT=production
PYTHON_ENV=production
DEBUG=false

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
SERVER_DEBUG=false
SERVER_LOG_LEVEL=INFO
SERVER_RELOAD=false

# 数据库配置已移除 - mcd-api-server 不再需要数据库连接

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# MCD API配置
MCD_API_BASE_URL=https://api.mcd.cn
MCD_API_APP_VERSION=********
MCD_API_DEVICE_ID=BQuLoOFbMfVK30is464W4mep0iXmkEqlh0b7zcnqGLuxpellGcpLVVvEHvDnUxWEQst3PoCbGho3B3FxlLfk2tg==
MCD_API_TID=00003TuN
MCD_API_TIMEOUT=30
MCD_API_MAX_RETRIES=3
MCD_API_RETRY_DELAY=1.0

# CORS配置
SERVER_CORS_ORIGINS=*
SERVER_ALLOWED_HOSTS=*

# JWT配置
JWT_SECRET=your-jwt-secret-key-change-in-production
