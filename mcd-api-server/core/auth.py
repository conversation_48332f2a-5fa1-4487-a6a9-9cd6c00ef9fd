"""
认证和授权模块
实现JWT认证、权限控制、请求限流等安全机制
"""
import jwt
import time
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import redis.asyncio as redis
from config.simple_config import config

# 配置实例 - 使用简化配置
jwt_secret = config.jwt_secret
jwt_algorithm = config.jwt_algorithm
jwt_expires_in = config.jwt_expires_in

# JWT Bearer认证
security = HTTPBearer()

class TokenData(BaseModel):
    """Token数据模型"""
    user_id: str
    username: str
    roles: List[str] = []
    permissions: List[str] = []
    exp: int
    user_type: str = "customer"

class User(BaseModel):
    """用户模型"""
    id: str
    username: str
    email: Optional[str] = None
    roles: List[str] = []
    permissions: List[str] = []
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None
    user_type: str = "customer"  # customer, store_staff, system_admin

class AuthService:
    """认证服务"""
    
    def __init__(self):
        self.redis_client = None
    
    async def init_redis(self):
        """初始化Redis连接"""
        if not self.redis_client:
            self.redis_client = redis.Redis(
                host=config.redis_host,
                port=config.redis_port,
                password=config.redis_password,
                db=config.redis_db,
                decode_responses=True
            )
    
    def create_access_token(self, user: User, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=30)  # 30分钟有效期
        
        to_encode = {
            "user_id": user.id,
            "username": user.username,
            "roles": user.roles,
            "permissions": user.permissions,
            "user_type": user.user_type,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        encoded_jwt = jwt.encode(
            to_encode,
            jwt_secret,
            algorithm=jwt_algorithm
        )
        return encoded_jwt
    
    def create_refresh_token(self, user: User) -> str:
        """创建刷新令牌"""
        expire = datetime.utcnow() + timedelta(days=30)
        
        to_encode = {
            "user_id": user.id,
            "username": user.username,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        }
        
        encoded_jwt = jwt.encode(
            to_encode,
            jwt_secret,
            algorithm=jwt_algorithm
        )
        return encoded_jwt
    
    def verify_token(self, token: str) -> TokenData:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                jwt_secret,
                algorithms=[jwt_algorithm]
            )
            
            user_id: str = payload.get("user_id")
            username: str = payload.get("username")
            roles: List[str] = payload.get("roles", [])
            permissions: List[str] = payload.get("permissions", [])
            user_type: str = payload.get("user_type", "customer")
            exp: int = payload.get("exp")
            
            if user_id is None or username is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌无效",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return TokenData(
                user_id=user_id,
                username=username,
                roles=roles,
                permissions=permissions,
                user_type=user_type,
                exp=exp
            )
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def is_token_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        await self.init_redis()
        token_hash = hashlib.md5(token.encode()).hexdigest()
        return await self.redis_client.exists(f"blacklist:{token_hash}")
    
    async def blacklist_token(self, token: str, ttl: int = None):
        """将令牌加入黑名单"""
        await self.init_redis()
        token_hash = hashlib.md5(token.encode()).hexdigest()
        
        if ttl is None:
            # 默认黑名单时间为令牌剩余有效期
            try:
                payload = jwt.decode(
                    token,
                    jwt_secret,
                    algorithms=[jwt_algorithm],
                    options={"verify_exp": False}
                )
                exp = payload.get("exp", 0)
                ttl = max(0, exp - int(time.time()))
            except:
                ttl = 86400  # 默认24小时
        
        await self.redis_client.setex(f"blacklist:{token_hash}", ttl, "1")
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        import bcrypt
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        import bcrypt
        return bcrypt.checkpw(
            plain_password.encode('utf-8'),
            hashed_password.encode('utf-8')
        )

# 全局认证服务实例
auth_service = AuthService()

class RateLimiter:
    """请求限流器"""
    
    def __init__(self):
        self.redis_client = None
        self.default_limits = {
            "high_frequency": {"requests": 100, "window": 60},  # 100请求/分钟
            "auth": {"requests": 10, "window": 60},  # 认证接口限流
            "standard": {"requests": 60, "window": 60}  # 标准接口限流
        }
    
    async def init_redis(self):
        """初始化Redis连接"""
        if not self.redis_client:
            self.redis_client = redis.Redis(
                host=config.redis_host,
                port=config.redis_port,
                password=config.redis_password,
                db=config.redis_db,
                decode_responses=True
            )
    
    async def is_rate_limited(self, key: str, limit: int = None, window: int = None, 
                             limit_type: str = "standard") -> bool:
        """检查是否触发限流"""
        await self.init_redis()
        
        if limit is None or window is None:
            limit_config = self.default_limits.get(limit_type, self.default_limits["standard"])
            limit = limit or limit_config["requests"]
            window = window or limit_config["window"]
        
        current_time = int(time.time())
        window_start = current_time - window
        
        # 使用滑动窗口算法
        pipe = self.redis_client.pipeline()
        
        # 删除窗口外的记录
        pipe.zremrangebyscore(key, 0, window_start)
        
        # 添加当前请求
        pipe.zadd(key, {str(current_time): current_time})
        
        # 获取当前窗口内的请求数
        pipe.zcard(key)
        
        # 设置过期时间
        pipe.expire(key, window)
        
        results = await pipe.execute()
        request_count = results[2]
        
        return request_count > limit
    
    async def get_rate_limit_status(self, key: str, limit_type: str = "standard") -> Dict[str, Any]:
        """获取限流状态"""
        await self.init_redis()
        
        limit_config = self.default_limits.get(limit_type, self.default_limits["standard"])
        current_time = int(time.time())
        window_start = current_time - limit_config["window"]
        
        # 获取当前窗口内的请求数
        request_count = await self.redis_client.zcard(key)
        remaining = max(0, limit_config["requests"] - request_count)
        
        return {
            "limit": limit_config["requests"],
            "remaining": remaining,
            "reset_time": current_time + limit_config["window"],
            "window": limit_config["window"]
        }

# 全局限流器实例
rate_limiter = RateLimiter()

# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """获取当前用户"""
    token = credentials.credentials
    
    # 检查令牌是否在黑名单中
    if await auth_service.is_token_blacklisted(token):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌已失效",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 验证令牌
    token_data = auth_service.verify_token(token)
    return token_data

async def get_current_active_user(current_user: TokenData = Depends(get_current_user)) -> TokenData:
    """获取当前活跃用户"""
    # 这里可以添加额外的用户状态检查
    return current_user

def require_permissions(required_permissions: List[str], require_all: bool = False):
    """权限检查装饰器"""
    def permission_checker(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        if require_all:
            # 需要所有权限
            if not all(perm in current_user.permissions for perm in required_permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )
        else:
            # 只需要任意一个权限
            if not any(perm in current_user.permissions for perm in required_permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )
        return current_user
    return permission_checker

def require_roles(required_roles: List[str]):
    """角色检查装饰器"""
    def role_checker(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        if not any(role in current_user.roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="角色权限不足"
            )
        return current_user
    return role_checker

def require_user_type(allowed_types: List[str]):
    """用户类型检查装饰器"""
    def user_type_checker(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        user_type = getattr(current_user, 'user_type', 'customer')
        if user_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户类型权限不足"
            )
        return current_user
    return user_type_checker

# 预定义的角色权限检查装饰器
def require_customer():
    """客户权限检查"""
    return require_user_type(["customer"])

def require_store_staff():
    """店员权限检查"""
    return require_user_type(["store_staff", "system_admin"])

def require_system_admin():
    """系统管理员权限检查"""
    return require_user_type(["system_admin"])

# 预定义的权限检查装饰器
def require_menu_read():
    """菜单读取权限"""
    return require_permissions(["menu:read"])

def require_menu_write():
    """菜单写入权限"""
    return require_permissions(["menu:write"])

def require_order_create():
    """订单创建权限"""
    return require_permissions(["order:create"])

def require_order_read():
    """订单查询权限"""
    return require_permissions(["order:read"])

def require_order_modify():
    """订单修改权限"""
    return require_permissions(["order:modify"])

async def check_rate_limit(request: Request, limit_type: str = "standard", 
                          current_user: Optional[TokenData] = None):
    """检查请求限流"""
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "unknown")
    
    # 基于用户的限流（如果已认证）
    if current_user:
        user_key = f"rate_limit:user:{current_user.user_id}"
        if await rate_limiter.is_rate_limited(user_key, limit_type=limit_type):
            status_info = await rate_limiter.get_rate_limit_status(user_key, limit_type)
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"请求过于频繁，请稍后再试。剩余请求数: {status_info['remaining']}",
                headers={
                    "X-RateLimit-Limit": str(status_info['limit']),
                    "X-RateLimit-Remaining": str(status_info['remaining']),
                    "X-RateLimit-Reset": str(status_info['reset_time'])
                }
            )
    
    # 基于IP的限流
    ip_key = f"rate_limit:ip:{client_ip}"
    if await rate_limiter.is_rate_limited(ip_key, limit_type=limit_type):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="请求过于频繁，请稍后再试"
        )
    
    # 基于用户代理的限流（防止恶意爬虫）
    ua_key = f"rate_limit:ua:{hashlib.md5(user_agent.encode()).hexdigest()}"
    if await rate_limiter.is_rate_limited(ua_key, limit=200, window=60):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="请求过于频繁，请稍后再试"
        )

async def refresh_access_token(refresh_token: str) -> Dict[str, str]:
    """刷新访问令牌"""
    try:
        # 验证刷新令牌
        payload = jwt.decode(
            refresh_token,
            jwt_secret,
            algorithms=[jwt_algorithm]
        )
        
        # 检查令牌类型
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 检查令牌是否在黑名单中
        if await auth_service.is_token_blacklisted(refresh_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="刷新令牌已失效"
            )
        
        # 获取用户信息
        user_id = payload.get("user_id")
        username = payload.get("username")
        
        # 这里应该从数据库获取最新的用户信息
        # 简化示例，实际应用中需要查询数据库
        user = User(
            id=user_id,
            username=username,
            roles=["customer"],  # 从数据库获取
            permissions=["menu:read", "order:create"],  # 从数据库获取
            created_at=datetime.utcnow(),
            user_type="customer"
        )
        
        # 生成新的访问令牌
        new_access_token = auth_service.create_access_token(user)
        
        # 生成新的刷新令牌
        new_refresh_token = auth_service.create_refresh_token(user)
        
        # 将旧的刷新令牌加入黑名单
        await auth_service.blacklist_token(refresh_token)
        
        return {
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
            "expires_in": 1800  # 30分钟
        }
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌已过期"
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )

# 可选的认证依赖（某些接口可能不需要认证）
async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[TokenData]:
    """可选的用户认证"""
    if credentials is None:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None
