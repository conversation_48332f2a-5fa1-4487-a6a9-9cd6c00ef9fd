"""
Modern HTTP client for McDonald's API using httpx.
Features connection pooling, automatic retries, and enhanced logging.
"""
import asyncio
import json
import random
import time
import uuid
from typing import Any, Dict, Optional

import httpx
from loguru import logger

from config.settings import settings
from core.exceptions import (MCDAPIError, MCDConnectionError,
                             MCDTimeoutError)
from utils.crypto_utils import CryptoUtils


class MCDHttpClient:
    """Modern HTTP client for McDonald's API, powered by httpx."""

    def __init__(self):
        """
        Initialize the httpx client with connection pooling, timeouts, and retry logic.
        """
        # Timeout configuration
        timeout = httpx.Timeout(settings.mcd_api.timeout, connect=10.0)

        # Transport configuration with connection pooling
        self.transport = httpx.AsyncHTTPTransport(
            retries=settings.mcd_api.max_retries,
            limits=httpx.Limits(max_connections=100, max_keepalive_connections=30)
        )

        # The httpx.AsyncClient instance
        self.session: Optional[httpx.AsyncClient] = None
        self.timeout = timeout
        self.crypto_utils = CryptoUtils()

    async def __aenter__(self):
        """Async context manager entry: initializes the client session."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit: closes the client session."""
        await self.close()

    async def start(self):
        """Start the httpx client session."""
        if self.session is None or self.session.is_closed:
            self.session = httpx.AsyncClient(
                transport=self.transport,
                timeout=self.timeout,
                headers=self._get_default_headers(),
                http2=True,  # Enable HTTP/2 for better performance
                verify=True  # Enforce SSL verification by default
            )
            logger.info("httpx client session started with HTTP/2.")

    async def close(self):
        """Close the httpx client session."""
        if self.session and not self.session.is_closed:
            await self.session.aclose()
            logger.info("httpx client session closed.")

    def _get_default_headers(self) -> Dict[str, str]:
        """Get default request headers."""
        return {
            'User-Agent': f'mcdonald_Android/{settings.mcd_api.app_version} (Android)',
            'Content-Type': 'application/json; charset=UTF-8',
            'Host': 'api.mcd.cn',
            'device-id': settings.mcd_api.device_id,
            'tid': settings.mcd_api.tid,
        }

    def _generate_request_headers(self, sid: Optional[str] = None,
                                biz_from: str = '1001',
                                biz_scenario: str = '100') -> Dict[str, str]:
        """
        Generate dynamic request headers for each request.
        Matches the original Python implementation exactly.

        Args:
            sid: Optional session ID.
            biz_from: Business from parameter
            biz_scenario: Business scenario parameter

        Returns:
            A dictionary of headers.
        """
        ts = int(time.time() * 1000)
        st = str(int(ts / 1000))
        nonce = f'{ts}{str(random.randint(100000, 999999))}'

        headers = {
            'User-Agent': f'mcdonald_Android/{settings.mcd_api.app_version} (Android)',
            'ct': '102',
            'v': settings.mcd_api.app_version,
            'p': '102',
            'language': 'cn',
            'traceid': str(uuid.uuid4()),
            'st': st,
            'nonce': nonce,
            'sv': 'v3',
            'tid': '00003TuN',
            'device-id': 'BQuLoOFbMfVK30is464W4mep0iXmkEqlh0b7zcnqGLuxpellGcpLVVvEHvDnUxWEQst3PoCbGho3B3FxlLfk2tg==',
            'Content-Type': 'application/json; charset=UTF-8',
            'Host': 'api.mcd.cn',
            'biz_from': biz_from,
            'biz_scenario': biz_scenario,
        }

        if sid:
            headers['sid'] = sid

        return headers

    async def _make_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        sid: Optional[str] = None,
        path_for_sign: str = '',
        biz_from: str = '1001',
        biz_scenario: str = '100'
    ) -> Dict[str, Any]:
        """
        Central method for making HTTP requests with httpx.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE).
            url: The request URL.
            params: URL query parameters.
            json_data: JSON body for the request.
            sid: Optional session ID.
            path_for_sign: The path component used for signature generation.

        Returns:
            The JSON response as a dictionary.

        Raises:
            MCDTimeoutError: If the request times out.
            MCDConnectionError: If a connection error occurs.
            MCDAPIError: For other request-related or API errors.
        """
        await self.start()  # Ensure session is active

        headers = self._generate_request_headers(sid, biz_from, biz_scenario)
        sign = self.crypto_utils.generate_adaptive_sign(headers, params, json_data, path_for_sign)
        headers['sign'] = sign

        request_id = f"req_{uuid.uuid4().hex[:8]}"
        logger.debug(f"[{request_id}] > {method} {url}")
        if params: logger.debug(f"[{request_id}]   Params: {params}")
        if json_data: logger.debug(f"[{request_id}]   Body: {json.dumps(json_data, ensure_ascii=False)}")

        start_time = time.monotonic()
        try:
            response = await self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=json_data
            )
            response.raise_for_status()  # Raise exception for 4xx/5xx responses

            duration = (time.monotonic() - start_time) * 1000
            logger.info(f"[{request_id}] < {response.status_code} in {duration:.2f}ms - {method} {url}")

            return response.json()

        except httpx.TimeoutException as e:
            duration = (time.monotonic() - start_time) * 1000
            logger.error(f"[{request_id}] ! Timeout after {duration:.2f}ms - {method} {url}: {e}")
            raise MCDTimeoutError(f"Request timed out: {e}") from e

        except httpx.RequestError as e:
            duration = (time.monotonic() - start_time) * 1000
            logger.error(f"[{request_id}] ! Connection error after {duration:.2f}ms - {method} {url}: {e}")
            raise MCDConnectionError(f"Connection error: {e}") from e

        except json.JSONDecodeError as e:
            duration = (time.monotonic() - start_time) * 1000
            logger.error(f"[{request_id}] ! JSON decode error after {duration:.2f}ms - {method} {url}")
            raise MCDAPIError("Failed to decode JSON response.") from e

    async def get(self, url: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """Make a GET request."""
        return await self._make_request('GET', url, params=params, **kwargs)

    async def post(self, url: str, json_data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """Make a POST request."""
        return await self._make_request('POST', url, json_data=json_data, **kwargs)

    async def put(self, url: str, json_data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """Make a PUT request."""
        return await self._make_request('PUT', url, json_data=json_data, **kwargs)

    async def delete(self, url: str, **kwargs) -> Dict[str, Any]:
        """Make a DELETE request."""
        return await self._make_request('DELETE', url, **kwargs)
