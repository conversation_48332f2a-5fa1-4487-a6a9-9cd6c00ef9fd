"""
Error Monitoring and Logging System
Provides comprehensive error monitoring, alerting, and analytics capabilities
File-based logging without database dependencies
"""

import logging
import json
import asyncio
import os
import aiofiles
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
from enum import Enum
from dataclasses import dataclass, asdict

from .exceptions import MCDBaseError, ErrorCategory, ErrorSeverity


class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ErrorAlert:
    """Error alert data structure"""
    id: str
    error_code: str
    category: str
    severity: str
    message: str
    count: int
    first_seen: datetime
    last_seen: datetime
    affected_users: List[str]
    request_ids: List[str]
    context: Dict[str, Any]


class ErrorMonitor:
    """Error monitoring and alerting system with file-based logging"""

    def __init__(self, log_file: str = "logs/errors.log", alert_thresholds=None):
        self.log_file = log_file
        self.alert_thresholds = alert_thresholds or {
            'error_rate_threshold': 0.1,  # 10% error rate
            'critical_errors_threshold': 5,  # 5 critical errors in 5 minutes
            'high_errors_threshold': 20,  # 20 high severity errors in 10 minutes
            'consecutive_errors_threshold': 10  # 10 consecutive errors
        }

        # Ensure log directory exists
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

        # In-memory storage for real-time monitoring
        self.error_buffer = deque(maxlen=10000)
        self.error_patterns = defaultdict(list)
        self.user_error_count = defaultdict(int)
        self.consecutive_errors = 0
        self.last_success_time = datetime.utcnow()

        # Alert management
        self.active_alerts = {}
        self.alert_cooldown = {}

        # Performance metrics
        self.performance_metrics = {
            'total_requests': 0,
            'error_requests': 0,
            'avg_response_time': 0,
            'error_rate': 0
        }

        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup structured logging for error monitoring"""
        self.logger = logging.getLogger('error_monitor')
        self.logger.setLevel(logging.INFO)
        
        # Create formatter for structured logging
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for errors
        file_handler = logging.FileHandler('error_monitor.log')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    async def record_error(
        self,
        error: MCDBaseError,
        user_id: str = None,
        request_duration: float = None,
        background_tasks: BackgroundTasks = None
    ):
        """Record error occurrence and trigger monitoring"""
        
        error_data = {
            'timestamp': datetime.utcnow(),
            'error_code': error.error_code,
            'category': error.category.value,
            'severity': error.severity.value,
            'message': error.message,
            'request_id': error.request_id,
            'user_id': user_id,
            'request_duration': request_duration,
            'context': error.context,
            'stack_trace': error.error_path
        }
        
        # Add to buffer for real-time monitoring
        self.error_buffer.append(error_data)
        
        # Update patterns
        pattern_key = f"{error.category.value}:{error.error_code}"
        self.error_patterns[pattern_key].append(error_data)
        
        # Update user error count
        if user_id:
            self.user_error_count[user_id] += 1
        
        # Update consecutive errors
        self.consecutive_errors += 1
        
        # Update performance metrics
        self.performance_metrics['total_requests'] += 1
        self.performance_metrics['error_requests'] += 1
        
        if request_duration:
            # Update average response time
            current_avg = self.performance_metrics['avg_response_time']
            total_requests = self.performance_metrics['total_requests']
            self.performance_metrics['avg_response_time'] = (
                (current_avg * (total_requests - 1) + request_duration) / total_requests
            )
        
        # Calculate error rate
        self.performance_metrics['error_rate'] = (
            self.performance_metrics['error_requests'] / 
            self.performance_metrics['total_requests']
        )
        
        # Log error
        self.logger.error(
            f"Error recorded: {error.error_code}",
            extra={
                'error_code': error.error_code,
                'category': error.category.value,
                'severity': error.severity.value,
                'request_id': error.request_id,
                'user_id': user_id,
                'context': error.context
            }
        )
        
        # Write to log file
        asyncio.create_task(self._write_error_to_file(error_data))

        # Check for alerts
        asyncio.create_task(self._check_alerts(error_data))
    
    async def record_success(self, request_duration: float = None):
        """Record successful request"""
        self.consecutive_errors = 0
        self.last_success_time = datetime.utcnow()
        
        # Update performance metrics
        self.performance_metrics['total_requests'] += 1
        
        if request_duration:
            current_avg = self.performance_metrics['avg_response_time']
            total_requests = self.performance_metrics['total_requests']
            self.performance_metrics['avg_response_time'] = (
                (current_avg * (total_requests - 1) + request_duration) / total_requests
            )
        
        # Update error rate
        self.performance_metrics['error_rate'] = (
            self.performance_metrics['error_requests'] / 
            self.performance_metrics['total_requests']
        )
    
    async def _check_alerts(self, error_data: Dict):
        """Check if error should trigger alerts"""
        
        # Check critical error threshold
        if error_data['severity'] == ErrorSeverity.CRITICAL.value:
            recent_critical_errors = self._get_recent_errors(
                minutes=5,
                severity=ErrorSeverity.CRITICAL.value
            )
            
            if len(recent_critical_errors) >= self.alert_thresholds['critical_errors_threshold']:
                await self._trigger_alert(
                    alert_type='critical_errors',
                    severity=AlertSeverity.CRITICAL,
                    message=f"Critical error threshold exceeded: {len(recent_critical_errors)} errors in 5 minutes",
                    context={'errors': recent_critical_errors}
                )
        
        # Check high error threshold
        if error_data['severity'] == ErrorSeverity.HIGH.value:
            recent_high_errors = self._get_recent_errors(
                minutes=10,
                severity=ErrorSeverity.HIGH.value
            )
            
            if len(recent_high_errors) >= self.alert_thresholds['high_errors_threshold']:
                await self._trigger_alert(
                    alert_type='high_errors',
                    severity=AlertSeverity.ERROR,
                    message=f"High severity error threshold exceeded: {len(recent_high_errors)} errors in 10 minutes",
                    context={'errors': recent_high_errors}
                )
        
        # Check error rate threshold
        if self.performance_metrics['error_rate'] > self.alert_thresholds['error_rate_threshold']:
            await self._trigger_alert(
                alert_type='error_rate',
                severity=AlertSeverity.WARNING,
                message=f"Error rate threshold exceeded: {self.performance_metrics['error_rate']:.2%}",
                context={'error_rate': self.performance_metrics['error_rate']}
            )
        
        # Check consecutive errors threshold
        if self.consecutive_errors >= self.alert_thresholds['consecutive_errors_threshold']:
            await self._trigger_alert(
                alert_type='consecutive_errors',
                severity=AlertSeverity.ERROR,
                message=f"Consecutive error threshold exceeded: {self.consecutive_errors} errors",
                context={'consecutive_errors': self.consecutive_errors}
            )
    
    async def _trigger_alert(
        self,
        alert_type: str,
        severity: AlertSeverity,
        message: str,
        context: Dict = None
    ):
        """Trigger alert if not in cooldown"""
        
        now = datetime.utcnow()
        cooldown_key = f"{alert_type}:{severity.value}"
        
        # Check cooldown
        if cooldown_key in self.alert_cooldown:
            if now - self.alert_cooldown[cooldown_key] < timedelta(minutes=15):
                return  # Skip alert due to cooldown
        
        # Update cooldown
        self.alert_cooldown[cooldown_key] = now
        
        # Create alert
        alert = ErrorAlert(
            id=f"{alert_type}_{int(now.timestamp())}",
            error_code=alert_type,
            category='SYSTEM',
            severity=severity.value,
            message=message,
            count=1,
            first_seen=now,
            last_seen=now,
            affected_users=[],
            request_ids=[],
            context=context or {}
        )
        
        # Store alert
        self.active_alerts[alert.id] = alert
        
        # Log alert
        self.logger.critical(
            f"Alert triggered: {alert_type}",
            extra={
                'alert_type': alert_type,
                'severity': severity.value,
                'message': message,
                'context': context
            }
        )
        
        # Send alert (implement based on your alerting system)
        await self._send_alert(alert)
    
    async def _send_alert(self, alert: ErrorAlert):
        """Send alert to monitoring system"""
        # This is where you would integrate with your alerting system
        # Examples: PagerDuty, Slack, Email, etc.
        
        # For now, just log the alert
        self.logger.critical(f"ALERT: {alert.message}")
        
        # Example integrations:
        # await self._send_to_slack(alert)
        # await self._send_to_email(alert)
        # await self._send_to_pagerduty(alert)
    
    def _get_recent_errors(
        self,
        minutes: int,
        severity: str = None,
        category: str = None,
        error_code: str = None
    ) -> List[Dict]:
        """Get recent errors matching criteria"""
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        recent_errors = []
        
        for error in self.error_buffer:
            if error['timestamp'] < cutoff_time:
                continue
                
            if severity and error['severity'] != severity:
                continue
                
            if category and error['category'] != category:
                continue
                
            if error_code and error['error_code'] != error_code:
                continue
                
            recent_errors.append(error)
        
        return recent_errors
    

    
    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Get error statistics for the specified time period"""
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_errors = [
            error for error in self.error_buffer
            if error['timestamp'] >= cutoff_time
        ]
        
        # Calculate statistics
        stats = {
            'total_errors': len(recent_errors),
            'error_rate': self.performance_metrics['error_rate'],
            'avg_response_time': self.performance_metrics['avg_response_time'],
            'consecutive_errors': self.consecutive_errors,
            'last_success_time': self.last_success_time.isoformat(),
            'errors_by_category': defaultdict(int),
            'errors_by_severity': defaultdict(int),
            'errors_by_code': defaultdict(int),
            'affected_users': len(set(
                error['user_id'] for error in recent_errors
                if error['user_id']
            )),
            'top_errors': [],
            'error_timeline': []
        }
        
        # Group by category, severity, and code
        for error in recent_errors:
            stats['errors_by_category'][error['category']] += 1
            stats['errors_by_severity'][error['severity']] += 1
            stats['errors_by_code'][error['error_code']] += 1
        
        # Get top errors
        error_counts = defaultdict(int)
        for error in recent_errors:
            error_counts[error['error_code']] += 1
        
        stats['top_errors'] = sorted(
            error_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # Create timeline (hourly buckets)
        timeline = defaultdict(int)
        for error in recent_errors:
            hour_key = error['timestamp'].replace(minute=0, second=0, microsecond=0)
            timeline[hour_key] += 1
        
        stats['error_timeline'] = [
            {'timestamp': k.isoformat(), 'count': v}
            for k, v in sorted(timeline.items())
        ]
        
        return stats
    
    def get_active_alerts(self) -> List[Dict]:
        """Get active alerts"""
        return [asdict(alert) for alert in self.active_alerts.values()]
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an active alert"""
        if alert_id in self.active_alerts:
            del self.active_alerts[alert_id]
            self.logger.info(f"Alert resolved: {alert_id}")
            return True
        return False
    
    async def get_error_patterns(self) -> Dict[str, Any]:
        """Analyze error patterns"""
        
        patterns = {
            'recurring_errors': [],
            'user_error_patterns': [],
            'time_based_patterns': [],
            'correlation_patterns': []
        }
        
        # Find recurring errors
        for error_type, errors in self.error_patterns.items():
            if len(errors) >= 5:  # Minimum occurrences
                patterns['recurring_errors'].append({
                    'error_type': error_type,
                    'count': len(errors),
                    'first_seen': min(e['timestamp'] for e in errors).isoformat(),
                    'last_seen': max(e['timestamp'] for e in errors).isoformat(),
                    'affected_users': len(set(
                        e['user_id'] for e in errors if e['user_id']
                    ))
                })
        
        # Find users with high error rates
        for user_id, count in self.user_error_count.items():
            if count >= 10:  # Minimum error count
                patterns['user_error_patterns'].append({
                    'user_id': user_id,
                    'error_count': count
                })
        
        return patterns
    
    async def cleanup_old_data(self, days: int = 30):
        """Clean up old error data"""
        cutoff_time = datetime.utcnow() - timedelta(days=days)
        
        # Clean up in-memory data
        self.error_buffer = deque(
            [error for error in self.error_buffer if error['timestamp'] >= cutoff_time],
            maxlen=10000
        )
        
        # Clean up patterns
        for error_type in list(self.error_patterns.keys()):
            self.error_patterns[error_type] = [
                error for error in self.error_patterns[error_type]
                if error['timestamp'] >= cutoff_time
            ]
            
            # Remove empty patterns
            if not self.error_patterns[error_type]:
                del self.error_patterns[error_type]
        
        # Clean up old log files (optional - could implement log rotation)
        self.logger.info(f"Cleaned up error data older than {days} days")

    async def _write_error_to_file(self, error_data: Dict[str, Any]):
        """Write error data to log file"""
        try:
            # Convert datetime to string for JSON serialization
            error_data_copy = error_data.copy()
            error_data_copy['timestamp'] = error_data['timestamp'].isoformat()

            async with aiofiles.open(self.log_file, 'a', encoding='utf-8') as f:
                await f.write(json.dumps(error_data_copy) + '\n')
        except Exception as e:
            self.logger.error(f"Failed to write error to file: {e}")


# Global error monitor instance
error_monitor = ErrorMonitor()