"""
Enhanced McDonald's API Client V2
Modern, clean implementation with better error handling and structure
"""
import asyncio
import json
import time
import random
from typing import Dict, Any, Optional, List, Union
from urllib.parse import urljoin

from loguru import logger

from config.settings import settings
from core.exceptions import (
    MCDAPIError, MCDAuthenticationError, MCDBusinessError, 
    MCDValidationError, MCDTimeoutError
)
from core.http_client import MCDHttpClient


class MCDClientV2:
    """Enhanced McDonald's API Client V2"""
    
    def __init__(self, redis_client):
        """Initialize MCD Client V2"""
        self.base_url = settings.mcd_api.base_url
        self.http_client = MCDHttpClient()
        self.redis_client = redis_client
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.http_client.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.http_client.close()
    
    def _build_url(self, path: str) -> str:
        """Build full URL from path"""
        return urljoin(self.base_url, path.lstrip('/'))
    
    def _validate_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate API response and handle common error cases
        
        Args:
            response: API response
            
        Returns:
            Validated response
            
        Raises:
            MCDAuthenticationError: Authentication failed
            MCDBusinessError: Business logic error
            MCDAPIError: General API error
        """
        if not isinstance(response, dict):
            raise MCDAPIError("Invalid response format")
        
        # Check for authentication errors
        if response.get('code') == 401 or response.get('code') == '401':
            raise MCDAuthenticationError("Authentication failed - invalid or expired SID")
        
        # Check for business errors
        if not response.get('success', True) and response.get('code') != 200:
            error_msg = response.get('message', 'Unknown error')
            error_code = response.get('code')
            
            # Common business error codes
            if error_code in ['INSUFFICIENT_BALANCE', 'COUPON_EXPIRED', 'PRODUCT_UNAVAILABLE']:
                raise MCDBusinessError(error_msg, error_code)
            
            raise MCDAPIError(f"API Error: {error_msg}", error_code=error_code)
        
        return response
    
    # =================== Store & Location APIs ===================
    
    async def get_all_cities(self) -> Dict[str, Any]:
        """Get all available cities"""
        url = self._build_url('/bff/store/cities/group')
        response = await self.http_client.get(url)
        return self._validate_response(response)
    
    async def get_city_by_coordinate(self, lat: float, lng: float) -> Dict[str, Any]:
        """
        Get city by coordinates

        Args:
            lat: Latitude
            lng: Longitude

        Returns:
            City information
        """
        # Try different API endpoints that might work
        # First try the same structure as cities/group
        url = self._build_url('/bff/store/cities')
        params = {'lat': lat, 'lng': lng}
        response = await self.http_client.get(url, params=params)
        return self._validate_response(response)
    
    async def get_nearby_stores(self, 
                              lat: float, 
                              lng: float, 
                              show_type: int = 2,
                              be_type: int = 1,
                              order_type: int = 1) -> Dict[str, Any]:
        """
        Get nearby stores by coordinates
        
        Args:
            lat: Latitude
            lng: Longitude
            show_type: Show type (default: 2)
            be_type: Business entity type (default: 1)
            order_type: Order type (default: 1)
            
        Returns:
            Nearby stores
        """
        url = self._build_url('/bff/store/stores/vicinity')
        params = {
            'lat': lat,
            'lng': lng,
            'showType': show_type,
            'beType': be_type,
            'orderType': order_type
        }
        response = await self.http_client.get(url, params=params)
        logger.debug(f"get_nearby_stores response: {response}")
        return self._validate_response(response)
    
    async def search_stores_by_keyword(self,
                                     city_code: str,
                                     keyword: str = '',
                                     page_no: int = 1,
                                     page_size: int = 10) -> Dict[str, Any]:
        """
        Search stores by keyword
        
        Args:
            city_code: City code
            keyword: Search keyword
            page_no: Page number
            page_size: Page size
            
        Returns:
            Store search results
        """
        url = self._build_url('/bff/store/stores')
        params = {
            'cityCode': city_code,
            'keyword': keyword,
            'pageNo': page_no,
            'pageSize': page_size
        }
        response = await self.http_client.get(url, params=params)
        return self._validate_response(response)
    
    async def get_store_info(self, store_code: str) -> Dict[str, Any]:
        """
        Get store information
        
        Args:
            store_code: Store code
            
        Returns:
            Store information
        """
        url = self._build_url(f'/bff/store/stores/{store_code}')
        response = await self.http_client.get(url)
        return self._validate_response(response)
    
    # =================== Menu & Product APIs ===================
    
    def _get_day_part_code(self) -> int:
        """
        Get current day part code based on time
        
        Returns:
            Day part code: 1=早餐, 8=午餐, 4=下午茶, 5=夜市
        """
        from datetime import datetime, timezone, timedelta
        
        local_now = datetime.now(timezone(timedelta(hours=8), name='Asia/Shanghai'))
        local_time = local_now.strftime("%H:%M:%S")
        
        if '05:00:00' <= local_time < '10:30:00':
            return 1  # 早餐
        elif '10:30:00' <= local_time < '14:30:00':
            return 8  # 午餐
        elif '14:30:00' <= local_time < '17:00:00':
            return 4  # 下午茶
        else:
            return 5  # 夜市
    
    async def get_store_menu(self,
                           store_code: str,
                           order_type: int = 1,
                           be_type: int = 1,
                           be_code: str = '',
                           day_part_code: Optional[int] = None) -> Dict[str, Any]:
        """
        Get store menu
        
        Args:
            store_code: Store code
            order_type: Order type (1=到店, 2=外卖)
            be_type: Business entity type
            be_code: Business entity code
            day_part_code: Day part code (auto-detected if None)
            
        Returns:
            Store menu
        """
        if day_part_code is None:
            day_part_code = self._get_day_part_code()
        
        url = self._build_url('/bff/spc/menu')
        params = {
            'storeCode': store_code,
            'orderType': order_type,
            'beType': be_type,
            'beCode': be_code,
            'dayPartCode': day_part_code
        }
        response = await self.http_client.get(url, params=params)
        return self._validate_response(response)
    
    async def get_product_detail(self,
                               store_code: str,
                               product_code: str,
                               channel_code: str = '03',
                               order_type: int = 1,
                               be_code: str = '',
                               day_part_code: Optional[int] = None) -> Dict[str, Any]:
        """
        Get product detail
        
        Args:
            store_code: Store code
            product_code: Product code
            channel_code: Channel code
            order_type: Order type
            be_code: Business entity code
            day_part_code: Day part code
            
        Returns:
            Product detail
        """
        if day_part_code is None:
            day_part_code = self._get_day_part_code()
        
        url = self._build_url(f'/bff/spc/products/detail/{product_code}')
        params = {
            'storeCode': store_code,
            'channelCode': channel_code,
            'orderType': order_type,
            'beCode': be_code,
            'dayPartCode': day_part_code
        }
        response = await self.http_client.get(url, params=params, path=product_code)
        return self._validate_response(response)
    
    # =================== User & Authentication APIs ===================
    
    async def get_user_info(self, sid: str) -> Dict[str, Any]:
        """
        Get user information
        
        Args:
            sid: Session ID
            
        Returns:
            User information
        """
        url = self._build_url('/bff/user/portal/info')
        response = await self.http_client.get(url, sid=sid)
        return self._validate_response(response)
    
    async def get_wallet_balance(self, sid: str) -> Dict[str, Any]:
        """
        Get user wallet balance
        
        Args:
            sid: Session ID
            
        Returns:
            Wallet balance information
        """
        url = self._build_url('/bff/user/wallet/balance')
        response = await self.http_client.get(url, sid=sid)
        return self._validate_response(response)
    
    async def login_refresh(self, sid: str) -> Dict[str, Any]:
        """
        Refresh login session
        
        Args:
            sid: Session ID
            
        Returns:
            Refresh result
        """
        url = self._build_url('/bff/passport/login/refresh')
        data = {}
        response = await self.http_client.post(url, json_data=data, sid=sid)
        return self._validate_response(response)

    async def send_sms_code(self, phone: str) -> Dict[str, Any]:
        """
        Send SMS verification code via mcdapi
        
        Args:
            phone: Phone number
            
        Returns:
            SMS send result
        """
        url = self._build_url('/bff/sms/sendCode')
        data = {
            'phone': phone,
            'bizType': 'login'
        }
        response = await self.http_client.post(url, json_data=data)
        result = self._validate_response(response)
        
        # Store verification code from API response
        if result.get('success') and 'code' in result.get('data', {}):
            code = result['data']['code']
            await self.redis_client.setex(f"sms_code:{phone}", 300, code)
            logger.info(f"SMS verification code stored for {phone}")
        
        return result

    async def login_by_sms(self, phone: str, code: str) -> Dict[str, Any]:
        """
        Login with SMS verification code
        
        Args:
            phone: Phone number
            code: Verification code
            
        Returns:
            Login result with session ID
        """
        # Verify code first
        stored_code = await self.redis_client.get(f"sms_code:{phone}")
        if not stored_code or stored_code != code:
            raise MCDBusinessError("Invalid or expired verification code", "INVALID_VERIFICATION_CODE")
        
        # Call mcdapi login interface
        url = self._build_url('/bff/passport/login/sms')
        data = {
            'phone': phone,
            'code': code
        }
        response = await self.http_client.post(url, json_data=data)
        result = self._validate_response(response)
        
        # Return login result with SID
        return result

    async def register_by_sms(self, phone: str, code: str, username: str) -> Dict[str, Any]:
        """
        Register user by SMS verification code
        
        Args:
            phone: Phone number
            code: Verification code
            username: Username
            
        Returns:
            Registration result
        """
        # Get code from Redis
        redis_client = self.redis_client
        stored_code = await redis_client.get(f"sms_code:{phone}")
        
        if not stored_code or stored_code != code:
            raise MCDBusinessError("Invalid or expired verification code", "INVALID_VERIFICATION_CODE")
        
        # In real implementation, create user in database here
        logger.info(f"User registered successfully: {username} ({phone})")
        
        # Generate mock SID
        sid = CryptoUtils.generate_device_id()
        
        return {
            "success": True,
            "data": {
                "sid": sid,
                "username": username,
                "phone": phone
            }
        }
    
    async def logout(self, sid: str) -> Dict[str, Any]:
        """
        Logout user
        
        Args:
            sid: Session ID
            
        Returns:
            Logout result
        """
        url = self._build_url('/bff/passport/login/logout')
        data = {}
        response = await self.http_client.post(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    # =================== Coupon & Promotion APIs ===================
    
    async def get_user_coupons(self, sid: str) -> Dict[str, Any]:
        """
        Get user's available coupons
        
        Args:
            sid: Session ID
            
        Returns:
            User coupons
        """
        url = self._build_url('/bff/promotion/coupons/rightCards')
        response = await self.http_client.get(url, sid=sid)
        return self._validate_response(response)
    
    async def get_available_coupons(self, sid: str) -> Dict[str, Any]:
        """
        Get available coupons
        
        Args:
            sid: Session ID
            
        Returns:
            Available coupons
        """
        url = self._build_url('/bff/promotion/coupons/v2')
        response = await self.http_client.get(url, sid=sid)
        return self._validate_response(response)
    
    async def get_coupon_detail(self, 
                              sid: str, 
                              coupon_id: str, 
                              coupon_code: str) -> Dict[str, Any]:
        """
        Get coupon detail
        
        Args:
            sid: Session ID
            coupon_id: Coupon ID
            coupon_code: Coupon code
            
        Returns:
            Coupon detail
        """
        url = self._build_url(f'/bff/promotion/coupons/{coupon_code}')
        params = {'couponId': coupon_id}
        response = await self.http_client.get(url, params=params, sid=sid)
        return self._validate_response(response)
    
    async def get_coupon_products(self,
                                sid: str,
                                coupon_id: str,
                                coupon_code: str,
                                promotion_id: str,
                                store_code: str,
                                order_type: int = 1,
                                be_code: str = '',
                                daypart_codes: Optional[int] = None,
                                date: Optional[str] = None,
                                time: Optional[str] = None) -> Dict[str, Any]:
        """
        Get products that can use the coupon
        
        Args:
            sid: Session ID
            coupon_id: Coupon ID
            coupon_code: Coupon code
            promotion_id: Promotion ID
            store_code: Store code
            order_type: Order type
            be_code: Business entity code
            daypart_codes: Day part codes
            date: Date
            time: Time
            
        Returns:
            Available products for coupon
        """
        if daypart_codes is None:
            daypart_codes = self._get_day_part_code()
        
        url = self._build_url('/bff/promotion/coupons/products')
        params = {
            'couponId': coupon_id,
            'couponCode': coupon_code,
            'promotionId': promotion_id,
            'storeCode': store_code,
            'orderType': order_type,
            'beCode': be_code,
            'daypartCodes': daypart_codes
        }
        
        if date:
            params['date'] = date
        if time:
            params['time'] = time
        
        response = await self.http_client.get(url, params=params, sid=sid)
        return self._validate_response(response)
    
    # =================== Cart APIs ===================
    
    async def add_to_cart(self,
                         sid: str,
                         store_code: str,
                         product_code: str = '',
                         cart_type: int = 1,
                         channel_code: str = '03',
                         be_type: int = 1,
                         be_code: str = '',
                         day_part_code: Optional[int] = None,
                         coupon_code: str = '',
                         coupon_id: str = '',
                         promotion_id: str = '',
                         order_type: int = 1,
                         quantity: int = 1,
                         customization: Optional[List] = None,
                         combo_items: Optional[List] = None,
                         sub_product_code: str = '',
                         membership_code: str = '',
                         product_type: int = 1,
                         card_type: int = 0,
                         card_id: str = '') -> Dict[str, Any]:
        """
        Add item to cart
        
        Args:
            sid: Session ID
            store_code: Store code
            product_code: Product code
            cart_type: Cart type
            channel_code: Channel code
            be_type: Business entity type
            be_code: Business entity code
            day_part_code: Day part code
            coupon_code: Coupon code
            coupon_id: Coupon ID
            promotion_id: Promotion ID
            order_type: Order type
            quantity: Quantity
            customization: Customization options
            combo_items: Combo items
            sub_product_code: Sub product code
            membership_code: Membership code
            product_type: Product type
            card_type: Card type
            card_id: Card ID
            
        Returns:
            Add to cart result
        """
        if day_part_code is None:
            day_part_code = self._get_day_part_code()
        
        if customization is None:
            customization = []
        if combo_items is None:
            combo_items = []
        
        url = self._build_url('/bff/cart/carts')
        data = {
            'storeCode': store_code,
            'productCode': product_code,
            'subProductCode': sub_product_code,
            'couponCode': coupon_code,
            'couponId': coupon_id,
            'promotionId': promotion_id,
            'cartType': cart_type,
            'channelCode': channel_code,
            'beType': be_type,
            'beCode': be_code,
            'orderType': order_type,
            'quantity': quantity,
            'customization': customization,
            'comboItems': combo_items,
            'membershipCode': membership_code,
            'productType': product_type,
            'cardType': card_type,
            'cardId': card_id,
            'dayPartCode': day_part_code
        }
        
        response = await self.http_client.put(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    async def empty_cart(self,
                        sid: str,
                        store_code: str,
                        cart_type: int = 1,
                        channel_code: str = '03',
                        be_type: int = 1,
                        be_code: str = '',
                        day_part_code: Optional[int] = None,
                        order_type: int = 1) -> Dict[str, Any]:
        """
        Empty shopping cart
        
        Args:
            sid: Session ID
            store_code: Store code
            cart_type: Cart type
            channel_code: Channel code
            be_type: Business entity type
            be_code: Business entity code
            day_part_code: Day part code
            order_type: Order type
            
        Returns:
            Empty cart result
        """
        if day_part_code is None:
            day_part_code = self._get_day_part_code()
        
        url = self._build_url('/bff/cart/carts/empty')
        data = {
            'storeCode': store_code,
            'cartType': cart_type,
            'channelCode': channel_code,
            'beType': be_type,
            'beCode': be_code,
            'dayPartCode': day_part_code,
            'orderType': order_type
        }
        
        response = await self.http_client.put(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    async def get_cart_validation_info(self,
                                     sid: str,
                                     store_code: str,
                                     channel_code: str = '03',
                                     order_type: int = 1,
                                     cart_type: int = 1,
                                     day_part_code: Optional[int] = None,
                                     be_code: str = '',
                                     address_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get cart validation information before checkout
        
        Args:
            sid: Session ID
            store_code: Store code
            channel_code: Channel code
            order_type: Order type
            cart_type: Cart type
            day_part_code: Day part code
            be_code: Business entity code
            address_id: Address ID (required for delivery orders)
            
        Returns:
            Cart validation info
        """
        if day_part_code is None:
            day_part_code = self._get_day_part_code()
        
        url = self._build_url('/bff/order/confirmation/validationinfo')
        data = {
            'storeCode': store_code,
            'orderType': order_type,
            'cartType': cart_type,
            'channelCode': channel_code,
            'beCode': be_code,
            'dayPartCode': day_part_code
        }
        
        if address_id:
            data['addressId'] = address_id
        
        response = await self.http_client.post(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    # =================== Order APIs ===================
    
    async def make_order(self,
                        sid: str,
                        validation_info: Dict[str, Any],
                        store_code: str,
                        order_type: int = 1,
                        day_part_code: Optional[int] = None,
                        be_type: int = 1,
                        be_code: str = '',
                        eat_type: str = 'take-in-store',
                        cash_coupon_code: str = '',
                        cash_coupon_id: str = '',
                        address_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create order
        
        Args:
            sid: Session ID
            validation_info: Cart validation info
            store_code: Store code
            order_type: Order type
            day_part_code: Day part code
            be_type: Business entity type
            be_code: Business entity code
            eat_type: Eating type
            cash_coupon_code: Cash coupon code
            cash_coupon_id: Cash coupon ID
            address_id: Address ID
            
        Returns:
            Order creation result
        """
        if day_part_code is None:
            day_part_code = self._get_day_part_code()
        
        url = self._build_url('/bff/order/orders')
        data = {
            'storeCode': store_code,
            'orderType': order_type,
            'cartType': 1,
            'channelCode': '03',
            'beCode': be_code,
            'beType': be_type,
            'dayPartCode': day_part_code,
            'eatType': eat_type,
            'cashCouponCode': cash_coupon_code,
            'cashCouponId': cash_coupon_id
        }
        
        if address_id:
            data['addressId'] = address_id
        
        # Merge validation info
        data.update(validation_info)
        
        response = await self.http_client.post(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    async def get_order_detail(self, sid: str, order_id: str) -> Dict[str, Any]:
        """
        Get order detail
        
        Args:
            sid: Session ID
            order_id: Order ID
            
        Returns:
            Order detail
        """
        url = self._build_url(f'/bff/order/orders/{order_id}')
        response = await self.http_client.get(url, sid=sid)
        return self._validate_response(response)
    
    async def cancel_order(self, 
                          sid: str, 
                          order_id: str, 
                          cancel_reason_code: str = '1') -> Dict[str, Any]:
        """
        Cancel order
        
        Args:
            sid: Session ID
            order_id: Order ID
            cancel_reason_code: Cancel reason code
            
        Returns:
            Cancel result
        """
        url = self._build_url(f'/bff/order/orders/{order_id}/cancellation')
        data = {'cancelReasonCode': cancel_reason_code}
        response = await self.http_client.put(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    # =================== Payment APIs ===================
    
    async def prepare_order(self, 
                           sid: str, 
                           pay_id: str, 
                           pay_channel: str = 'ALI') -> Dict[str, Any]:
        """
        Prepare order for payment
        
        Args:
            sid: Session ID
            pay_id: Payment ID
            pay_channel: Payment channel (ALI, WX, ARCHCARD)
            
        Returns:
            Payment preparation result
        """
        url = self._build_url('/bff/cashier/preorder')
        data = {
            'payId': pay_id,
            'payChannel': pay_channel
        }
        response = await self.http_client.post(url, json_data=data, sid=sid)
        return self._validate_response(response)
    
    async def pay_by_balance(self, sid: str, pay_id: str) -> Dict[str, Any]:
        """
        Pay by account balance
        
        Args:
            sid: Session ID
            pay_id: Payment ID
            
        Returns:
            Payment result
        """
        url = self._build_url('/bff/cashier/archcard')
        data = {'payId': pay_id}
        response = await self.http_client.post(url, json_data=data, sid=sid)
        return self._validate_response(response)