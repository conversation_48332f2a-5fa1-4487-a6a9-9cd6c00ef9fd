"""
McDonald's API Server V3 - FastAPI Implementation
High-performance async API server with automatic documentation
"""
import time
import json
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict
from urllib.parse import quote_plus

import redis.asyncio as redis
from fastapi import FastAPI, Depends, HTTPException, Request, status, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

# 导入认证模块
from core.auth import check_rate_limit, get_current_user, require_permissions, require_roles

# 导入CORS安全模块
from core.cors_security import CORSSecurityConfig, CORSSecurityMiddleware, CORSSecurityMonitor

from config.settings import settings
from config.simple_config import config
from core import (MCDAuthenticationError, MCDBaseError, MCDBusinessError,
                  MCDClientV2, MCDValidationError)
from models.simple_schemas import (AddToCartRequest, BalancePayRequest,
                            CancelOrderRequest, CartValidationRequest,
                            CoordinateRequest, CouponDetailRequest,
                            CouponProductsRequest, CreateOrderRequest,
                            CryptoTestRequest, CryptoTestResponse,
                            EmptyCartRequest, ErrorResponse,
                            HealthCheckResponse, MenuRequest,
                            NearbyStoresRequest, PrepareOrderRequest,
                            ProductDetailRequest, ServiceInfo,
                            StoreSearchRequest, SuccessResponse,
                            UserInfoRequest, SendSmsCodeRequest, RegisterBySmsRequest, LoginBySmsRequest)
from utils import CryptoUtils


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan management for resource initialization and cleanup.
    - Initializes and closes a shared MCDClientV2 instance for performance.
    - Initializes and closes Redis connection.
    """
    # --- Startup ---
    logger.info("Starting McDonald's API Server V3...")

    # Initialize a shared MCDClientV2 instance to be reused across the application
    # Initialize Redis connection for MCD client
    redis_client = redis.Redis(host='localhost', port=6379)
    mcd_client = MCDClientV2(redis_client)
    app.state.mcd_client = mcd_client
    logger.info("Shared MCD API client initialized.")

    # Initialize Redis connection for application
    redis_client = redis.Redis(
        host=settings.redis.host,
        port=settings.redis.port,
        db=settings.redis.db,
        password=settings.redis.password if settings.redis.password else None,
        decode_responses=True
    )
    app.state.redis_client = redis_client

    # Test Redis connection
    try:
        await app.state.redis_client.ping()
        logger.info("Redis connection established.")
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")

    # Initialize CORS security
    cors_config = CORSSecurityConfig()
    cors_monitor = CORSSecurityMonitor(cors_config)
    app.state.cors_config = cors_config
    app.state.cors_monitor = cors_monitor
    logger.info("CORS security system initialized.")

    logger.info("Server startup completed.")

    yield

    # --- Shutdown ---
    logger.info("Shutting down server...")

    # Close shared MCD client connection
    if hasattr(app.state, 'mcd_client') and app.state.mcd_client:
        await app.state.mcd_client.__aexit__(None, None, None)
        logger.info("Shared MCD API client closed.")

    # Close Redis connection
    if hasattr(app.state, 'redis_client') and app.state.redis_client:
        await app.state.redis_client.close()
        logger.info("Redis connection closed.")

    logger.info("Server shutdown completed.")


# --- FastAPI App Initialization ---
app = FastAPI(
    title=settings.title,
    description=settings.description,
    version=settings.version,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# --- Middleware ---
# Enhanced CORS Security Configuration
cors_config = CORSSecurityConfig()
app.add_middleware(CORSSecurityMiddleware, config=cors_config)

# Use allowed_hosts from settings for better security
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.server.allowed_hosts
)


# --- Dependencies ---
def get_mcd_client(request: Request) -> MCDClientV2:
    """Dependency to get the shared MCD client instance from application state."""
    return request.app.state.mcd_client


def get_redis_client(request: Request) -> redis.Redis:
    """Dependency to get the shared Redis client instance from application state."""
    return request.app.state.redis_client


def get_cors_config(request: Request) -> CORSSecurityConfig:
    """Dependency to get the CORS security config from application state."""
    return request.app.state.cors_config


def get_cors_monitor(request: Request) -> CORSSecurityMonitor:
    """Dependency to get the CORS security monitor from application state."""
    return request.app.state.cors_monitor


# --- Exception Handlers ---
# Using .model_dump() instead of deprecated .dict()
@app.exception_handler(MCDAuthenticationError)
async def authentication_error_handler(request: Request, exc: MCDAuthenticationError):
    """Handle authentication errors with a 401 status code."""
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content=ErrorResponse(
            error="AUTHENTICATION_ERROR",
            message=str(exc),
            code=401,
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )


@app.exception_handler(MCDBusinessError)
async def business_error_handler(request: Request, exc: MCDBusinessError):
    """Handle business logic errors with a 400 status code."""
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=ErrorResponse(
            error="BUSINESS_ERROR",
            message=str(exc),
            code=400,
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )


@app.exception_handler(MCDValidationError)
async def validation_error_handler(request: Request, exc: MCDValidationError):
    """Handle validation errors with a 422 status code."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(
            error="VALIDATION_ERROR",
            message=str(exc),
            code=422,
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )


@app.exception_handler(MCDBaseError)
async def mcd_error_handler(request: Request, exc: MCDBaseError):
    """Handle general MCD errors with a 500 status code."""
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="MCD_API_ERROR",
            message=str(exc),
            code=500,
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle FastAPI's built-in HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error="HTTP_ERROR",
            message=exc.detail,
            code=exc.status_code,
            timestamp=datetime.now().isoformat()
        ).model_dump()
    )


# =================== User Authentication APIs ===================

@app.post(
    "/user/send-sms-code",
    response_model=SuccessResponse,
    tags=["User"],
    summary="Send SMS Verification Code",
    description="Send verification code to phone number"
)
async def send_sms_code(
    request: SendSmsCodeRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Send SMS verification code"""
    result = await client.send_sms_code(request.phone)
    return SuccessResponse(message="Success", data=result.get('data'))


@app.post(
    "/user/register-by-sms",
    response_model=SuccessResponse,
    tags=["User"],
    summary="Register by SMS Verification Code",
    description="Register new user using phone number and verification code"
)
async def register_by_sms(
    request: RegisterBySmsRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Register user by SMS verification code"""
    result = await client.register_by_sms(request.phone, request.code, request.username)
    return SuccessResponse(message="Success", data=result.get('data'))

@app.post(
    "/user/login-by-sms",
    response_model=SuccessResponse,
    tags=["User"],
    summary="Login by SMS Verification Code",
    description="Login using phone number and SMS verification code"
)
async def login_by_sms(
    request: LoginBySmsRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Login by SMS verification code"""
    result = await client.login_by_sms(request.phone, request.code)
    return SuccessResponse(message="Success", data=result.get('data'))

@app.post(
    "/admin/add-sid-by-sms",
    response_model=SuccessResponse,
    tags=["Admin"],
    summary="Add SID Account by SMS",
    description="Add new SID account using phone number and verification code"
)
async def add_sid_by_sms(
    request: LoginBySmsRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Add SID account by verifying phone number and SMS code"""
    # Verify SMS code and get SID
    result = await client.login_by_sms(request.phone, request.code)
    # In real implementation, store SID and phone mapping in database here
    logger.info(f"New SID account added: {request.phone} -> {result.get('data', {}).get('sid')}")
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Health Check & Info ===================

@app.get(
    "/health",
    response_model=HealthCheckResponse,
    tags=["Health"],
    summary="Health Check",
    description="Check the health status of the API server and its dependencies"
)
async def health_check(redis_client: redis.Redis = Depends(get_redis_client)):
    """Health check endpoint."""
    redis_status = "healthy"
    try:
        await redis_client.ping()
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        redis_status = "unhealthy"

    return HealthCheckResponse(
        status="healthy" if redis_status == "healthy" else "degraded",
        message="MCD API Server is running",
        timestamp=str(int(time.time() * 1000)),
        version=settings.version,
        environment=config.environment
    )


@app.get(
    "/",
    response_model=SuccessResponse,
    tags=["Info"],
    summary="Service Information",
    description="Get basic information about the API server"
)
async def get_service_info():
    """Get service information."""
    service_info = ServiceInfo(
        name=settings.title,
        version=settings.version,
        description=settings.description
    )
    return SuccessResponse(data=service_info.model_dump())


# =================== Security Monitoring APIs ===================

@app.get(
    "/security/cors/report",
    response_model=SuccessResponse,
    tags=["Security"],
    summary="CORS Security Report",
    description="Get CORS security monitoring report (admin only)",
    include_in_schema=settings.server.debug
)
async def get_cors_security_report(
    monitor: CORSSecurityMonitor = Depends(get_cors_monitor)
):
    """Get CORS security monitoring report."""
    if not settings.server.debug:
        raise HTTPException(status_code=404, detail="Endpoint not available in production")
    
    report = monitor.get_security_report()
    return SuccessResponse(data=report)


@app.post(
    "/security/cors/reset",
    response_model=SuccessResponse,
    tags=["Security"],
    summary="Reset CORS Security Statistics",
    description="Reset CORS security monitoring statistics (admin only)",
    include_in_schema=settings.server.debug
)
async def reset_cors_security_stats(
    monitor: CORSSecurityMonitor = Depends(get_cors_monitor)
):
    """Reset CORS security statistics."""
    if not settings.server.debug:
        raise HTTPException(status_code=404, detail="Endpoint not available in production")
    
    monitor.reset_statistics()
    return SuccessResponse(data={"message": "CORS security statistics reset successfully"})


@app.post(
    "/security/cors/unblock-ip",
    response_model=SuccessResponse,
    tags=["Security"],
    summary="Unblock IP Address",
    description="Remove an IP address from the block list (admin only)",
    include_in_schema=settings.server.debug
)
async def unblock_ip(
    ip_address: str,
    config: CORSSecurityConfig = Depends(get_cors_config)
):
    """Unblock an IP address."""
    if not settings.server.debug:
        raise HTTPException(status_code=404, detail="Endpoint not available in production")
    
    if ip_address in config.blocked_ips:
        config.blocked_ips.remove(ip_address)
        # Reset attack counter
        config.attack_counters.pop(ip_address, None)
        logger.info(f"IP {ip_address} has been unblocked")
        return SuccessResponse(data={"message": f"IP {ip_address} unblocked successfully"})
    else:
        return SuccessResponse(data={"message": f"IP {ip_address} was not blocked"})


# =================== Store & Location APIs ===================

@app.get(
    "/store/cities/group",
    response_model=SuccessResponse,
    tags=["Store"],
    summary="Get All Cities",
    description="Get all available cities for McDonald's stores"
)
async def get_all_cities(client: MCDClientV2 = Depends(get_mcd_client)):
    """Get all available cities."""
    result = await client.get_all_cities()
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/store/cities",
    response_model=SuccessResponse,
    tags=["Store"],
    summary="Get City by Coordinates",
    description="Get city information based on latitude and longitude"
)
async def get_city_by_coordinate(
    lat: float = Query(..., description="Latitude"),
    lng: float = Query(..., description="Longitude"),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get city by coordinates."""
    result = await client.get_city_by_coordinate(lat, lng)
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/store/stores/vicinity",
    response_model=SuccessResponse,
    tags=["Store"],
    summary="Get Nearby Stores",
    description="Get nearby McDonald's stores based on coordinates"
)
async def get_nearby_stores(
    lat: float = Query(..., description="Latitude"),
    lng: float = Query(..., description="Longitude"),
    showType: int = Query(2, description="Show type"),
    beType: int = Query(1, description="Business entity type"),
    orderType: int = Query(1, description="Order type"),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get nearby stores."""
    result = await client.get_nearby_stores(
        lat, lng, showType, beType, orderType
    )
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/store/stores",
    response_model=SuccessResponse,
    tags=["Store"],
    summary="Search Stores",
    description="Search stores by keyword within a city"
)
async def search_stores_by_keyword(
    cityCode: str = Query(..., description="City code"),
    keyword: str = Query("", description="Search keyword"),
    pageNo: int = Query(1, description="Page number"),
    pageSize: int = Query(10, description="Page size"),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Search stores by keyword."""
    result = await client.search_stores_by_keyword(
        cityCode, keyword, pageNo, pageSize
    )
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/store/stores/{store_code}",
    response_model=SuccessResponse,
    tags=["Store"],
    summary="Get Store Information",
    description="Get detailed information about a specific store"
)
async def get_store_info(
    store_code: str,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get store information."""
    result = await client.get_store_info(store_code)
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Menu & Product APIs ===================

@app.get(
    "/spc/menu",
    response_model=SuccessResponse,
    tags=["Menu"],
    summary="Get Store Menu",
    description="Get the menu for a specific store"
)
async def get_store_menu(
    storeCode: str = Query(..., description="Store code"),
    orderType: int = Query(1, description="Order type"),
    beType: int = Query(1, description="Business entity type"),
    beCode: str = Query("", description="Business entity code"),
    dayPartCode: int = Query(8, description="Day part code"),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get store menu."""
    result = await client.get_store_menu(
        storeCode, orderType, beType, beCode, dayPartCode
    )
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/spc/products/detail/{product_code}",
    response_model=SuccessResponse,
    tags=["Menu"],
    summary="Get Product Detail",
    description="Get detailed information about a specific product"
)
async def get_product_detail(
    product_code: str,
    storeCode: str = Query(..., description="Store code"),
    channelCode: str = Query("03", description="Channel code"),
    orderType: int = Query(1, description="Order type"),
    beCode: str = Query("", description="Business entity code"),
    dayPartCode: int = Query(8, description="Day part code"),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get product detail."""
    result = await client.get_product_detail(
        storeCode, product_code, channelCode, orderType, beCode, dayPartCode
    )
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== User APIs ===================

@app.get(
    "/user/portal/info",
    response_model=SuccessResponse,
    tags=["User"],
    summary="Get User Information",
    description="Get user information including wallet balance"
)
async def get_user_info(
    request: UserInfoRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get user information."""
    # Get user info
    user_info = await client.get_user_info(request.debug_sid)

    # Try to get wallet balance
    try:
        balance_info = await client.get_wallet_balance(request.debug_sid)
        if balance_info.get('success') and 'data' in balance_info:
            user_info['data']['walletBalance'] = balance_info['data'].get('balance', 'N/A')
        else:
            user_info['data']['walletBalance'] = 'N/A'
    except Exception as e:
        logger.warning(f"Failed to get wallet balance: {e}")
        user_info['data']['walletBalance'] = 'N/A'

    return SuccessResponse(data=user_info)


@app.get(
    "/user/wallet/balance",
    response_model=SuccessResponse,
    tags=["User"],
    summary="Get Wallet Balance",
    description="Get user's wallet balance"
)
async def get_wallet_balance(
    request: UserInfoRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get wallet balance."""
    result = await client.get_wallet_balance(request.debug_sid)
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Coupon APIs ===================

@app.get(
    "/promotion/coupons/rightCards",
    response_model=SuccessResponse,
    tags=["Coupons"],
    summary="Get User Coupons",
    description="Get user's available coupons and right cards"
)
async def get_user_coupons(
    request: UserInfoRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get user coupons."""
    result = await client.get_user_coupons(request.debug_sid)
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/promotion/coupons/v2",
    response_model=SuccessResponse,
    tags=["Coupons"],
    summary="Get Available Coupons",
    description="Get all available coupons for the user"
)
async def get_available_coupons(
    request: UserInfoRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get available coupons."""
    result = await client.get_available_coupons(request.debug_sid)
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/promotion/coupons/{coupon_code}",
    response_model=SuccessResponse,
    tags=["Coupons"],
    summary="Get Coupon Detail",
    description="Get detailed information about a specific coupon"
)
async def get_coupon_detail(
    coupon_code: str,
    request: CouponDetailRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get coupon detail."""
    result = await client.get_coupon_detail(request.debug_sid, request.coupon_id, coupon_code)
    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/promotion/coupons/products",
    response_model=SuccessResponse,
    tags=["Coupons"],
    summary="Get Coupon Products",
    description="Get products that can be purchased with a specific coupon"
)
async def get_coupon_products(
    request: CouponProductsRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get coupon products."""
    result = await client.get_coupon_products(
        request.debug_sid, request.coupon_id, request.coupon_code,
        request.promotion_id, request.store_code, request.order_type,
        request.be_code, request.daypart_codes, request.date, request.time
    )
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Cart APIs ===================

@app.put(
    "/cart/carts",
    response_model=SuccessResponse,
    tags=["Cart"],
    summary="Add to Cart",
    description="Add a product to the shopping cart"
)
async def add_to_cart(
    request: AddToCartRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Add item to cart."""
    result = await client.add_to_cart(
        sid=request.debug_sid,
        store_code=request.store_code,
        product_code=request.product_code,
        cart_type=request.cart_type,
        channel_code=request.channel_code,
        be_type=request.be_type,
        be_code=request.be_code,
        day_part_code=request.day_part_code,
        coupon_code=request.coupon_code,
        coupon_id=request.coupon_id,
        promotion_id=request.promotion_id,
        order_type=request.order_type,
        quantity=request.quantity,
        customization=[item.model_dump() for item in request.customization],
        combo_items=[item.model_dump() for item in request.combo_items],
        sub_product_code=request.sub_product_code,
        membership_code=request.membership_code,
        product_type=request.product_type,
        card_type=request.card_type,
        card_id=request.card_id
    )
    return SuccessResponse(message="Success", data=result.get('data'))


@app.put(
    "/cart/carts/empty",
    response_model=SuccessResponse,
    tags=["Cart"],
    summary="Empty Cart",
    description="Empty the shopping cart"
)
async def empty_cart(
    request: EmptyCartRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Empty cart."""
    result = await client.empty_cart(
        sid=request.debug_sid,
        store_code=request.store_code,
        cart_type=request.cart_type,
        channel_code=request.channel_code,
        be_type=request.be_type,
        be_code=request.be_code,
        day_part_code=request.day_part_code,
        order_type=request.order_type
    )
    return SuccessResponse(message="Success", data=result.get('data'))


@app.post(
    "/order/confirmation/validationinfo",
    response_model=SuccessResponse,
    tags=["Cart"],
    summary="Get Cart Validation Info",
    description="Get cart validation information before checkout"
)
async def get_cart_validation_info(
    request: CartValidationRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get cart validation info."""
    result = await client.get_cart_validation_info(
        sid=request.debug_sid,
        store_code=request.store_code,
        channel_code=request.channel_code,
        order_type=request.order_type,
        cart_type=request.cart_type,
        day_part_code=request.day_part_code,
        be_code=request.be_code,
        address_id=request.address_id
    )
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Order APIs ===================

@app.post(
    "/order/orders",
    response_model=SuccessResponse,
    tags=["Orders"],
    summary="Create Order",
    description="Create a new order from the shopping cart"
)
async def create_order(
    request: CreateOrderRequest,
    client: MCDClientV2 = Depends(get_mcd_client),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """Create order."""
    # First get cart validation info
    validation_info = await client.get_cart_validation_info(
        sid=request.debug_sid,
        store_code=request.store_code,
        channel_code=request.channel_code,
        order_type=request.order_type,
        cart_type=request.cart_type,
        day_part_code=request.day_part_code,
        be_code=request.be_code,
        address_id=request.address_id
    )

    if not validation_info.get('success'):
        raise MCDBusinessError("Cart validation failed")

    # Create order
    result = await client.make_order(
        sid=request.debug_sid,
        validation_info=validation_info['data'],
        store_code=request.store_code,
        order_type=request.order_type,
        day_part_code=request.day_part_code,
        be_type=request.be_type,
        be_code=request.be_code,
        eat_type=request.eat_type,
        cash_coupon_code=request.cash_coupon_code,
        cash_coupon_id=request.cash_coupon_id,
        address_id=request.address_id
    )

    # Save order info to Redis for payment processing (if not balance pay)
    if result.get('success') and not request.pay_by_balance:
        order_data = {
            'sid': request.debug_sid,
            'orderId': result['data']['orderId'],
            'payId': result['data']['payId'],
            'timestamp': int(time.time() * 1000)
        }

        redis_key = f"orders_v3_{request.proxy_id}"
        await redis_client.rpush(redis_key, json.dumps(order_data))
        logger.info(f"Order saved to Redis: {redis_key}")

    return SuccessResponse(message="Success", data=result.get('data'))


@app.get(
    "/order/orders/{order_id}",
    response_model=SuccessResponse,
    tags=["Orders"],
    summary="Get Order Detail",
    description="Get detailed information about a specific order"
)
async def get_order_detail(
    order_id: str,
    request: UserInfoRequest = Depends(),
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Get order detail."""
    result = await client.get_order_detail(request.debug_sid, order_id)
    return SuccessResponse(message="Success", data=result.get('data'))


@app.put(
    "/order/orders/{order_id}/cancellation",
    response_model=SuccessResponse,
    tags=["Orders"],
    summary="Cancel Order",
    description="Cancel an existing order"
)
async def cancel_order(
    order_id: str,
    request: CancelOrderRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Cancel order."""
    result = await client.cancel_order(request.debug_sid, order_id, request.cancel_reason_code)
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Payment APIs ===================

@app.post(
    "/cashier/preorder",
    response_model=SuccessResponse,
    tags=["Payment"],
    summary="Prepare Order Payment",
    description="Prepare order for payment processing"
)
async def prepare_order(
    request: PrepareOrderRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Prepare order for payment."""
    result = await client.prepare_order(request.debug_sid, request.pay_id, request.pay_channel)

    # Process payment data for third-party payment
    if (request.pay_channel in ['ALI', 'WX'] and
        result.get('success') and
        result.get('data', {}).get('responseCode') == 'SUCCESS'):

        channel_pay_data = result['data'].get('channelPayData')
        if channel_pay_data:
            try:
                pay_data = json.loads(channel_pay_data)
                query_pairs = []
                for k, v in pay_data.items():
                    encoded_v = quote_plus(str(v))
                    query_pairs.append(f'{k}={encoded_v}')

                result['data']['orderInfo'] = '&'.join(query_pairs)
                logger.debug(f"Generated order info: {result['data']['orderInfo']}")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse channel pay data: {e}")

    return SuccessResponse(message="Success", data=result.get('data'))


@app.post(
    "/cashier/archcard",
    response_model=SuccessResponse,
    tags=["Payment"],
    summary="Pay by Balance",
    description="Pay for order using account balance"
)
async def pay_by_balance(
    request: BalancePayRequest,
    client: MCDClientV2 = Depends(get_mcd_client)
):
    """Pay by account balance."""
    result = await client.pay_by_balance(request.debug_sid, request.pay_id)
    return SuccessResponse(message="Success", data=result.get('data'))


# =================== Development APIs ===================

@app.post(
    "/dev/crypto/test",
    response_model=SuccessResponse,
    tags=["Development"],
    summary="Test Crypto Utilities",
    description="Test cryptography utilities (development only)",
    include_in_schema=settings.server.debug
)
async def test_crypto(request: CryptoTestRequest):
    """Test cryptography utilities (development only)"""
    if not settings.server.debug:
        raise HTTPException(status_code=404, detail="Endpoint not available in production")

    crypto = CryptoUtils()

    # Test encryption/decryption
    encrypted = crypto.aes_ecb_encrypt(request.text)
    decrypted = crypto.aes_ecb_decrypt(encrypted)

    # Test hashing
    md5_hash = crypto.make_md5(request.text)
    sha1_hash = crypto.make_sha1(request.text)

    # Test device ID generation
    device_id = crypto.generate_device_id()
    imei = crypto.generate_random_imei()
    trace_id = crypto.generate_trace_id()

    response_data = CryptoTestResponse(
        input=request.text,
        encryption={
            'encrypted': encrypted,
            'decrypted': decrypted,
            'match': request.text == decrypted
        },
        hashing={
            'md5': md5_hash,
            'sha1': sha1_hash
        },
        generation={
            'device_id': device_id,
            'imei': imei,
            'trace_id': trace_id
        }
    )

    return SuccessResponse(data=response_data.model_dump())


if __name__ == "__main__":
    import uvicorn

    logger.info("Starting McDonald's API Server V3 with FastAPI")
    logger.info(f"Server config: {settings.server.host}:{settings.server.port}")
    logger.info(f"Debug mode: {settings.server.debug}")
    logger.info(f"Log level: {settings.server.log_level}")

    uvicorn.run(
        "main:app",
        host=settings.server.host,
        port=settings.server.port,
        reload=settings.server.reload,
        log_level=settings.server.log_level.lower(),
        access_log=True
    )
