"""
环境变量配置模块
统一管理所有环境变量的读取和验证
"""
import os
from typing import Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
import logging

logger = logging.getLogger(__name__)

# 数据库配置已移除 - mcd-api-server 不再需要数据库连接

class RedisConfig(BaseSettings):
    """Redis配置"""
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    host: str = Field(default="127.0.0.1", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    db: int = Field(default=0, env="REDIS_DB")
    
    @field_validator('password', mode='before')
    @classmethod
    def warn_if_no_password(cls, v):
        if not v:
            logger.warning("Redis密码未设置，建议在生产环境中设置密码")
        return v

class JWTConfig(BaseSettings):
    """JWT配置"""
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    secret: str = Field(env="JWT_SECRET")
    expires_in: str = Field(default="24h", env="JWT_EXPIRES_IN")
    algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    
    @field_validator('secret')
    @classmethod
    def secret_must_be_strong(cls, v):
        if not v:
            raise ValueError('JWT密钥不能为空')
        if len(v) < 32:
            raise ValueError('JWT密钥长度至少32个字符')
        return v

class ServerConfig(BaseSettings):
    """服务器配置"""
    host: str = Field(default="0.0.0.0", env="FASTAPI_HOST")
    port: int = Field(default=9527, env="FASTAPI_PORT")
    debug: bool = Field(default=False, env="FASTAPI_DEBUG")
    log_level: str = Field(default="INFO", env="FASTAPI_LOG_LEVEL")
    
    # CORS配置
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    cors_methods: List[str] = Field(default=["GET", "POST", "PUT", "DELETE"], env="CORS_METHODS")
    cors_headers: List[str] = Field(default=["*"], env="CORS_HEADERS")
    
    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v

    @field_validator('cors_methods', mode='before')
    @classmethod
    def parse_cors_methods(cls, v):
        if isinstance(v, str):
            return [method.strip() for method in v.split(',')]
        return v

class MCDApiConfig(BaseSettings):
    """麦当劳API配置"""
    base_url: str = Field(default="https://api.mcd.cn", env="MCD_API_BASE_URL")
    app_version: str = Field(default="********", env="MCD_API_APP_VERSION")
    device_id: str = Field(env="MCD_API_DEVICE_ID")
    tid: str = Field(default="00003TuN", env="MCD_API_TID")
    timeout: int = Field(default=30, env="MCD_API_TIMEOUT")
    max_retries: int = Field(default=3, env="MCD_API_MAX_RETRIES")
    
    @field_validator('device_id')
    @classmethod
    def device_id_must_be_set(cls, v):
        if not v:
            raise ValueError('麦当劳API设备ID不能为空')
        return v

class PaymentConfig(BaseSettings):
    """支付配置"""
    # 支付宝配置
    alipay_app_id: Optional[str] = Field(default=None, env="ALIPAY_APP_ID")
    alipay_private_key: Optional[str] = Field(default=None, env="ALIPAY_PRIVATE_KEY")
    alipay_public_key: Optional[str] = Field(default=None, env="ALIPAY_PUBLIC_KEY")
    alipay_notify_url: Optional[str] = Field(default=None, env="ALIPAY_NOTIFY_URL")
    
    # 微信支付配置
    wechat_app_id: Optional[str] = Field(default=None, env="WECHAT_APP_ID")
    wechat_mch_id: Optional[str] = Field(default=None, env="WECHAT_MCH_ID")
    wechat_api_key: Optional[str] = Field(default=None, env="WECHAT_API_KEY")
    wechat_notify_url: Optional[str] = Field(default=None, env="WECHAT_NOTIFY_URL")

class SMSConfig(BaseSettings):
    """短信服务配置"""
    api_url: Optional[str] = Field(default=None, env="SMS_API_URL")
    api_key: Optional[str] = Field(default=None, env="SMS_API_KEY")
    template_id: Optional[str] = Field(default=None, env="SMS_TEMPLATE_ID")
    sign: Optional[str] = Field(default=None, env="SMS_SIGN")

class SecurityConfig(BaseSettings):
    """安全配置"""
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, env="RATE_LIMIT_WINDOW")
    session_secret: str = Field(env="SESSION_SECRET")
    session_timeout: int = Field(default=3600, env="SESSION_TIMEOUT")
    encryption_key: str = Field(env="ENCRYPTION_KEY")
    
    # SSL/TLS配置
    ssl_cert_path: Optional[str] = Field(default=None, env="SSL_CERT_PATH")
    ssl_key_path: Optional[str] = Field(default=None, env="SSL_KEY_PATH")
    
    # 健康检查配置
    health_check_endpoint: str = Field(default="/health", env="HEALTH_CHECK_ENDPOINT")
    health_check_token: Optional[str] = Field(default=None, env="HEALTH_CHECK_TOKEN")
    
    # 密钥轮换配置
    key_rotation_interval: int = Field(default=30, env="KEY_ROTATION_INTERVAL")  # 天
    enable_key_rotation: bool = Field(default=False, env="ENABLE_KEY_ROTATION")
    
    @field_validator('session_secret')
    @classmethod
    def session_secret_must_be_set(cls, v):
        if not v:
            raise ValueError('会话密钥不能为空')
        if len(v) < 32:
            raise ValueError('会话密钥长度至少32个字符')
        return v

    @field_validator('encryption_key')
    @classmethod
    def encryption_key_must_be_valid(cls, v):
        if not v:
            raise ValueError('加密密钥不能为空')
        if len(v) != 32:
            raise ValueError('加密密钥必须是32个字符')
        return v

    @field_validator('health_check_token')
    @classmethod
    def health_check_token_strength(cls, v):
        if v and len(v) < 16:
            raise ValueError('健康检查令牌长度至少16个字符')
        return v

class LoggingConfig(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    file_path: str = Field(default="./logs/app.log", env="LOG_FILE_PATH")
    max_size: str = Field(default="100MB", env="LOG_MAX_SIZE")
    backup_count: int = Field(default=10, env="LOG_BACKUP_COUNT")

class MonitoringConfig(BaseSettings):
    """监控配置"""
    enable_performance: bool = Field(default=True, env="ENABLE_PERFORMANCE_MONITORING")
    metrics_port: int = Field(default=8080, env="METRICS_PORT")

class StorageConfig(BaseSettings):
    """存储配置"""
    storage_type: str = Field(default="local", env="STORAGE_TYPE")  # local, cos, s3
    storage_path: str = Field(default="./uploads", env="STORAGE_PATH")
    
    # 腾讯云COS配置
    cos_secret_id: Optional[str] = Field(default=None, env="COS_SECRET_ID")
    cos_secret_key: Optional[str] = Field(default=None, env="COS_SECRET_KEY")
    cos_region: str = Field(default="ap-guangzhou", env="COS_REGION")
    cos_bucket: Optional[str] = Field(default=None, env="COS_BUCKET")
    
    @field_validator('storage_type')
    @classmethod
    def validate_storage_type(cls, v):
        if v not in ['local', 'cos', 's3']:
            raise ValueError('存储类型必须是 local, cos, s3 之一')
        return v

class EmailConfig(BaseSettings):
    """邮件配置"""
    smtp_host: str = Field(default="smtp.gmail.com", env="SMTP_HOST")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_user: Optional[str] = Field(default=None, env="SMTP_USER")
    smtp_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    
    @field_validator('smtp_user')
    @classmethod
    def smtp_user_format(cls, v):
        if v and '@' not in v:
            raise ValueError('SMTP用户名应该是有效的邮箱地址')
        return v

class BackupConfig(BaseSettings):
    """备份配置"""
    backup_schedule: str = Field(default="0 2 * * *", env="BACKUP_SCHEDULE")
    backup_retention_days: int = Field(default=30, env="BACKUP_RETENTION_DAYS")
    backup_s3_bucket: Optional[str] = Field(default=None, env="BACKUP_S3_BUCKET")
    backup_s3_region: str = Field(default="ap-guangzhou", env="BACKUP_S3_REGION")

class TestConfig(BaseSettings):
    """测试配置"""
    use_in_memory_db: bool = Field(default=False, env="USE_IN_MEMORY_DB")
    test_db_reset_on_start: bool = Field(default=False, env="TEST_DB_RESET_ON_START")
    enable_mock_services: bool = Field(default=False, env="ENABLE_MOCK_SERVICES")
    mock_mcd_api: bool = Field(default=False, env="MOCK_MCD_API")
    mock_payment_services: bool = Field(default=False, env="MOCK_PAYMENT_SERVICES")
    mock_sms_service: bool = Field(default=False, env="MOCK_SMS_SERVICE")
    coverage_report: bool = Field(default=False, env="COVERAGE_REPORT")
    coverage_threshold: int = Field(default=80, env="COVERAGE_THRESHOLD")
    test_concurrency: int = Field(default=4, env="TEST_CONCURRENCY")
    test_timeout: int = Field(default=30, env="TEST_TIMEOUT")

class EnvironmentConfig(BaseSettings):
    """环境配置"""
    node_env: str = Field(default="production", env="NODE_ENV")
    python_env: str = Field(default="production", env="PYTHON_ENV")
    debug: bool = Field(default=False, env="DEBUG")
    timezone: str = Field(default="Asia/Shanghai", env="TZ")
    
    # 开发环境特有配置
    auto_reload: bool = Field(default=False, env="AUTO_RELOAD")
    show_docs: bool = Field(default=False, env="SHOW_DOCS")
    enable_mock_data: bool = Field(default=False, env="ENABLE_MOCK_DATA")
    enable_sql_logging: bool = Field(default=False, env="ENABLE_SQL_LOGGING")
    
    # 容器化配置
    container_name: Optional[str] = Field(default=None, env="CONTAINER_NAME")
    container_port: int = Field(default=9527, env="CONTAINER_PORT")
    container_memory_limit: Optional[str] = Field(default=None, env="CONTAINER_MEMORY_LIMIT")
    container_cpu_limit: Optional[str] = Field(default=None, env="CONTAINER_CPU_LIMIT")

def validate_environment():
    """验证所有必要的环境变量"""
    try:
        # 验证核心配置
        db_config = DatabaseConfig()
        redis_config = RedisConfig()
        jwt_config = JWTConfig()
        server_config = ServerConfig()
        mcd_api_config = MCDApiConfig()
        security_config = SecurityConfig()
        
        # 验证可选配置
        payment_config = PaymentConfig()
        sms_config = SMSConfig()
        logging_config = LoggingConfig()
        monitoring_config = MonitoringConfig()
        storage_config = StorageConfig()
        email_config = EmailConfig()
        backup_config = BackupConfig()
        test_config = TestConfig()
        environment_config = EnvironmentConfig()
        
        logger.info("✅ 环境变量验证通过")
        logger.info(f"当前环境: {environment_config.python_env}")
        logger.info(f"调试模式: {'开启' if environment_config.debug else '关闭'}")
        logger.info(f"存储类型: {storage_config.storage_type}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境变量验证失败: {e}")
        return False

def get_all_configs():
    """获取所有配置实例"""
    return {
        'database': DatabaseConfig(),
        'redis': RedisConfig(),
        'jwt': JWTConfig(),
        'server': ServerConfig(),
        'mcd_api': MCDApiConfig(),
        'payment': PaymentConfig(),
        'sms': SMSConfig(),
        'security': SecurityConfig(),
        'logging': LoggingConfig(),
        'monitoring': MonitoringConfig(),
        'storage': StorageConfig(),
        'email': EmailConfig(),
        'backup': BackupConfig(),
        'test': TestConfig(),
        'environment': EnvironmentConfig(),
    }

if __name__ == "__main__":
    # 验证环境变量
    if validate_environment():
        print("✅ 所有环境变量配置正确")
    else:
        print("❌ 环境变量配置有误，请检查 .env 文件")
        exit(1)
