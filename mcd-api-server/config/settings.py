"""
Configuration settings for MCD API Server using Pydantic Settings
"""
from typing import Dict, List
from pydantic import Field
from pydantic_settings import BaseSettings


# 数据库配置已移除 - mcd-api-server 不再需要数据库连接


class RedisConfig(BaseSettings):
    """Redis configuration"""
    host: str = Field(default="127.0.0.1", description="Redis host")
    port: int = Field(default=6379, description="Redis port")
    db: int = Field(default=1, description="Redis database")
    password: str = Field(default="", description="Redis password")
    
    @property
    def connection_url(self) -> str:
        """Generate Redis connection URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"
    
    @property
    def url(self) -> str:
        """Alias for connection_url"""
        return self.connection_url
    
    class Config:
        env_prefix = "REDIS_"


class MCDApiConfig(BaseSettings):
    """McDonald's API configuration"""
    base_url: str = Field(default="https://api.mcd.cn", description="MCD API base URL")
    app_version: str = Field(default="********", description="App version")
    device_id: str = Field(
        default="BQuLoOFbMfVK30is464W4mep0iXmkEqlh0b7zcnqGLuxpellGcpLVVvEHvDnUxWEQst3PoCbGho3B3FxlLfk2tg==",
        description="Device ID"
    )
    tid: str = Field(default="00003TuN", description="TID")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, description="Retry delay in seconds")
    
    @property
    def sign_keys(self) -> Dict[int, str]:
        """API签名密钥配置"""
        return {
            10: '71d414c0-8fe1-495d-ac55-207414632479',
            11: '71d414c0-8fe1-495d-ac55-207414632479', 
            20: '59b891d7-8d5a-4993-9b16-37d905a16967',
            21: 'ad5bac4a-2f99-4728-a7e5-818b5770b559',
            30: '3c0860ff-fabe-436a-ba47-94d42669591b',
            31: '71d414c0-8fe1-495d-ac55-207414632479',
            32: '71d414c0-8fe1-495d-ac55-207414632479',
            33: '71d414c0-8fe1-495d-ac55-207414632479',
            34: '71d414c0-8fe1-495d-ac55-207414632479',
            36: '71d414c0-8fe1-495d-ac55-207414632479',
            101: '97741bd7-21bb-4f0d-8b4a-501a089cd208',
            102: '97741bd7-21bb-4f0d-8b4a-501a089cd208',
            302: '71d414c0-8fe1-495d-ac55-207414632479'
        }
    
    class Config:
        env_prefix = "MCD_API_"


class ServerConfig(BaseSettings):
    """Server configuration"""
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=9527, description="Server port")
    debug: bool = Field(default=False, description="Debug mode")
    workers: int = Field(default=1, description="Number of workers")
    log_level: str = Field(default="INFO", description="Log level")
    cors_origins: List[str] = Field(default=["*"], description="CORS origins")
    allowed_hosts: List[str] = Field(default=["*"], description="Allowed hosts for TrustedHostMiddleware")
    reload: bool = Field(default=False, description="Auto reload in development")
    
    class Config:
        env_prefix = "SERVER_"


class Settings(BaseSettings):
    """Application settings manager"""
    
    # API metadata
    title: str = Field(default="McDonald's API Server V3", description="API title")
    description: str = Field(
        default="Enhanced McDonald's API proxy server with FastAPI",
        description="API description"
    )
    version: str = Field(default="3.0.0", description="API version")
    
    # Sub-configurations
    redis: RedisConfig = Field(default_factory=RedisConfig)
    mcd_api: MCDApiConfig = Field(default_factory=MCDApiConfig)
    server: ServerConfig = Field(default_factory=ServerConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"


# Global settings instance
settings = Settings()