"""
Pydantic models for request/response validation
"""
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum
import re
from typing import Literal
import html
import bleach
from decimal import Decimal


class ResponseStatus(str, Enum):
    """Response status enum"""
    SUCCESS = "success"
    ERROR = "error"


class OrderType(int, Enum):
    """Order type enum"""
    DINE_IN = 1
    DELIVERY = 2


class PayChannel(str, Enum):
    """Payment channel enum"""
    ALIPAY = "ALI"
    WECHAT = "WX"
    BALANCE = "ARCHCARD"


# =================== Base Response Models ===================

class BaseResponse(BaseModel):
    """Base response model"""
    success: bool = True
    code: int = 200
    message: str = "Success"
    timestamp: int = Field(default_factory=lambda: int(datetime.now().timestamp() * 1000))


class SuccessResponse(BaseResponse):
    """Success response model"""
    data: Optional[Any] = None


class ErrorResponse(BaseResponse):
    """Error response model"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


# =================== Store & Location Models ===================

class CoordinateRequest(BaseModel):
    """Coordinate request model with enhanced validation"""
    lat: float = Field(..., ge=-90, le=90, description="Latitude")
    lng: float = Field(..., ge=-180, le=180, description="Longitude")
    
    @validator('lat')
    def validate_latitude(cls, v):
        """Enhanced latitude validation"""
        if v is None:
            raise ValueError('Latitude is required')
            
        # Check range
        if v < -90 or v > 90:
            raise ValueError('Latitude must be between -90 and 90')
            
        # Check precision (max 6 decimal places)
        if abs(v) > 0 and len(str(v).split('.')[-1]) > 6:
            raise ValueError('Latitude precision too high (max 6 decimal places)')
            
        return round(v, 6)
    
    @validator('lng')
    def validate_longitude(cls, v):
        """Enhanced longitude validation"""
        if v is None:
            raise ValueError('Longitude is required')
            
        # Check range
        if v < -180 or v > 180:
            raise ValueError('Longitude must be between -180 and 180')
            
        # Check precision (max 6 decimal places)
        if abs(v) > 0 and len(str(v).split('.')[-1]) > 6:
            raise ValueError('Longitude precision too high (max 6 decimal places)')
            
        return round(v, 6)
    
    @model_validator(mode='after')
    def validate_coordinates(self):
        """Validate coordinate combination"""
        lat = self.lat
        lng = self.lng
        
        if lat is not None and lng is not None:
            # Check if coordinates are not at null island (0,0)
            if lat == 0 and lng == 0:
                raise ValueError('Invalid coordinates: null island')
                
            # Check if coordinates are within reasonable bounds for Earth
            if abs(lat) < 0.000001 and abs(lng) < 0.000001:
                raise ValueError('Coordinates too close to null island')
                
        return self


class StoreSearchRequest(BaseModel):
    """Store search request model with enhanced validation"""
    city_code: str = Field(..., min_length=1, max_length=50, description="City code")
    keyword: Optional[str] = Field("", max_length=100, description="Search keyword")
    page_no: int = Field(1, ge=1, le=1000, description="Page number")
    page_size: int = Field(10, ge=1, le=100, description="Page size")
    
    @validator('city_code')
    def validate_city_code(cls, v):
        """Enhanced city code validation"""
        if not v:
            raise ValueError('City code is required')
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) < 1 or len(v) > 50:
            raise ValueError('City code must be 1-50 characters')
            
        # Check for alphanumeric and underscore only
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('City code must contain only letters, numbers, underscore, and dash')
            
        # Security check: prevent injection attempts
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('City code contains forbidden patterns')
            
        return v
    
    @validator('keyword')
    def validate_keyword(cls, v):
        """Enhanced keyword validation"""
        if v is None:
            return ""
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) > 100:
            raise ValueError('Keyword must be less than 100 characters')
            
        # Security check: prevent XSS attempts
        if any(char in v for char in ['<', '>', '"', "'", '&', ';', '\\']):
            raise ValueError('Keyword contains invalid characters')
            
        # Check for SQL injection patterns
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('Keyword contains forbidden patterns')
            
        # HTML escape the keyword
        if v:
            v = html.escape(v)
            v = bleach.clean(v, tags=[], attributes={}, strip=True)
            
        return v


class NearbyStoresRequest(CoordinateRequest):
    """Nearby stores request model"""
    show_type: int = Field(2, description="Show type")
    be_type: int = Field(1, description="Business entity type")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")


# =================== Menu & Product Models ===================

class MenuRequest(BaseModel):
    """Store menu request model"""
    store_code: str = Field(..., min_length=1, description="Store code")
    be_type: int = Field(1, description="Business entity type")
    be_code: str = Field("", description="Business entity code")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    day_part_code: Optional[int] = Field(None, description="Day part code (auto-detected if None)")


class ProductDetailRequest(BaseModel):
    """Product detail request model"""
    store_code: str = Field(..., min_length=1, description="Store code")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    be_code: str = Field("", description="Business entity code")
    day_part_code: Optional[int] = Field(None, description="Day part code")
    channel_code: str = Field("03", description="Channel code")


# =================== User Models ===================

class UserInfoRequest(BaseModel):
    """User info request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")


class SendSmsCodeRequest(BaseModel):
    """Send SMS verification code request model"""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Phone number")
    
    @validator('phone')
    def validate_phone_security(cls, v):
        """Enhanced phone number validation with security checks"""
        if not v:
            raise ValueError('Phone number is required')
        
        # Remove any non-digit characters
        clean_phone = re.sub(r'\D', '', v)
        
        # Check length and format
        if len(clean_phone) != 11:
            raise ValueError('Phone number must be 11 digits')
            
        # Check if starts with 1 and second digit is 3-9
        if not re.match(r'^1[3-9]\d{9}$', clean_phone):
            raise ValueError('Invalid phone number format')
            
        # Security check: prevent SQL injection attempts
        if any(char in v for char in ['\';', '--', '/*', '*/', 'union', 'select', 'drop', 'insert', 'update', 'delete']):
            raise ValueError('Invalid characters in phone number')
            
        return clean_phone


class RegisterBySmsRequest(BaseModel):
    """Register user by SMS verification code request model"""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Phone number")
    code: str = Field(..., min_length=4, max_length=6, description="Verification code")
    username: str = Field(..., min_length=2, max_length=20, description="Username")
    
    @validator('phone')
    def validate_phone_security(cls, v):
        """Enhanced phone number validation with security checks"""
        if not v:
            raise ValueError('Phone number is required')
        
        # Remove any non-digit characters
        clean_phone = re.sub(r'\D', '', v)
        
        # Check length and format
        if len(clean_phone) != 11:
            raise ValueError('Phone number must be 11 digits')
            
        # Check if starts with 1 and second digit is 3-9
        if not re.match(r'^1[3-9]\d{9}$', clean_phone):
            raise ValueError('Invalid phone number format')
            
        # Security check: prevent SQL injection attempts
        if any(char in v for char in ['\';', '--', '/*', '*/', 'union', 'select', 'drop', 'insert', 'update', 'delete']):
            raise ValueError('Invalid characters in phone number')
            
        return clean_phone
    
    @validator('code')
    def validate_sms_code(cls, v):
        """Validate SMS code format and security"""
        if not v:
            raise ValueError('SMS code is required')
            
        # Remove any non-digit characters
        clean_code = re.sub(r'\D', '', v)
        
        # Check length
        if len(clean_code) < 4 or len(clean_code) > 6:
            raise ValueError('SMS code must be 4-6 digits')
            
        # Check if all digits
        if not clean_code.isdigit():
            raise ValueError('SMS code must contain only digits')
            
        # Security check: prevent injection attempts
        if any(char in v for char in ['<', '>', '"', "'", '&', ';', '\\', '/', 'script', 'javascript']):
            raise ValueError('Invalid characters in SMS code')
            
        return clean_code
    
    @validator('username')
    def validate_username_security(cls, v):
        """Enhanced username validation with security checks"""
        if not v:
            raise ValueError('Username is required')
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) < 2 or len(v) > 20:
            raise ValueError('Username must be 2-20 characters')
            
        # Security check: prevent XSS and injection attempts
        if any(char in v for char in ['<', '>', '"', "'", '&', ';', '\\', '/', 'script', 'javascript']):
            raise ValueError('Username contains invalid characters')
            
        # Check for SQL injection patterns
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('Username contains forbidden patterns')
            
        # HTML escape the username
        v = html.escape(v)
        
        # Additional sanitization with bleach
        v = bleach.clean(v, tags=[], attributes={}, strip=True)
        
        return v

class LoginBySmsRequest(BaseModel):
    """Login by SMS verification code request model"""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Phone number")
    code: str = Field(..., min_length=4, max_length=6, description="Verification code")
    
    @validator('phone')
    def validate_phone_security(cls, v):
        """Enhanced phone number validation with security checks"""
        if not v:
            raise ValueError('Phone number is required')
        
        # Remove any non-digit characters
        clean_phone = re.sub(r'\D', '', v)
        
        # Check length and format
        if len(clean_phone) != 11:
            raise ValueError('Phone number must be 11 digits')
            
        # Check if starts with 1 and second digit is 3-9
        if not re.match(r'^1[3-9]\d{9}$', clean_phone):
            raise ValueError('Invalid phone number format')
            
        # Security check: prevent SQL injection attempts
        if any(char in v for char in ['\';', '--', '/*', '*/', 'union', 'select', 'drop', 'insert', 'update', 'delete']):
            raise ValueError('Invalid characters in phone number')
            
        return clean_phone
    
    @validator('code')
    def validate_sms_code(cls, v):
        """Validate SMS code format and security"""
        if not v:
            raise ValueError('SMS code is required')
            
        # Remove any non-digit characters
        clean_code = re.sub(r'\D', '', v)
        
        # Check length
        if len(clean_code) < 4 or len(clean_code) > 6:
            raise ValueError('SMS code must be 4-6 digits')
            
        # Check if all digits
        if not clean_code.isdigit():
            raise ValueError('SMS code must contain only digits')
            
        # Security check: prevent injection attempts
        if any(char in v for char in ['<', '>', '"', "'", '&', ';', '\\', '/', 'script', 'javascript']):
            raise ValueError('Invalid characters in SMS code')
            
        return clean_code


# =================== Coupon Models ===================

class CouponDetailRequest(BaseModel):
    """Coupon detail request model"""
    coupon_id: str = Field(..., min_length=1, description="Coupon ID")
    debug_sid: str = Field(..., min_length=1, description="Session ID")


class CouponProductsRequest(BaseModel):
    """Coupon products request model"""
    coupon_id: str = Field(..., min_length=1, description="Coupon ID")
    coupon_code: str = Field(..., min_length=1, description="Coupon code")
    promotion_id: str = Field(..., min_length=1, description="Promotion ID")
    store_code: str = Field(..., min_length=1, description="Store code")
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    be_code: str = Field("", description="Business entity code")
    daypart_codes: Optional[int] = Field(None, description="Day part codes")
    date: Optional[str] = Field(None, description="Date")
    time: Optional[str] = Field(None, description="Time")


# =================== Cart Models ===================

class CustomizationItem(BaseModel):
    """Customization item model"""
    option_id: str
    choice_id: str
    quantity: int = 1


class ComboItem(BaseModel):
    """Combo item model"""
    product_code: str
    quantity: int = 1
    customizations: Optional[List[CustomizationItem]] = []


class AddToCartRequest(BaseModel):
    """Add to cart request model with enhanced validation"""
    debug_sid: str = Field(..., min_length=1, max_length=100, description="Session ID")
    store_code: str = Field(..., min_length=1, max_length=50, description="Store code")
    product_code: str = Field("", max_length=100, description="Product code")
    cart_type: int = Field(1, ge=1, le=10, description="Cart type")
    channel_code: str = Field("03", max_length=10, description="Channel code")
    be_type: int = Field(1, ge=1, le=10, description="Business entity type")
    be_code: str = Field("", max_length=50, description="Business entity code")
    day_part_code: Optional[int] = Field(None, ge=1, le=24, description="Day part code")
    coupon_code: str = Field("", max_length=100, description="Coupon code")
    coupon_id: str = Field("", max_length=100, description="Coupon ID")
    promotion_id: str = Field("", max_length=100, description="Promotion ID")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    quantity: int = Field(1, ge=1, le=99, description="Quantity")
    customization: Optional[List[CustomizationItem]] = Field([], max_items=20, description="Customization options")
    combo_items: Optional[List[ComboItem]] = Field([], max_items=20, description="Combo items")
    sub_product_code: str = Field("", max_length=100, description="Sub product code")
    membership_code: str = Field("", max_length=100, description="Membership code")
    product_type: int = Field(1, ge=1, le=10, description="Product type")
    card_type: int = Field(0, ge=0, le=10, description="Card type")
    card_id: str = Field("", max_length=100, description="Card ID")
    
    @validator('debug_sid')
    def validate_session_id(cls, v):
        """Enhanced session ID validation"""
        if not v:
            raise ValueError('Session ID is required')
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) < 1 or len(v) > 100:
            raise ValueError('Session ID must be 1-100 characters')
            
        # Check for alphanumeric and allowed special characters
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Session ID must contain only letters, numbers, underscore, and dash')
            
        # Security check: prevent injection attempts
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('Session ID contains forbidden patterns')
            
        return v
    
    @validator('store_code')
    def validate_store_code(cls, v):
        """Enhanced store code validation"""
        if not v:
            raise ValueError('Store code is required')
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) < 1 or len(v) > 50:
            raise ValueError('Store code must be 1-50 characters')
            
        # Check for alphanumeric and allowed special characters
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Store code must contain only letters, numbers, underscore, and dash')
            
        # Security check: prevent injection attempts
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('Store code contains forbidden patterns')
            
        return v
    
    @validator('product_code')
    def validate_product_code(cls, v):
        """Enhanced product code validation"""
        if not v:
            return ""
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) > 100:
            raise ValueError('Product code must be less than 100 characters')
            
        # Check for alphanumeric and allowed special characters
        if not re.match(r'^[a-zA-Z0-9_-]*$', v):
            raise ValueError('Product code must contain only letters, numbers, underscore, and dash')
            
        # Security check: prevent injection attempts
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('Product code contains forbidden patterns')
            
        return v
    
    @validator('quantity')
    def validate_quantity(cls, v):
        """Enhanced quantity validation"""
        if v is None:
            raise ValueError('Quantity is required')
            
        # Check range
        if v < 1 or v > 99:
            raise ValueError('Quantity must be between 1 and 99')
            
        return v
    
    @validator('coupon_code')
    def validate_coupon_code(cls, v):
        """Enhanced coupon code validation"""
        if not v:
            return ""
            
        # Trim whitespace
        v = v.strip()
        
        # Check length
        if len(v) > 100:
            raise ValueError('Coupon code must be less than 100 characters')
            
        # Check for alphanumeric and allowed special characters
        if not re.match(r'^[a-zA-Z0-9_-]*$', v):
            raise ValueError('Coupon code must contain only letters, numbers, underscore, and dash')
            
        # Security check: prevent injection attempts
        if any(pattern in v.lower() for pattern in ['union', 'select', 'drop', 'insert', 'update', 'delete', '--', '/*', '*/']):
            raise ValueError('Coupon code contains forbidden patterns')
            
        return v


class EmptyCartRequest(BaseModel):
    """Empty cart request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    store_code: str = Field(..., min_length=1, description="Store code")
    cart_type: int = Field(1, description="Cart type")
    channel_code: str = Field("03", description="Channel code")
    be_type: int = Field(1, description="Business entity type")
    be_code: str = Field("", description="Business entity code")
    day_part_code: Optional[int] = Field(None, description="Day part code")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")


class CartValidationRequest(BaseModel):
    """Cart validation request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    store_code: str = Field(..., min_length=1, description="Store code")
    channel_code: str = Field("03", description="Channel code")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    cart_type: int = Field(1, description="Cart type")
    day_part_code: Optional[int] = Field(None, description="Day part code")
    be_code: str = Field("", description="Business entity code")
    address_id: Optional[str] = Field(None, description="Address ID (required for delivery)")

    @validator('address_id')
    def validate_address_for_delivery(cls, v, values):
        """Validate address_id is required for delivery orders"""
        if values.get('order_type') == OrderType.DELIVERY and not v:
            raise ValueError('Address ID is required for delivery orders')
        return v


# =================== Order Models ===================

class CreateOrderRequest(BaseModel):
    """Create order request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    store_code: str = Field(..., min_length=1, description="Store code")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    cart_type: int = Field(1, description="Cart type")
    channel_code: str = Field("03", description="Channel code")
    be_type: int = Field(1, description="Business entity type")
    be_code: str = Field("", description="Business entity code")
    day_part_code: Optional[int] = Field(None, description="Day part code")
    eat_type: str = Field("take-in-store", description="Eating type")
    cash_coupon_code: str = Field("", description="Cash coupon code")
    cash_coupon_id: str = Field("", description="Cash coupon ID")
    address_id: Optional[str] = Field(None, description="Address ID (required for delivery)")
    pay_by_balance: bool = Field(False, description="Pay by balance")
    proxy_id: str = Field("default", description="Proxy ID")

    @validator('address_id')
    def validate_address_for_delivery(cls, v, values):
        """Validate address_id is required for delivery orders"""
        if values.get('order_type') == OrderType.DELIVERY and not v:
            raise ValueError('Address ID is required for delivery orders')
        return v


class CancelOrderRequest(BaseModel):
    """Cancel order request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    cancel_reason_code: str = Field("1", description="Cancel reason code")


# =================== Payment Models ===================

class PrepareOrderRequest(BaseModel):
    """Prepare order request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    pay_id: str = Field(..., min_length=1, description="Payment ID")
    pay_channel: PayChannel = Field(PayChannel.ALIPAY, description="Payment channel")


class BalancePayRequest(BaseModel):
    """Balance pay request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")
    pay_id: str = Field(..., min_length=1, description="Payment ID")


# =================== Health Check Models ===================

class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service status")
    timestamp: int = Field(..., description="Timestamp")
    version: str = Field(..., description="API version")
    services: Dict[str, str] = Field(..., description="Service statuses")


class ServiceInfo(BaseModel):
    """Service info model"""
    name: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    description: str = Field(..., description="Service description")


# =================== Development Models ===================

class CryptoTestRequest(BaseModel):
    """Crypto test request model"""
    text: str = Field("test", description="Text to test with")


class CryptoTestResponse(BaseModel):
    """Crypto test response model"""
    input: str
    encryption: Dict[str, Any]
    hashing: Dict[str, str]
    generation: Dict[str, str]