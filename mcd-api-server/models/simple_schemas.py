"""
简化的数据模型
用于快速启动服务器
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class OrderType(str, Enum):
    """Order type enumeration"""
    PICKUP = "pickup"
    DELIVERY = "delivery"


class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service status")
    message: str = Field(..., description="Status message")
    timestamp: str = Field(..., description="Check timestamp")
    version: str = Field(..., description="Service version")
    environment: str = Field(..., description="Environment")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    code: int = Field(..., description="Error code")
    timestamp: str = Field(..., description="Error timestamp")


class SuccessResponse(BaseModel):
    """Success response model"""
    success: bool = Field(True, description="Success status")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")


class ServiceInfo(BaseModel):
    """Service information model"""
    name: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    description: str = Field(..., description="Service description")
    endpoints: List[str] = Field(..., description="Available endpoints")


class CoordinateRequest(BaseModel):
    """Coordinate request model"""
    lat: float = Field(..., ge=-90, le=90, description="Latitude")
    lng: float = Field(..., ge=-180, le=180, description="Longitude")


class StoreSearchRequest(BaseModel):
    """Store search request model"""
    city_code: str = Field(..., min_length=1, max_length=50, description="City code")
    keyword: Optional[str] = Field("", max_length=100, description="Search keyword")
    page_no: int = Field(1, ge=1, le=1000, description="Page number")
    page_size: int = Field(10, ge=1, le=100, description="Page size")


class NearbyStoresRequest(BaseModel):
    """Nearby stores request model"""
    lat: float = Field(..., ge=-90, le=90, description="Latitude")
    lng: float = Field(..., ge=-180, le=180, description="Longitude")
    radius: int = Field(5000, ge=100, le=50000, description="Search radius in meters")
    limit: int = Field(20, ge=1, le=100, description="Maximum number of stores")


class MenuRequest(BaseModel):
    """Menu request model"""
    store_code: str = Field(..., min_length=1, max_length=50, description="Store code")
    order_type: OrderType = Field(OrderType.PICKUP, description="Order type")
    day_part_code: int = Field(5, description="Day part code")


class ProductDetailRequest(BaseModel):
    """Product detail request model"""
    store_code: str = Field(..., min_length=1, max_length=50, description="Store code")
    product_code: str = Field(..., min_length=1, max_length=50, description="Product code")
    channel_code: str = Field("03", description="Channel code")
    order_type: OrderType = Field(OrderType.PICKUP, description="Order type")


class UserInfoRequest(BaseModel):
    """User info request model"""
    debug_sid: str = Field(..., min_length=1, description="Session ID")


class SendSmsCodeRequest(BaseModel):
    """Send SMS verification code request model"""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Phone number")


class RegisterBySmsRequest(BaseModel):
    """Register user by SMS verification code request model"""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Phone number")
    code: str = Field(..., min_length=4, max_length=6, description="Verification code")
    username: str = Field(..., min_length=2, max_length=20, description="Username")


class LoginBySmsRequest(BaseModel):
    """Login by SMS verification code request model"""
    phone: str = Field(..., pattern=r'^1[3-9]\d{9}$', description="Phone number")
    code: str = Field(..., min_length=4, max_length=6, description="Verification code")


class AddToCartRequest(BaseModel):
    """Add to cart request model"""
    debug_sid: str = Field(..., description="Session ID")
    store_code: str = Field(..., description="Store code")
    product_code: str = Field(..., description="Product code")
    quantity: int = Field(1, ge=1, le=99, description="Quantity")
    coupon_code: str = Field("", description="Coupon code")


class CartValidationRequest(BaseModel):
    """Cart validation request model"""
    debug_sid: str = Field(..., description="Session ID")
    store_code: str = Field(..., description="Store code")
    order_type: OrderType = Field(OrderType.PICKUP, description="Order type")


class EmptyCartRequest(BaseModel):
    """Empty cart request model"""
    debug_sid: str = Field(..., description="Session ID")
    store_code: str = Field(..., description="Store code")


class PrepareOrderRequest(BaseModel):
    """Prepare order request model"""
    debug_sid: str = Field(..., description="Session ID")
    store_code: str = Field(..., description="Store code")
    order_type: OrderType = Field(OrderType.PICKUP, description="Order type")
    cart_type: int = Field(1, description="Cart type")
    day_part_code: Optional[int] = Field(None, description="Day part code")
    be_code: str = Field("", description="Business entity code")
    address_id: Optional[str] = Field(None, description="Address ID (required for delivery)")


class CreateOrderRequest(BaseModel):
    """Create order request model"""
    debug_sid: str = Field(..., description="Session ID")
    store_code: str = Field(..., description="Store code")
    order_type: OrderType = Field(OrderType.PICKUP, description="Order type")
    cash_coupon_id: str = Field("", description="Cash coupon ID")
    address_id: Optional[str] = Field(None, description="Address ID (required for delivery)")
    pay_by_balance: bool = Field(False, description="Pay by balance")
    proxy_id: str = Field("default", description="Proxy ID")


class BalancePayRequest(BaseModel):
    """Balance payment request model"""
    debug_sid: str = Field(..., description="Session ID")
    order_id: str = Field(..., description="Order ID")
    payment_amount: float = Field(..., gt=0, description="Payment amount")


class CancelOrderRequest(BaseModel):
    """Cancel order request model"""
    debug_sid: str = Field(..., description="Session ID")
    order_id: str = Field(..., description="Order ID")
    reason: str = Field("", description="Cancellation reason")


class CouponDetailRequest(BaseModel):
    """Coupon detail request model"""
    debug_sid: str = Field(..., description="Session ID")
    coupon_id: str = Field(..., description="Coupon ID")
    coupon_code: str = Field(..., description="Coupon code")


class CouponProductsRequest(BaseModel):
    """Coupon products request model"""
    debug_sid: str = Field(..., description="Session ID")
    coupon_id: str = Field(..., description="Coupon ID")
    store_code: str = Field(..., description="Store code")


class CryptoTestRequest(BaseModel):
    """Crypto test request model"""
    data: str = Field(..., description="Data to encrypt/decrypt")
    operation: str = Field(..., description="Operation type (encrypt/decrypt)")


class CryptoTestResponse(BaseModel):
    """Crypto test response model"""
    result: str = Field(..., description="Operation result")
    operation: str = Field(..., description="Operation performed")
    success: bool = Field(..., description="Operation success status")
