"""
Cryptography utilities for the McDonald's API, including hashing, encryption, and signature generation.
"""
import base64
import binascii
import json
import random
import secrets
import time
import uuid
from hashlib import md5, sha1
from typing import Any, Dict, List, Optional

from Crypto.Cipher import AES
from loguru import logger

from config.settings import settings


class SignatureBuilder:
    """A builder class to construct the signature string in a modular way."""

    def __init__(self, headers: Dict[str, str]):
        self.headers = headers
        self.is_android = int(headers.get('ct', '102')) >= 101
        self.separator = '#' if self.is_android else '&'
        self.parts: List[str] = []

    def _add_part(self, part: Optional[str]):
        """Add a non-empty part to the signature components."""
        if part:
            self.parts.append(part)

    def add_headers(self) -> 'SignatureBuilder':
        """Build the headers component of the signature string."""
        header_fields = ["ct", "language", "ov", "p", "sid", "token", "v", "st", "nonce"]
        header_str = self.separator.join(
            f'{field}={self.headers[field]}'
            for field in sorted(header_fields)
            if field in self.headers and self.headers[field]
        )
        self._add_part(header_str)
        return self

    def add_post_body(self, post_body: Optional[Dict[str, Any]]) -> 'SignatureBuilder':
        """Build the post body component."""
        if post_body:
            body_str = json.dumps(post_body, separators=(',', ':'), ensure_ascii=False)
            self._add_part(body_str)
        return self

    def add_query_params(self, query_params: Optional[Dict[str, Any]]) -> 'SignatureBuilder':
        """Build the query parameters component."""
        if query_params:
            params_str = self.separator.join(
                f'{key}={value}'
                for key, value in sorted(query_params.items())
                if value != ''
            )
            self._add_part(params_str)
        return self

    def add_path(self, path: str) -> 'SignatureBuilder':
        """Add the request path component."""
        self._add_part(path)
        return self

    def add_key(self) -> 'SignatureBuilder':
        """Add the secret key component."""
        ct = int(self.headers.get('ct', '102'))
        if ct not in settings.mcd_api.sign_keys:
            logger.warning(f"Unknown ct value: {ct}, using default key for signing.")
            ct = 102
        key = settings.mcd_api.sign_keys[ct]
        self._add_part(f'key={key}')
        return self

    def build(self) -> str:
        """Combine all parts to produce the final raw string for signing."""
        # Android and Web platforms have different ordering of signature components.
        if not self.is_android:
            # Web order: post_body, query_params, path, headers, key
            # To reorder, we need to rebuild parts in the correct sequence.
            web_parts = []
            if any('{' in p for p in self.parts): # Heuristic for post_body
                web_parts.extend([p for p in self.parts if '{' in p])
            if any('=' in p and '{' not in p for p in self.parts): # Heuristic for params/headers
                web_parts.extend([p for p in self.parts if '=' in p and '{' not in p and 'key=' not in p])
            if any(p and '=' not in p for p in self.parts): # Heuristic for path
                web_parts.extend([p for p in self.parts if p and '=' not in p])
            web_parts.extend([p for p in self.parts if 'key=' in p])
            self.parts = web_parts

        return self.separator.join(self.parts)


class CryptoUtils:
    """Cryptography utilities for MCD API, providing hashing, encryption, and signing."""

    @staticmethod
    def make_md5(s: str, encoding: str = 'utf-8') -> str:
        """Create an MD5 hash from a string."""
        return md5(s.encode(encoding)).hexdigest()

    @staticmethod
    def generate_adaptive_sign(
        headers: Dict[str, Any],
        query_params: Optional[Dict[str, Any]],
        post_body: Optional[Dict[str, Any]],
        path: str = ''
    ) -> str:
        """
        Generate adaptive signature for McDonald's API requests.
        Exactly matches the original Python implementation.
        """
        # Key mapping based on ct value - exactly as in original
        keys = {
            10: '71d414c0-8fe1-495d-ac55-207414632479',
            11: '71d414c0-8fe1-495d-ac55-207414632479',
            20: '59b891d7-8d5a-4993-9b16-37d905a16967',
            21: 'ad5bac4a-2f99-4728-a7e5-818b5770b559',
            30: '3c0860ff-fabe-436a-ba47-94d42669591b',
            31: '71d414c0-8fe1-495d-ac55-207414632479',
            32: '71d414c0-8fe1-495d-ac55-207414632479',
            33: '71d414c0-8fe1-495d-ac55-207414632479',
            34: '71d414c0-8fe1-495d-ac55-207414632479',
            36: '71d414c0-8fe1-495d-ac55-207414632479',
            101: '97741bd7-21bb-4f0d-8b4a-501a089cd208',
            102: '97741bd7-21bb-4f0d-8b4a-501a089cd208',
            302: '71d414c0-8fe1-495d-ac55-207414632479'
        }

        # Determine if Android device based on ct value
        is_android_device = int(headers.get('ct', 102)) >= 101
        separator = '#' if is_android_device else '&'

        r = []

        # Build params string
        params_str = ''
        if query_params:
            for key in sorted(query_params.keys()):
                if str(query_params[key]) != '':
                    params_str += f'{key}={query_params[key]}{separator}'
        params_str = params_str[:-1] if params_str else ''

        # Build header string - exactly as in original
        h = ["ct", "language", "ov", "p", "sid", "token", "v", "st", "nonce"]
        header_str = ''
        for key in sorted(h):
            if key in headers and str(headers[key]) != '':
                header_str += f'{key}={headers[key]}{separator}'
        header_str = header_str[:-1] if header_str else ''

        # Build post body string
        post_body_str = ''
        if post_body:
            import json
            post_body_str = json.dumps(post_body, separators=(',', ':'), ensure_ascii=False)

        # Get key
        key = keys[int(headers.get('ct', 102))]
        key_str = f'key={key}'

        # Build signature components in correct order
        if is_android_device:
            if header_str:
                r.append(header_str)
            if post_body_str:
                r.append(post_body_str)
            if params_str:
                r.append(params_str)
            if path:
                r.append(path)
        else:
            if post_body_str:
                r.append(post_body_str)
            if params_str:
                r.append(params_str)
            if path:
                r.append(path)
            if header_str:
                r.append(header_str)

        if key_str:
            r.append(key_str)

        raw_string = separator.join(r)
        logger.debug(f"Generated sign raw string: {raw_string}")

        signature = CryptoUtils.make_md5(raw_string)
        logger.debug(f"Final signature: {signature}")
        return signature

    @staticmethod
    def aes_ecb_encrypt(plaintext: str, key: str = 'w8ZJ4wrUl7dDB1A7') -> str:
        """
        Encrypts text using AES in ECB mode. 
        
        NOTE: ECB is used for compatibility with the target API and is not generally
        recommended due to its security vulnerabilities. Use with caution.
        """
        BLOCK_SIZE = AES.block_size
        pad = lambda s: s + (BLOCK_SIZE - len(s) % BLOCK_SIZE) * chr(BLOCK_SIZE - len(s) % BLOCK_SIZE)
        
        cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
        encrypted_bytes = cipher.encrypt(pad(plaintext).encode('utf-8'))
        return binascii.hexlify(encrypted_bytes).decode('utf-8')

    @staticmethod
    def aes_ecb_decrypt(ciphertext_hex: str, key: str = 'w8ZJ4wrUl7dDB1A7') -> str:
        """
        Decrypts a hex-encoded string using AES in ECB mode.
        """
        unpad = lambda s: s[:-ord(s[-1:])]
        
        cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
        encrypted_bytes = binascii.unhexlify(ciphertext_hex)
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        return unpad(decrypted_bytes).decode('utf-8')

    @staticmethod
    def generate_random_imei() -> str:
        """Generate a random, Luhn-valid 15-digit IMEI."""
        prefix = ''.join(random.choices('0123456789', k=14))
        digits = [int(d) for d in prefix]
        checksum = 0
        for i, digit in enumerate(digits):
            if i % 2 == 0:
                checksum += digit
            else:
                checksum += (digit * 2) % 10 + (digit * 2) // 10
        return f"{prefix}{(10 - checksum % 10) % 10}"

    @staticmethod
    def generate_device_id() -> str:
        """Generate a random, base64-encoded device ID."""
        return base64.b64encode(secrets.token_bytes(48)).decode('utf-8')

    @staticmethod
    def generate_trace_id() -> str:
        """Generate a UUID4 trace ID for request tracking."""
        return str(uuid.uuid4())
