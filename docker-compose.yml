version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: mcd-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-mdl_server}
      MYSQL_USER: ${MYSQL_USER:-mdl_server}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-mdl_pass_2024}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./生产数据库备份.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - mcd-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: mcd-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mcd-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCD API服务器 (Python FastAPI)
  mcd-api-server:
    build: 
      context: ./mcd-api-server
      dockerfile: Dockerfile
    container_name: mcd-api-server
    restart: unless-stopped
    ports:
      - "9527:9527"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MCD_API_BASE_URL=https://api.mcd.cn
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - mcd-network
    volumes:
      - ./mcd-api-server/logs:/app/logs
      - ./mcd-api-server/main.py:/app/main.py
      - ./mcd-api-server/core:/app/core
      - ./mcd-api-server/models:/app/models
      - ./mcd-api-server/utils:/app/utils
      - ./mcd-api-server/config:/app/config

  # Node.js后端服务
  backend:
    build:
      context: ./后端
      dockerfile: Dockerfile
    container_name: mcd-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=${MYSQL_USER:-mdl_server}
      - DB_PASSWORD=${MYSQL_PASSWORD:-mdl_pass_2024}
      - DB_NAME=${MYSQL_DATABASE:-mdl_server}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MCD_USE_LOCAL_PROXY=true
      - MCD_LOCAL_PROXY_URL=http://mcd-api-server:9527
      - MCD_EXTERNAL_API_URL=http://server.pqkap.com
      - JWT_SECRET=${JWT_SECRET:-mdl_jwt_secret_2024_key_for_production}
      - BUSINESS_SIGN_KEY=${BUSINESS_SIGN_KEY:-dsjjnwkso23njn12jj5n6}
      - SESSION_SECRET=${SESSION_SECRET:-mdl_session_secret_2024}
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mcd-network
    volumes:
      - ./后端/logs:/app/logs

  # Admin管理后台
  admin:
    build:
      context: ./admin-backend-backup
      dockerfile: Dockerfile
    container_name: mcd-admin
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - mcd-network

  # 用户前端 (静态文件部署)
  user-frontend:
    build:
      context: ./用户前端
      dockerfile: Dockerfile.static
    container_name: mcd-user-frontend
    restart: unless-stopped
    ports:
      - "8081:80"
    depends_on:
      - backend
    networks:
      - mcd-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  mcd-network:
    driver: bridge
